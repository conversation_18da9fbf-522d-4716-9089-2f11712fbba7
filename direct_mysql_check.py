#!/usr/bin/env python3
"""
直接MySQL连接检查索引
尝试多种连接方式
"""

import mysql.connector
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

def try_mysql_connection():
    """尝试多种方式连接MySQL"""
    print("🔍 尝试连接MySQL服务器...")
    
    # 配置选项
    configs = [
        {
            'name': '远程服务器 (*************)',
            'config': {
                'host': '*************',
                'user': 'Seller',
                'password': '98c06z27W@',
                'database': 'kkuguan_db',
                'port': 3306,
                'connection_timeout': 10,
                'charset': 'utf8mb4'
            }
        },
        {
            'name': '本地服务器 (127.0.0.1)',
            'config': {
                'host': '127.0.0.1',
                'user': 'Seller',
                'password': '98c06z27W@',
                'database': 'kkuguan_db',
                'port': 3306,
                'connection_timeout': 10,
                'charset': 'utf8mb4'
            }
        },
        {
            'name': '本地root用户',
            'config': {
                'host': '127.0.0.1',
                'user': 'root',
                'password': '',
                'database': 'kkuguan_db',
                'port': 3306,
                'connection_timeout': 10,
                'charset': 'utf8mb4'
            }
        },
        {
            'name': '本地root用户 (常见密码)',
            'config': {
                'host': '127.0.0.1',
                'user': 'root',
                'password': 'root',
                'database': 'kkuguan_db',
                'port': 3306,
                'connection_timeout': 10,
                'charset': 'utf8mb4'
            }
        }
    ]
    
    for config_info in configs:
        print(f"\n🔗 尝试连接: {config_info['name']}")
        try:
            connection = mysql.connector.connect(**config_info['config'])
            if connection.is_connected():
                print(f"✅ 连接成功: {config_info['name']}")
                
                # 获取基本信息
                cursor = connection.cursor(dictionary=True)
                
                # 检查当前用户和数据库
                cursor.execute("SELECT USER() as user, DATABASE() as db, VERSION() as version")
                info = cursor.fetchone()
                print(f"👤 当前用户: {info['user']}")
                print(f"🗄️  当前数据库: {info['db']}")
                print(f"📊 MySQL版本: {info['version']}")
                
                # 检查表
                cursor.execute("SHOW TABLES")
                tables = cursor.fetchall()
                table_names = [list(t.values())[0] for t in tables]
                print(f"📋 数据库表 ({len(table_names)}个): {', '.join(table_names[:5])}{'...' if len(table_names) > 5 else ''}")
                
                # 检查索引
                check_indexes(cursor, table_names)
                
                cursor.close()
                connection.close()
                return True
                
        except mysql.connector.Error as e:
            print(f"❌ 连接失败: {e}")
        except Exception as e:
            print(f"❌ 其他错误: {e}")
    
    print("\n❌ 所有连接尝试都失败了")
    return False

def check_indexes(cursor, table_names):
    """检查表索引"""
    print(f"\n🔍 检查表索引...")
    
    main_tables = ['products', 'inventory', 'orders', 'tracking']
    
    for table in main_tables:
        if table in table_names:
            print(f"\n📊 {table} 表的索引:")
            try:
                cursor.execute(f"SHOW INDEX FROM {table}")
                indexes = cursor.fetchall()
                
                if indexes:
                    # 按索引名分组
                    index_groups = {}
                    for idx in indexes:
                        key_name = idx.get('Key_name', 'N/A')
                        column_name = idx.get('Column_name', 'N/A')
                        is_unique = idx.get('Non_unique', 1) == 0
                        cardinality = idx.get('Cardinality', 0)
                        
                        if key_name not in index_groups:
                            index_groups[key_name] = {
                                'columns': [],
                                'unique': is_unique,
                                'cardinality': cardinality
                            }
                        index_groups[key_name]['columns'].append(column_name)
                    
                    for index_name, info in index_groups.items():
                        unique_str = '(唯一)' if info['unique'] else '(非唯一)'
                        columns_str = ', '.join(info['columns'])
                        cardinality = info['cardinality']
                        print(f"   📌 {index_name}: {columns_str} {unique_str} [基数: {cardinality}]")
                else:
                    print("   ⚠️  没有找到索引")
                    
            except mysql.connector.Error as e:
                print(f"   ❌ 查询索引失败: {e}")
        else:
            print(f"\n⚠️  表 {table} 不存在")

def suggest_indexes():
    """建议索引优化"""
    print(f"\n💡 索引优化建议:")
    print("基于KKUGUAN项目的查询模式，建议添加以下索引:")
    print()
    
    suggestions = [
        ("products表", [
            "ALTER TABLE products ADD INDEX idx_sku (sku);",
            "ALTER TABLE products ADD INDEX idx_supplier_code (supplier_code);",
            "ALTER TABLE products ADD INDEX idx_sku_supplier (sku, supplier_code);"
        ]),
        ("inventory表", [
            "ALTER TABLE inventory ADD INDEX idx_sku (sku);",
            "ALTER TABLE inventory ADD INDEX idx_date (date);",
            "ALTER TABLE inventory ADD INDEX idx_sku_date (sku, date DESC);"
        ]),
        ("orders表", [
            "ALTER TABLE orders ADD INDEX idx_sku (sku);",
            "ALTER TABLE orders ADD INDEX idx_order_date (order_date);",
            "ALTER TABLE orders ADD INDEX idx_sku_order_date (sku, order_date DESC);",
            "ALTER TABLE orders ADD INDEX idx_platform (platform);"
        ]),
        ("tracking表", [
            "ALTER TABLE tracking ADD INDEX idx_tracking_number (tracking_number);",
            "ALTER TABLE tracking ADD INDEX idx_status (status);",
            "ALTER TABLE tracking ADD INDEX idx_platform_shop (platform, shop);"
        ])
    ]
    
    for table_name, sqls in suggestions:
        print(f"📋 {table_name}:")
        for sql in sqls:
            print(f"   {sql}")
        print()

def main():
    """主函数"""
    print("🚀 MySQL索引检查工具")
    print("=" * 60)
    
    if try_mysql_connection():
        print("\n" + "=" * 60)
        print("✅ 索引检查完成")
    else:
        print("\n" + "=" * 60)
        print("❌ 无法连接到MySQL服务器")
        print("\n💡 可能的原因:")
        print("1. MySQL服务器未启动")
        print("2. 网络连接问题")
        print("3. 用户权限问题")
        print("4. 防火墙阻止连接")
    
    suggest_indexes()

if __name__ == '__main__':
    main()
