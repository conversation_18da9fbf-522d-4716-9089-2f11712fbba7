#!/usr/bin/env python3
"""
KKUGUAN 快速启动脚本
跳过所有预热和数据库表结构更新，直接启动Flask应用
"""

import os
import sys

# 设置环境变量，跳过所有耗时操作
os.environ['SKIP_WARMUP'] = 'true'
os.environ['SKIP_DB_UPDATE'] = 'true'
os.environ['QUICK_START'] = 'true'

# 设置端口（可以通过命令行参数修改）
if len(sys.argv) > 1:
    try:
        port = int(sys.argv[1])
        os.environ['PORT'] = str(port)
        print(f"🚀 使用指定端口: {port}")
    except ValueError:
        print("❌ 端口号必须是数字")
        sys.exit(1)
else:
    os.environ['PORT'] = '5003'

print("🚀 KKUGUAN 快速启动模式")
print("🚫 跳过预热和数据库表结构更新")
print("⚡ 快速启动中...")

# 导入并启动应用
if __name__ == '__main__':
    from app import app
    
    port = int(os.getenv('PORT', 5003))
    host = os.getenv('HOST', '0.0.0.0')
    
    print(f"🌐 启动地址: http://{host}:{port}")
    print("🔧 调试模式: 开启")
    print("✅ 应用就绪！")
    
    app.run(host=host, port=port, debug=True, threaded=True)
