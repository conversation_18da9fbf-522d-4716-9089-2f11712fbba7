from flask import Flask, render_template, jsonify, request, session, redirect, url_for, flash, send_from_directory, Response
import time
import logging
import pandas as pd
import threading
from functools import wraps
from datetime import datetime, timedelta
import hashlib
import json
import traceback
import sys

import os
import requests
import asyncio
import openpyxl
from dotenv import load_dotenv
from utils.image_optimizer import ImageOptimizer
from database_config import (
    execute_query, get_sku_detail, get_sku_list, get_sales_rank,
    get_inventory_alerts, get_restock_calendar, get_dashboard_stats, DatabaseConfig,
    # 物流追踪相关函数
    get_tracking_data, get_tracking_by_number, insert_or_update_tracking,
    update_tracking_remark, get_tracking_dashboard_summary, batch_insert_tracking_from_excel
)

# Redis缓存配置
try:
    import redis
    import pickle
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False

# 加载环境变量
load_dotenv()




# 配置Flask应用
app = Flask(__name__)
app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', 'kkuguan-secret-key-2024')

# 性能优化配置
app.config['COMPRESS_MIMETYPES'] = [
    'text/html', 'text/css', 'text/xml', 'application/json',
    'application/javascript', 'text/javascript'
]
app.config['COMPRESS_LEVEL'] = 6  # 压缩级别
app.config['COMPRESS_MIN_SIZE'] = 500  # 最小压缩大小

# 配置日志
log_level = os.getenv('LOG_LEVEL', 'INFO').upper()

# 创建日志目录
log_file = os.getenv('LOG_FILE', 'app.log')
log_dir = os.path.dirname(log_file)
if log_dir and not os.path.exists(log_dir):
    os.makedirs(log_dir, exist_ok=True)

logging.basicConfig(
    level=getattr(logging, log_level),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler(log_file, encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

# 记录Redis可用性
if REDIS_AVAILABLE:
    logger.info("✅ Redis模块可用")
else:
    logger.warning("⚠️ Redis模块不可用，将使用内存缓存")

# 17track API配置
TRACK_API_KEY = os.getenv('TRACK_API_KEY') or os.getenv('API_KEY')
TRACK_REGISTER_URL = 'https://api.17track.net/track/v2.2/register'
TRACK_GET_INFO_URL = 'https://api.17track.net/track/v2.2/gettrackinfo'

# 物流追踪内存存储（用于临时缓存）
tracking_data_cache = {}

# 全局错误处理装饰器
def handle_errors(f):
    """全局错误处理装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        try:
            return f(*args, **kwargs)
        except Exception as e:
            error_id = hashlib.md5(f"{time.time()}{str(e)}".encode()).hexdigest()[:8]
            logger.error(f"错误ID: {error_id} - 函数: {f.__name__} - 错误: {str(e)}")
            logger.error(f"错误详情: {traceback.format_exc()}")

            # 返回统一的错误响应
            if request.path.startswith('/api/'):
                return jsonify({
                    'error': f'服务器内部错误 (错误ID: {error_id})',
                    'error_id': error_id,
                    'message': '请联系管理员或稍后重试'
                }), 500
            else:
                flash(f'系统错误 (错误ID: {error_id})，请稍后重试', 'error')
                return redirect(url_for('dashboard'))
    return decorated_function

# 尝试启用压缩（如果flask-compress可用）
try:
    from flask_compress import Compress
    Compress(app)
    logger.info("✅ Flask压缩已启用")
except ImportError:
    # 使用内置的gzip压缩替代
    from flask import Response
    import gzip
    import io

    @app.after_request
    def compress_response(response):
        """简单的响应压缩"""
        if (response.status_code == 200 and
            'gzip' in request.headers.get('Accept-Encoding', '') and
            response.content_length and response.content_length > 500):
            try:
                gzip_buffer = io.BytesIO()
                with gzip.GzipFile(fileobj=gzip_buffer, mode='wb') as gzip_file:
                    gzip_file.write(response.get_data())

                response.set_data(gzip_buffer.getvalue())
                response.headers['Content-Encoding'] = 'gzip'
                response.headers['Content-Length'] = len(response.get_data())
            except:
                pass  # 压缩失败时使用原始响应
        return response

    logger.info("✅ 使用内置压缩替代flask-compress")

# API响应优化装饰器
def optimize_api_response(f):
    """API响应优化装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        start_time = time.time()

        # 执行原函数
        result = f(*args, **kwargs)

        # 添加性能头信息
        if isinstance(result, dict):
            response = jsonify(result)
        else:
            response = result

        # 添加缓存控制头
        response.headers['Cache-Control'] = 'public, max-age=300'
        response.headers['X-Response-Time'] = f"{(time.time() - start_time) * 1000:.2f}ms"

        return response
    return decorated_function

# 分页辅助函数
def paginate_results(data, page=1, per_page=50):
    """分页处理"""
    if not isinstance(data, list):
        return data

    total = len(data)
    start = (page - 1) * per_page
    end = start + per_page

    return {
        'data': data[start:end],
        'pagination': {
            'page': page,
            'per_page': per_page,
            'total': total,
            'pages': (total + per_page - 1) // per_page
        }
    }

# 飞书API集成（移到logger初始化之后）
try:
    from feishu_config import setup_feishu, FEISHU_CACHE_CONFIG
    from feishu_api import get_sku_shop_mapping, get_platform_store_mapping
    FEISHU_ENABLED = setup_feishu()
except ImportError as e:
    logger.warning(f"飞书API模块导入失败: {e}")
    FEISHU_ENABLED = False
except Exception as e:
    logger.warning(f"飞书API初始化失败: {e}")
    FEISHU_ENABLED = False

# 应用启动时进行初始化检查
def initialize_application():
    """应用初始化检查"""
    try:
        logger.info("🚀 启动KKUGUAN库存管理系统 - 优化版 (混合缓存架构)")

        # 检查数据库连接
        from database_config import DatabaseConfig
        db_config = DatabaseConfig()

        # 更新数据库表结构（可选，用于快速启动）
        skip_db_update = os.getenv('SKIP_DB_UPDATE', 'true').lower() == 'true'
        if skip_db_update:
            logger.info("🚫 跳过数据库表结构更新 - 快速启动模式")
        else:
            logger.info("📝 检查并更新数据库表结构...")
            try:
                db_config.update_table_structure()
                logger.info("✅ 数据库表结构检查完成")
            except Exception as e:
                logger.warning(f"⚠️ 数据库表结构更新失败: {e}")

        # 检查飞书API
        if FEISHU_ENABLED:
            logger.info("✅ 飞书API集成已启用 - 支持店铺SKU映射")
        else:
            logger.info("⚠️ 飞书API集成未启用 - 请配置后重启以启用店铺SKU功能")

        return True

    except Exception as e:
        logger.error(f"❌ 应用初始化失败: {e}")
        return False

# 执行初始化
if not initialize_application():
    logger.error("❌ 应用初始化失败，请检查配置")
    # 在生产环境中可以选择退出
    # sys.exit(1)

# ==================== Redis缓存管理器 ====================
class RedisCacheManager:
    """
    Redis缓存管理器 - 支持持久化缓存和高性能
    """
    def __init__(self):
        self.redis_client = None
        self.fallback_cache = {}  # 内存备用缓存
        self.cache_stats = {
            'hits': 0,
            'misses': 0,
            'redis_hits': 0,
            'fallback_hits': 0,
            'errors': 0
        }

        # 缓存策略配置 - 优化版本
        self.cache_strategies = {
            # 高频访问数据 - 较短TTL，确保数据新鲜度
            'sku_detail': {'ttl': 1800, 'refresh_threshold': 0.8},      # 30分钟，24分钟后预刷新
            'sales_stock': {'ttl': 1800, 'refresh_threshold': 0.8},     # 30分钟，24分钟后预刷新
            'dashboard_stats': {'ttl': 1800, 'refresh_threshold': 0.7}, # 30分钟，21分钟后预刷新
            'inventory_alerts': {'ttl': 2700, 'refresh_threshold': 0.8}, # 45分钟，36分钟后预刷新

            # 中频访问数据 - 中等TTL
            'dashboard_alerts': {'ttl': 3600, 'refresh_threshold': 0.8}, # 1小时，48分钟后预刷新

            # 低频访问数据 - 较长TTL
            'recommendations': {'ttl': 7200, 'refresh_threshold': 0.9}, # 2小时，108分钟后预刷新
            'restock_calendar': {'ttl': 14400, 'refresh_threshold': 0.9}, # 4小时，216分钟后预刷新

            # 默认策略
            'default': {'ttl': 3600, 'refresh_threshold': 0.8}          # 默认1小时，48分钟后预刷新
        }

        # 自动刷新配置
        self.auto_refresh_enabled = True
        self.refresh_queue = []  # 待刷新的缓存队列
        self.last_cleanup_time = time.time()
        self.cleanup_interval = 300  # 5分钟清理一次过期缓存

        # 初始化Redis连接
        self._init_redis()

    def _init_redis(self):
        """初始化Redis连接"""
        if not REDIS_AVAILABLE:
            logger.warning("⚠️ Redis不可用，使用内存缓存")
            return

        try:
            # Redis配置
            redis_host = os.getenv('REDIS_HOST', 'localhost')
            redis_port = int(os.getenv('REDIS_PORT', 6379))
            redis_db = int(os.getenv('REDIS_DB', 0))
            redis_password = os.getenv('REDIS_PASSWORD', None)

            self.redis_client = redis.Redis(
                host=redis_host,
                port=redis_port,
                db=redis_db,
                password=redis_password,
                decode_responses=False,  # 保持二进制数据
                socket_timeout=5,
                socket_connect_timeout=5,
                health_check_interval=30
            )

            # 测试连接
            self.redis_client.ping()
            logger.info(f"✅ Redis连接成功: {redis_host}:{redis_port}")

        except Exception as e:
            logger.warning(f"⚠️ Redis连接失败: {e}，使用内存缓存")
            self.redis_client = None

    def _get_cache_strategy(self, cache_key):
        """获取缓存策略"""
        for strategy_key, strategy in self.cache_strategies.items():
            if cache_key.startswith(strategy_key):
                return strategy
        return self.cache_strategies['default']

    def _serialize_data(self, data):
        """序列化数据"""
        try:
            return pickle.dumps(data)
        except Exception as e:
            logger.error(f"数据序列化失败: {e}")
            return None

    def _deserialize_data(self, data):
        """反序列化数据"""
        try:
            return pickle.loads(data)
        except Exception as e:
            logger.error(f"数据反序列化失败: {e}")
            return None

    def validate_cache_data(self, key, data):
        """
        验证缓存数据的有效性
        """
        if data is None:
            return False, "数据为None"

        # 基本类型检查
        if key == 'dashboard_alerts':
            if not isinstance(data, list):
                return False, "仪表板提醒数据应该是列表格式"

            if len(data) > 0:
                # 检查第一个元素的结构
                sample_item = data[0]
                required_fields = ['SKU', 'estimated_days', '总库存', '近30天销量']
                missing_fields = [field for field in required_fields if field not in sample_item]
                if missing_fields:
                    return False, f"缺少必要字段: {missing_fields}"

                # 检查数据合理性
                if not isinstance(sample_item.get('estimated_days'), (int, float)):
                    return False, "estimated_days字段应该是数字"

                if sample_item.get('estimated_days', 0) < 0:
                    return False, "estimated_days不能为负数"

        elif key == 'sku_list_all':
            if not isinstance(data, list):
                return False, "SKU列表应该是列表格式"

        elif key == 'dashboard_stats':
            if not isinstance(data, dict):
                return False, "仪表板统计应该是字典格式"

            required_stats = ['total_skus', 'pending_restock', 'alerts_count']
            missing_stats = [stat for stat in required_stats if stat not in data]
            if missing_stats:
                return False, f"缺少必要统计字段: {missing_stats}"

        return True, "数据验证通过"

    def should_refresh_cache(self, cache_key, timestamp):
        """
        检查缓存是否需要预刷新
        """
        if not self.auto_refresh_enabled:
            return False

        strategy = self._get_cache_strategy(cache_key)
        ttl = strategy['ttl']
        refresh_threshold = strategy.get('refresh_threshold', 0.8)

        # 计算缓存年龄
        cache_age = time.time() - timestamp
        refresh_time = ttl * refresh_threshold

        return cache_age >= refresh_time

    def schedule_cache_refresh(self, cache_key, fallback_func, *args, **kwargs):
        """
        安排缓存刷新
        """
        refresh_item = {
            'cache_key': cache_key,
            'fallback_func': fallback_func,
            'args': args,
            'kwargs': kwargs,
            'scheduled_time': time.time()
        }

        # 避免重复安排
        existing_keys = [item['cache_key'] for item in self.refresh_queue]
        if cache_key not in existing_keys:
            self.refresh_queue.append(refresh_item)
            logger.debug(f"📅 已安排缓存刷新: {cache_key}")

    def process_refresh_queue(self):
        """
        处理缓存刷新队列
        """
        if not self.refresh_queue:
            return

        # 处理队列中的刷新任务
        processed_items = []
        for item in self.refresh_queue[:3]:  # 每次最多处理3个
            try:
                cache_key = item['cache_key']
                fallback_func = item['fallback_func']
                args = item['args']
                kwargs = item['kwargs']

                logger.info(f"🔄 后台刷新缓存: {cache_key}")

                # 执行刷新
                if args and kwargs:
                    data = fallback_func(*args, **kwargs)
                elif args:
                    data = fallback_func(*args)
                elif kwargs:
                    data = fallback_func(**kwargs)
                else:
                    data = fallback_func()

                # 验证并存储
                is_valid, validation_msg = self.validate_cache_data(cache_key, data)
                if is_valid:
                    strategy = self._get_cache_strategy(cache_key)
                    self._set_cache(cache_key, data, strategy['ttl'])
                    logger.info(f"✅ 缓存后台刷新成功: {cache_key}")
                else:
                    logger.warning(f"⚠️ 缓存刷新数据验证失败: {cache_key} - {validation_msg}")

                processed_items.append(item)

            except Exception as e:
                logger.error(f"❌ 缓存刷新失败: {item['cache_key']} - {e}")
                processed_items.append(item)

        # 移除已处理的项目
        for item in processed_items:
            self.refresh_queue.remove(item)

    def cleanup_expired_cache(self):
        """
        清理过期缓存
        """
        current_time = time.time()

        # 检查是否需要清理
        if current_time - self.last_cleanup_time < self.cleanup_interval:
            return

        logger.debug("🧹 开始清理过期缓存...")

        # 清理内存缓存
        expired_keys = []
        for cache_key, cache_entry in self.fallback_cache.items():
            strategy = self._get_cache_strategy(cache_key)
            if current_time - cache_entry['timestamp'] > strategy['ttl']:
                expired_keys.append(cache_key)

        for key in expired_keys:
            del self.fallback_cache[key]
            logger.debug(f"🗑️ 清理过期内存缓存: {key}")

        if expired_keys:
            logger.info(f"🧹 清理了 {len(expired_keys)} 个过期内存缓存")

        self.last_cleanup_time = current_time

    def get_cached_data(self, cache_key, fallback_func, *args, **kwargs):
        """获取缓存数据"""
        start_time = time.time()
        strategy = self._get_cache_strategy(cache_key)

        # 1. 尝试从Redis获取
        if self.redis_client:
            try:
                cached_data = self.redis_client.get(f"kkuguan:{cache_key}")
                if cached_data:
                    data = self._deserialize_data(cached_data)
                    if data is not None:
                        # 验证缓存数据
                        is_valid, validation_msg = self.validate_cache_data(cache_key, data)
                        if is_valid:
                            self.cache_stats['redis_hits'] += 1
                            self.cache_stats['hits'] += 1
                            response_time = (time.time() - start_time) * 1000
                            logger.info(f"🔴 Redis缓存命中且验证通过: {cache_key} ({response_time:.1f}ms)")

                            # 检查是否需要预刷新
                            cache_timestamp = self.redis_client.hget(f"kkuguan:{cache_key}:meta", "timestamp")
                            if cache_timestamp:
                                timestamp = float(cache_timestamp)
                                if self.should_refresh_cache(cache_key, timestamp):
                                    self.schedule_cache_refresh(cache_key, fallback_func, *args, **kwargs)

                            return data
                        else:
                            logger.warning(f"🔴 Redis缓存数据验证失败: {cache_key} - {validation_msg}")
                            # 删除无效缓存
                            self.redis_client.delete(f"kkuguan:{cache_key}")
                            self.redis_client.delete(f"kkuguan:{cache_key}:meta")
            except Exception as e:
                logger.warning(f"Redis读取失败: {e}")
                self.cache_stats['errors'] += 1

        # 2. 尝试从内存备用缓存获取
        if cache_key in self.fallback_cache:
            cache_entry = self.fallback_cache[cache_key]
            if time.time() - cache_entry['timestamp'] < strategy['ttl']:
                data = cache_entry['data']

                # 验证缓存数据
                is_valid, validation_msg = self.validate_cache_data(cache_key, data)
                if is_valid:
                    self.cache_stats['fallback_hits'] += 1
                    self.cache_stats['hits'] += 1
                    response_time = (time.time() - start_time) * 1000
                    logger.info(f"💾 内存缓存命中且验证通过: {cache_key} ({response_time:.1f}ms)")
                    return data
                else:
                    logger.warning(f"💾 内存缓存数据验证失败: {cache_key} - {validation_msg}")
                    # 删除无效缓存
                    del self.fallback_cache[cache_key]

        # 3. 缓存未命中，获取新数据
        self.cache_stats['misses'] += 1
        logger.info(f"🔄 缓存未命中，获取新数据: {cache_key}")

        try:
            # 执行回调函数获取数据
            if args and kwargs:
                data = fallback_func(*args, **kwargs)
            elif args:
                data = fallback_func(*args)
            elif kwargs:
                data = fallback_func(**kwargs)
            else:
                data = fallback_func()

            # 验证新生成的数据
            is_valid, validation_msg = self.validate_cache_data(cache_key, data)
            if is_valid:
                # 存储到缓存
                self._set_cache(cache_key, data, strategy['ttl'])
                logger.info(f"✅ 数据验证通过并已缓存: {cache_key}")
            else:
                logger.warning(f"⚠️ 新生成的数据验证失败，不缓存: {cache_key} - {validation_msg}")

            response_time = (time.time() - start_time) * 1000
            logger.info(f"✅ 数据获取完成: {cache_key} ({response_time:.1f}ms)")
            return data

        except Exception as e:
            logger.error(f"获取数据失败: {cache_key}, 错误: {e}")
            raise e

    def _set_cache(self, cache_key, data, ttl):
        """设置缓存（增强版本）"""
        current_time = time.time()

        # 存储到Redis
        if self.redis_client:
            try:
                serialized_data = self._serialize_data(data)
                if serialized_data:
                    # 存储数据
                    self.redis_client.setex(f"kkuguan:{cache_key}", ttl, serialized_data)
                    # 存储元数据（时间戳）
                    self.redis_client.hset(f"kkuguan:{cache_key}:meta", "timestamp", current_time)
                    self.redis_client.expire(f"kkuguan:{cache_key}:meta", ttl)
            except Exception as e:
                logger.warning(f"Redis存储失败: {e}")
                self.cache_stats['errors'] += 1

        # 存储到内存备用缓存
        self.fallback_cache[cache_key] = {
            'data': data,
            'timestamp': current_time
        }

    def set_cached_data(self, cache_key, data, ttl=None):
        """
        设置缓存数据（带验证）
        """
        # 验证数据
        is_valid, validation_msg = self.validate_cache_data(cache_key, data)
        if not is_valid:
            logger.error(f"❌ 数据验证失败，不缓存: {cache_key} - {validation_msg}")
            return False

        # 获取TTL
        if ttl is None:
            strategy = self._get_cache_strategy(cache_key)
            ttl = strategy['ttl']

        # 存储到缓存
        self._set_cache(cache_key, data, ttl)
        logger.info(f"✅ 数据验证通过并已缓存: {cache_key}, TTL={ttl}秒")
        return True

    def invalidate_cache(self, cache_key=None):
        """清除缓存"""
        if cache_key:
            # 清除特定缓存
            if self.redis_client:
                try:
                    self.redis_client.delete(f"kkuguan:{cache_key}")
                except Exception as e:
                    logger.warning(f"Redis删除失败: {e}")

            if cache_key in self.fallback_cache:
                del self.fallback_cache[cache_key]

            logger.info(f"🗑️ 缓存已清除: {cache_key}")
        else:
            # 清除所有缓存
            if self.redis_client:
                try:
                    keys = self.redis_client.keys("kkuguan:*")
                    if keys:
                        self.redis_client.delete(*keys)
                except Exception as e:
                    logger.warning(f"Redis批量删除失败: {e}")

            self.fallback_cache.clear()
            logger.info("🗑️ 所有缓存已清除")

    def background_maintenance(self):
        """
        后台维护任务
        """
        try:
            # 处理缓存刷新队列
            self.process_refresh_queue()

            # 清理过期缓存
            self.cleanup_expired_cache()

        except Exception as e:
            logger.error(f"❌ 缓存后台维护失败: {e}")

    def get_cache_stats(self):
        """获取缓存统计"""
        total_requests = self.cache_stats['hits'] + self.cache_stats['misses']
        hit_rate = (self.cache_stats['hits'] / total_requests * 100) if total_requests > 0 else 0

        stats = {
            'hit_rate': f"{hit_rate:.1f}%",
            'total_requests': total_requests,
            'redis_available': self.redis_client is not None,
            'fallback_cache_size': len(self.fallback_cache),
            'stats': self.cache_stats.copy()
        }

        # 获取Redis信息
        if self.redis_client:
            try:
                redis_info = self.redis_client.info('memory')
                stats['redis_memory'] = redis_info.get('used_memory_human', 'N/A')
                stats['redis_keys'] = len(self.redis_client.keys("kkuguan:*"))
            except:
                stats['redis_memory'] = 'N/A'
                stats['redis_keys'] = 0

        return stats

# ==================== 简化缓存管理器 ====================
class EnhancedCacheManager:
    """
    增强缓存管理器 - 支持差异化TTL和双缓存机制
    - 差异化TTL策略: 不同页面不同缓存时间
    - 双缓存机制: 主缓存+备用缓存，支持异步更新
    - 流畅体验: 主缓存过期时返回备用缓存，异步更新
    """
    def __init__(self):
        self.cache = {}
        self.backup_cache = {}
        self.cache_stats = {
            'hits': 0,
            'misses': 0,
            'updates': 0,
            'backup_hits': 0,
            'async_updates': 0
        }

        # 差异化TTL策略
        self.cache_strategies = {
            # SKU详情页面 - 30分钟主缓存，2小时备用缓存
            'sku_detail': {'ttl': 1800, 'backup_ttl': 7200},
            'sales_stock': {'ttl': 1800, 'backup_ttl': 7200},

            # 选品推荐页面 - 2小时主缓存，10小时备用缓存
            'recommendations': {'ttl': 7200, 'backup_ttl': 36000},

            # 补货周期页面 - 4小时主缓存，20小时备用缓存
            'restock_calendar': {'ttl': 14400, 'backup_ttl': 72000},
            'enhanced_restock_calendar': {'ttl': 14400, 'backup_ttl': 72000},

            # 库存提醒页面 - 30分钟主缓存，2小时备用缓存
            'inventory_alerts': {'ttl': 1800, 'backup_ttl': 7200},

            # 仪表盘库存提醒 - 1小时主缓存，2小时备用缓存
            'dashboard_alerts': {'ttl': 3600, 'backup_ttl': 7200},

            # 仪表盘统计数据 - 30分钟主缓存，2小时备用缓存
            'dashboard_stats': {'ttl': 1800, 'backup_ttl': 7200},

            # 其他页面 - 30分钟主缓存，2小时备用缓存
            'default': {'ttl': 1800, 'backup_ttl': 7200}
        }

        self.default_ttl = 1800  # 30分钟默认TTL
        self.default_backup_ttl = 7200  # 2小时默认备用TTL

        logger.info("🔥 增强缓存管理器已初始化 - 支持差异化TTL和异步更新")



    def _get_cache_strategy(self, cache_key):
        """获取缓存策略"""
        for strategy_key, strategy in self.cache_strategies.items():
            if cache_key.startswith(strategy_key):
                return strategy
        return self.cache_strategies['default']

    def get_cached_data(self, cache_key, fallback_func, *args, **kwargs):
        """
        获取缓存数据 - 支持异步更新的双缓存机制
        """
        start_time = time.time()
        strategy = self._get_cache_strategy(cache_key)

        # 1. 检查主缓存是否存在且未过期
        if cache_key in self.cache:
            cache_entry = self.cache[cache_key]
            if time.time() - cache_entry['timestamp'] < strategy['ttl']:
                self.cache_stats['hits'] += 1
                response_time = (time.time() - start_time) * 1000
                logger.info(f"📦 主缓存命中: {cache_key} ({response_time:.1f}ms)")
                return cache_entry['data']

        # 2. 主缓存过期，检查备用缓存
        if cache_key in self.backup_cache:
            backup_entry = self.backup_cache[cache_key]
            if time.time() - backup_entry['timestamp'] < strategy['backup_ttl']:
                self.cache_stats['backup_hits'] += 1
                response_time = (time.time() - start_time) * 1000
                logger.info(f"� 备用缓存命中，异步更新: {cache_key} ({response_time:.1f}ms)")

                # 异步更新主缓存
                self._async_update_cache(cache_key, fallback_func, args, kwargs)

                return backup_entry['data']

        # 3. 主缓存和备用缓存都过期或不存在，同步获取数据
        self.cache_stats['misses'] += 1
        logger.info(f"🔄 缓存完全缺失，同步获取: {cache_key}")

        try:
            # 调用原始函数
            data = self._execute_fallback(fallback_func, args, kwargs)

            # 同时更新主缓存和备用缓存
            current_time = time.time()
            cache_entry = {
                'data': data,
                'timestamp': current_time
            }
            self.cache[cache_key] = cache_entry
            self.backup_cache[cache_key] = cache_entry.copy()
            self.cache_stats['updates'] += 1

            response_time = (time.time() - start_time) * 1000
            logger.info(f"🔄 缓存已创建: {cache_key} ({response_time:.1f}ms)")

            return data

        except Exception as e:
            logger.error(f"❌ 获取数据失败: {cache_key}, 错误: {e}")
            raise e

    def _execute_fallback(self, fallback_func, args, kwargs):
        """执行回调函数"""
        if args and kwargs:
            return fallback_func(*args, **kwargs)
        elif args:
            return fallback_func(*args)
        elif kwargs:
            return fallback_func(**kwargs)
        else:
            return fallback_func()

    def _async_update_cache(self, cache_key, fallback_func, args, kwargs):
        """异步更新缓存"""
        def async_update():
            try:
                data = self._execute_fallback(fallback_func, args, kwargs)

                current_time = time.time()
                cache_entry = {
                    'data': data,
                    'timestamp': current_time
                }
                self.cache[cache_key] = cache_entry
                self.backup_cache[cache_key] = cache_entry.copy()

                self.cache_stats['async_updates'] += 1
                logger.info(f"🔄 异步更新完成: {cache_key}")
            except Exception as e:
                logger.error(f"❌ 异步更新失败: {cache_key}, 错误: {e}")

        # 启动异步更新线程
        import threading
        threading.Thread(target=async_update, daemon=True).start()

    def invalidate_cache(self, cache_key=None):
        """清除缓存"""
        if cache_key:
            if cache_key in self.cache:
                del self.cache[cache_key]
            if cache_key in self.backup_cache:
                del self.backup_cache[cache_key]
            logger.info(f"🗑️ 缓存已清除: {cache_key}")
        else:
            self.cache.clear()
            self.backup_cache.clear()
            logger.info("🗑️ 所有缓存已清除")

    def get_cache_stats(self):
        """获取缓存统计"""
        total_requests = self.cache_stats['hits'] + self.cache_stats['misses'] + self.cache_stats['backup_hits']
        hit_rate = ((self.cache_stats['hits'] + self.cache_stats['backup_hits']) / total_requests * 100) if total_requests > 0 else 0

        return {
            'hit_rate': f"{hit_rate:.1f}%",
            'total_requests': total_requests,
            'cache_size': len(self.cache),
            'backup_cache_size': len(self.backup_cache),
            'async_updates': self.cache_stats['async_updates'],
            'stats': self.cache_stats.copy()
        }

    def _get_cache_detail(self, cache_key):
        """获取特定缓存键的详细状态"""
        current_time = time.time()
        strategy = self._get_cache_strategy(cache_key)

        result = {
            'cache_key': cache_key,
            'strategy': strategy,
            'main_cache': {
                'exists': cache_key in self.cache,
                'expired': False,
                'age_seconds': 0,
                'remaining_seconds': 0
            },
            'backup_cache': {
                'exists': cache_key in self.backup_cache,
                'expired': False,
                'age_seconds': 0,
                'remaining_seconds': 0
            }
        }

        # 检查主缓存状态
        if cache_key in self.cache:
            cache_entry = self.cache[cache_key]
            age = current_time - cache_entry['timestamp']
            remaining = strategy['ttl'] - age
            result['main_cache']['age_seconds'] = round(age, 1)
            result['main_cache']['remaining_seconds'] = round(max(0, remaining), 1)
            result['main_cache']['expired'] = age > strategy['ttl']

        # 检查备用缓存状态
        if cache_key in self.backup_cache:
            backup_entry = self.backup_cache[cache_key]
            age = current_time - backup_entry['timestamp']
            remaining = strategy['backup_ttl'] - age
            result['backup_cache']['age_seconds'] = round(age, 1)
            result['backup_cache']['remaining_seconds'] = round(max(0, remaining), 1)
            result['backup_cache']['expired'] = age > strategy['backup_ttl']

        return result

# 全局缓存管理器实例 - 优先使用Redis
if REDIS_AVAILABLE:
    cache_manager = RedisCacheManager()
    logger.info("🔴 使用Redis缓存管理器")
else:
    cache_manager = EnhancedCacheManager()
    logger.info("💾 使用内存缓存管理器")







# ================================================================
# 启动预热系统 - 应用启动时预加载关键数据
# ================================================================

class EnhancedCacheWarmupManager:
    """增强缓存预热管理器 - 预热关键页面数据"""

    def __init__(self):
        self.warmup_progress = {
            'total_tasks': 4,  # 增强预热：4个关键任务
            'completed_tasks': 0,
            'current_task': '',
            'status': 'not_started',  # not_started, running, completed, failed
            'start_time': None,
            'end_time': None,
            'errors': []
        }

    def get_progress(self):
        """获取预热进度"""
        progress = self.warmup_progress.copy()
        if progress['start_time'] and progress['status'] == 'running':
            progress['elapsed_time'] = time.time() - progress['start_time']
        elif progress['start_time'] and progress['end_time']:
            progress['total_time'] = progress['end_time'] - progress['start_time']
        return progress

    def update_progress(self, task_name, completed=False, error=None):
        """更新预热进度"""
        self.warmup_progress['current_task'] = task_name
        if completed:
            self.warmup_progress['completed_tasks'] += 1
        if error:
            self.warmup_progress['errors'].append(f"{task_name}: {error}")

        progress_percent = (self.warmup_progress['completed_tasks'] / self.warmup_progress['total_tasks']) * 100
        logger.info(f"🔥 预热进度: {progress_percent:.1f}% - {task_name}")

    def warmup_sku_list(self):
        """预热SKU列表"""
        try:
            self.update_progress("预热SKU列表...")
            cache_key = "sku_list_all"

            # 使用与API相同的数据获取逻辑
            def get_sku_list_data():
                start_time = time.time()

                # 获取SKU列表 - 修复重复问题，添加历史销量信息
                query = """
                SELECT p.sku, p.supplier_code, p.product_image, p.first_stock_date, p.available_stock,
                       i.quantity as current_stock, i.account_stock,
                       COALESCE(o.total_sales, 0) as total_historical_sales
                FROM (
                    SELECT DISTINCT sku, supplier_code, product_image, first_stock_date, available_stock
                    FROM products
                    WHERE sku IS NOT NULL AND sku != ''
                ) p
                LEFT JOIN (
                    SELECT sku, quantity, account_stock
                    FROM inventory i1
                    WHERE date = (SELECT MAX(date) FROM inventory i2 WHERE i2.sku = i1.sku)
                ) i ON p.sku = i.sku
                LEFT JOIN (
                    SELECT sku, SUM(product_quantity) as total_sales
                    FROM orders
                    GROUP BY sku
                ) o ON p.sku = o.sku
                ORDER BY COALESCE(o.total_sales, 0) DESC, p.sku
                """
                raw_sku_list = execute_query(query)

                # 处理SKU列表，添加图片优化
                sku_list = []
                for item in raw_sku_list:
                    # 安全转换函数
                    def safe_str(val):
                        return str(val) if val is not None and str(val) != 'nan' else ''

                    def safe_int(val):
                        try:
                            return int(val) if val is not None else 0
                        except (ValueError, TypeError):
                            return 0

                    # 优化图片URL
                    img_url = ''
                    if item.get('product_image'):
                        try:
                            img_url = ImageOptimizer.optimize_image_url(
                                safe_str(item['product_image']),
                                user_agent='',
                                width=100,
                                height=100,
                                quality=30
                            )
                        except:
                            img_url = safe_str(item['product_image'])

                    processed_item = {
                        'sku': safe_str(item['sku']),
                        'supplier_code': safe_str(item.get('supplier_code', '')),
                        'product_image': img_url,
                        'first_stock_date': safe_str(item.get('first_stock_date', '')),
                        'available_stock': safe_int(item.get('available_stock', 0)),
                        'current_stock': safe_int(item.get('current_stock', 0)),
                        'account_stock': safe_int(item.get('account_stock', 0)),
                        'total_historical_sales': safe_int(item.get('total_historical_sales', 0))
                    }
                    sku_list.append(processed_item)

                # 获取有订单的供应商列表
                supplier_query = """
                SELECT DISTINCT p.supplier_code
                FROM products p
                INNER JOIN orders o ON p.sku = o.sku
                WHERE p.supplier_code IS NOT NULL
                """
                suppliers = execute_query(supplier_query)
                ordered_suppliers = [s['supplier_code'] for s in suppliers]

                end_time = time.time()
                logger.info(f'SKU列表预热执行时间: {end_time - start_time:.2f}秒，返回{len(sku_list)}个SKU')

                return {
                    'sku_list': sku_list,
                    'ordered_suppliers': ordered_suppliers
                }

            cache_manager.get_cached_data(cache_key, get_sku_list_data)
            self.update_progress("SKU列表预热完成", completed=True)
            return True
        except Exception as e:
            self.update_progress("SKU列表预热失败", error=str(e))
            return False







    def warmup_dashboard_stats(self):
        """预热仪表板统计数据"""
        try:
            self.update_progress("预热仪表板统计...")

            cache_key = "dashboard_stats"
            cache_manager.get_cached_data(cache_key, get_dashboard_stats)

            self.update_progress("仪表板统计预热完成", completed=True)
            return True

        except Exception as e:
            self.update_progress("仪表板统计预热失败", error=str(e))
            return False

    def warmup_dashboard_alerts(self):
        """预热仪表板库存提醒数据"""
        try:
            self.update_progress("预热仪表板库存提醒...")

            # 导入必要的函数
            from database_config import execute_query

            def get_dashboard_alerts_data():
                """获取仪表板库存提醒数据，按预计可售天数排序 - 改进版本"""
                import time
                start_time = time.time()
                logger.info("🔥 预热: 开始获取仪表板库存提醒数据...")

                query = """
                SELECT
                    p.sku,
                    p.product_image,
                    p.supplier_code,
                    COALESCE(o30.sales_30d, 0) as sales_30d,
                    COALESCE(o60.sales_60d, 0) as sales_60d,
                    COALESCE(i.quantity, 0) as 公共库存,
                    COALESCE(i.quantity, 0) + COALESCE(i.sl_quantity, 0) + COALESCE(i.gwyj_quantity, 0) as 总库存,
                    i.estimated_restock_date as 预计到货时间,
                    i.estimated_restock_qty as 预计到货数量
                FROM products p
                LEFT JOIN (
                    SELECT sku, SUM(product_quantity) as sales_30d
                    FROM orders
                    WHERE order_date >= DATE_SUB(NOW(), INTERVAL 30 DAY)
                    GROUP BY sku
                ) o30 ON p.sku = o30.sku
                LEFT JOIN (
                    SELECT sku, SUM(product_quantity) as sales_60d
                    FROM orders
                    WHERE order_date >= DATE_SUB(NOW(), INTERVAL 60 DAY)
                    GROUP BY sku
                ) o60 ON p.sku = o60.sku
                LEFT JOIN (
                    SELECT
                        sku,
                        date,
                        quantity,
                        sl_quantity,
                        gwyj_quantity,
                        estimated_restock_date,
                        estimated_restock_qty,
                        ROW_NUMBER() OVER (PARTITION BY sku ORDER BY date DESC) as rn
                    FROM inventory
                    WHERE sku IS NOT NULL AND sku != ''
                ) i ON p.sku = i.sku AND i.rn = 1
                WHERE p.sku IS NOT NULL AND p.sku != ''
                AND (COALESCE(i.quantity, 0) + COALESCE(i.sl_quantity, 0) + COALESCE(i.gwyj_quantity, 0)) > 0
                """

                raw_data = execute_query(query)
                logger.info(f"🔥 预热: 查询到 {len(raw_data)} 条有库存的SKU")

                if not raw_data:
                    logger.warning("🔥 预热: 警告 - 没有查询到有库存的SKU数据")
                    return []

                # 计算预计可售天数并排序
                alerts_with_days = []
                alerts_120_days = []  # 备用数据

                for item in raw_data:
                    total_stock = (item.get('总库存', 0) or 0)
                    sales_30d = item.get('sales_30d', 0) or 0

                    if sales_30d > 0:
                        estimated_days = round((total_stock * 30) / sales_30d, 1)
                    else:
                        estimated_days = float('inf')

                    alert_item = {
                        'SKU': item.get('sku', ''),
                        '近30天销量': sales_30d,
                        '近60天销量': item.get('sales_60d', 0) or 0,
                        '公共库存': item.get('公共库存', 0) or 0,
                        '总库存': total_stock,
                        '预计到货时间': item.get('预计到货时间', '') or '',
                        '预计到货数量': item.get('预计到货数量', '') or '',
                        '产品主图': item.get('product_image', '') or '',
                        'supplier_code': item.get('supplier_code', '') or '',
                        'estimated_days': estimated_days
                    }

                    # 收集≤60天的SKU
                    if estimated_days <= 60:
                        alerts_with_days.append(alert_item)

                    # 收集≤120天的SKU作为备用
                    if estimated_days <= 120:
                        alerts_120_days.append(alert_item)

                logger.info(f"🔥 预热: 找到 {len(alerts_with_days)} 个≤60天的SKU")
                logger.info(f"🔥 预热: 找到 {len(alerts_120_days)} 个≤120天的SKU")

                # 智能选择数据
                if len(alerts_with_days) >= 10:
                    # 有足够的≤60天SKU
                    alerts_with_days.sort(key=lambda x: x['estimated_days'])
                    result = alerts_with_days[:30]
                    logger.info(f"🔥 预热: 使用≤60天数据，返回 {len(result)} 个SKU")
                elif len(alerts_120_days) > 0:
                    # 扩展到≤120天
                    alerts_120_days.sort(key=lambda x: x['estimated_days'])
                    result = alerts_120_days[:30]
                    logger.info(f"🔥 预热: 扩展到≤120天数据，返回 {len(result)} 个SKU")
                else:
                    # 没有符合条件的SKU
                    logger.warning("🔥 预热: 警告 - 没有找到符合条件的SKU")
                    result = []

                query_time = time.time() - start_time
                logger.info(f"🔥 预热: 仪表板数据获取完成，耗时 {query_time:.2f}秒")

                return result

            cache_key = "dashboard_alerts"

            # 获取数据并验证
            data = get_dashboard_alerts_data()

            # 数据验证
            if not isinstance(data, list):
                logger.error("🔥 预热: 错误 - 返回的数据不是列表格式")
                return False

            if len(data) == 0:
                logger.warning("🔥 预热: 警告 - 返回的数据为空，这可能是正常的（没有紧急SKU）")
            else:
                logger.info(f"🔥 预热: 成功生成 {len(data)} 条仪表板提醒数据")
                # 验证数据结构
                sample_item = data[0]
                required_fields = ['SKU', 'estimated_days', '总库存', '近30天销量']
                missing_fields = [field for field in required_fields if field not in sample_item]
                if missing_fields:
                    logger.error(f"🔥 预热: 错误 - 数据缺少必要字段: {missing_fields}")
                    return False

            # 存储到缓存
            cache_manager.set_cached_data(cache_key, data, ttl=3600)  # 1小时过期
            logger.info(f"🔥 预热: 仪表板数据已缓存，TTL=3600秒")

            self.update_progress("仪表板库存提醒预热完成", completed=True)
            return True

        except Exception as e:
            logger.error(f"🔥 预热: 仪表板库存提醒预热失败: {str(e)}")
            import traceback
            logger.error(f"🔥 预热: 错误详情: {traceback.format_exc()}")
            self.update_progress("仪表板库存提醒预热失败", error=str(e))
            return False

    def warmup_inventory_alerts(self):
        """预热库存提醒（近7天）"""
        try:
            self.update_progress("预热库存提醒数据...")
            today = datetime.now()
            success_count = 0

            # 预热近7天的数据
            for i in range(7):
                date = (today - timedelta(days=i)).strftime('%Y-%m-%d')
                try:
                    cache_key = f"inventory_alerts_daily_{date}"
                    cache_manager.get_cached_data(cache_key, get_inventory_alerts, date, date)
                    success_count += 1
                except Exception as e:
                    logger.warning(f"预热库存提醒失败 {date}: {e}")

            if success_count >= 5:  # 至少成功5天
                self.update_progress("库存提醒预热完成", completed=True)
                return True
            else:
                self.update_progress("库存提醒预热部分失败", error=f"只成功{success_count}/7天")
                return False

        except Exception as e:
            self.update_progress("库存提醒预热失败", error=str(e))
            return False

    def warmup_restock_calendar(self):
        """预热补货日历数据"""
        try:
            self.update_progress("预热补货日历...")

            cache_key = "restock_calendar"
            cache_manager.get_cached_data(cache_key, get_restock_calendar)

            self.update_progress("补货日历预热完成", completed=True)
            return True

        except Exception as e:
            self.update_progress("补货日历预热失败", error=str(e))
            return False



    def start_warmup(self):
        """开始增强预热过程 - 预热关键页面数据"""
        try:
            logger.info("🔥 开始增强缓存预热...")
            self.warmup_progress['status'] = 'running'
            self.warmup_progress['start_time'] = time.time()
            self.warmup_progress['completed_tasks'] = 0
            self.warmup_progress['errors'] = []

            # 预热关键页面数据
            tasks = [
                ("SKU列表", self.warmup_sku_list),
                ("仪表板统计", self.warmup_dashboard_stats),
                ("仪表板库存提醒", self.warmup_dashboard_alerts),
                ("库存提醒", self.warmup_inventory_alerts),
                ("补货日历", self.warmup_restock_calendar)
            ]

            failed_tasks = []
            for task_name, task_func in tasks:
                logger.info(f"🔥 开始预热: {task_name}")
                if not task_func():
                    failed_tasks.append(task_name)

            self.warmup_progress['end_time'] = time.time()
            total_time = self.warmup_progress['end_time'] - self.warmup_progress['start_time']

            if len(failed_tasks) == 0:
                self.warmup_progress['status'] = 'completed'
                logger.info(f"🎉 增强缓存预热完成！总耗时: {total_time:.2f}秒")
                return True
            elif len(failed_tasks) < len(tasks):
                self.warmup_progress['status'] = 'completed'
                logger.warning(f"⚠️ 增强缓存预热部分完成，失败任务: {failed_tasks}，总耗时: {total_time:.2f}秒")
                return True
            else:
                self.warmup_progress['status'] = 'failed'
                logger.error(f"❌ 增强缓存预热失败，所有任务都失败了，总耗时: {total_time:.2f}秒")
                return False

        except Exception as e:
            self.warmup_progress['status'] = 'failed'
            self.warmup_progress['end_time'] = time.time()
            logger.error(f"❌ 增强缓存预热过程出错: {e}")
            return False

# 全局预热管理器实例
warmup_manager = EnhancedCacheWarmupManager()

def start_application_warmup():
    """启动应用预热"""
    def warmup_task():
        try:
            logger.info("🔥 应用启动预热开始...")
            success = warmup_manager.start_warmup()

            if not success:
                logger.error("❌ 预热失败，应用将退出")
                # 在实际部署中，这里可以选择退出应用
                # import sys
                # sys.exit(1)
            else:
                logger.info("✅ 应用预热成功，系统就绪")

        except Exception as e:
            logger.error(f"❌ 应用预热任务出错: {e}")

    # 立即启动预热
    warmup_thread = threading.Thread(target=warmup_task, daemon=True)
    warmup_thread.start()
    logger.info("🔥 应用预热任务已启动")

# 登录配置
LOGIN_USERS = {
    'admin': 'admin123',
    'kkuguan': '123456',
}

# 登录安全
_login_attempts = {}
MAX_LOGIN_ATTEMPTS = 5
LOCKOUT_DURATION = 300  # 5分钟

def is_ip_locked(ip):
    """检查IP是否被锁定"""
    if ip in _login_attempts:
        attempts, last_attempt = _login_attempts[ip]
        if attempts >= MAX_LOGIN_ATTEMPTS:
            if time.time() - last_attempt < LOCKOUT_DURATION:
                return True
            else:
                del _login_attempts[ip]
    return False

def record_login_attempt(ip, success=False):
    """记录登录尝试"""
    if success:
        _login_attempts.pop(ip, None)
    else:
        attempts, _ = _login_attempts.get(ip, (0, 0))
        _login_attempts[ip] = (attempts + 1, time.time())

def login_required(f):
    """登录验证装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'logged_in' not in session:
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    return decorated_function

# ================================================================
# API路由 - 直接查询MySQL
# ================================================================

@app.route('/api/sku_list')
@login_required
@optimize_api_response
def api_sku_list():
    """SKU列表API - 混合缓存版本"""
    try:
        # 构建缓存键
        cache_key = "sku_list_all"

        # 定义获取SKU列表数据的函数
        def get_sku_list_data():
            start_time = time.time()
            logger.info("🔍 开始获取SKU列表数据...")

            # 先尝试简单查询测试数据库连接
            try:
                test_query = "SELECT COUNT(*) as count FROM products WHERE sku IS NOT NULL AND sku != ''"
                test_result = execute_query(test_query)
                if test_result:
                    total_products = test_result[0]['count']
                    logger.info(f"📊 数据库中有效产品数量: {total_products}")
                else:
                    logger.warning("⚠️ 无法获取产品数量，使用模拟数据")
                    return get_mock_sku_data()
            except Exception as e:
                logger.error(f"❌ 数据库连接测试失败: {e}，使用模拟数据")
                return get_mock_sku_data()

            # 获取SKU列表 - 使用最简单的查询避免磁盘空间问题
            try:
                # 分步查询，避免大型JOIN操作
                logger.info("📦 开始分步查询产品数据...")

                # 获取所有产品信息（不限制数量）
                basic_query = """
                SELECT sku, supplier_code, product_image, first_stock_date, available_stock
                FROM products
                WHERE sku IS NOT NULL AND sku != ''
                ORDER BY supplier_code, sku
                """
                raw_sku_list = execute_query(basic_query)
                logger.info(f"📦 基本查询成功，获取到 {len(raw_sku_list) if raw_sku_list else 0} 个产品")

            except Exception as e:
                logger.error(f"❌ 基本SKU查询失败: {e}")
                # 尝试更简单的查询
                try:
                    simple_query = "SELECT sku, supplier_code FROM products WHERE sku IS NOT NULL ORDER BY supplier_code, sku"
                    raw_sku_list = execute_query(simple_query)
                    logger.info(f"📦 简化查询成功，获取到 {len(raw_sku_list) if raw_sku_list else 0} 个产品")
                except Exception as e2:
                    logger.error(f"❌ 简化查询也失败: {e2}")
                    raw_sku_list = []

            # 处理SKU列表，添加图片优化和历史销量计算
            sku_list = []
            if raw_sku_list:
                # 批量获取所有SKU的历史销量
                all_skus = [item['sku'] for item in raw_sku_list if item.get('sku')]
                sales_dict = {}

                if all_skus:
                    try:
                        # 批量查询历史销量（最近6个月）
                        placeholders = ','.join(['%s'] * len(all_skus))
                        sales_query = f"""
                        SELECT sku, SUM(product_quantity) as total_sales
                        FROM orders
                        WHERE sku IN ({placeholders})
                        AND order_date >= DATE_SUB(NOW(), INTERVAL 6 MONTH)
                        GROUP BY sku
                        """
                        sales_data = execute_query(sales_query, all_skus)
                        if sales_data:
                            sales_dict = {item['sku']: int(item['total_sales'] or 0) for item in sales_data}
                            logger.info(f"📊 获取到 {len(sales_dict)} 个SKU的历史销量数据")
                    except Exception as e:
                        logger.warning(f"⚠️ 获取历史销量失败: {e}")
                        sales_dict = {}

                for item in raw_sku_list:
                    # 安全转换函数
                    def safe_str(val):
                        return str(val) if val is not None and str(val) != 'nan' else ''

                    def safe_int(val):
                        try:
                            return int(val) if val is not None else 0
                        except (ValueError, TypeError):
                            return 0

                    # 优化图片URL
                    img_url = ''
                    if item.get('product_image'):
                        try:
                            img_url = ImageOptimizer.optimize_image_url(
                                safe_str(item['product_image']),
                                user_agent='',
                                width=200,
                                height=200,
                                quality=60
                            )
                        except:
                            img_url = safe_str(item['product_image'])

                    # 获取该SKU的历史销量
                    sku = safe_str(item['sku'])
                    historical_sales = sales_dict.get(sku, 0)

                    processed_item = {
                        'sku': sku,
                        'supplier_code': safe_str(item.get('supplier_code', '')),
                        'product_image': img_url,
                        'first_stock_date': safe_str(item.get('first_stock_date', '')),
                        'available_stock': safe_int(item.get('available_stock', 0)),
                        'current_stock': 0,  # 简化版本，暂时设为0
                        'account_stock': 0,  # 简化版本，暂时设为0
                        'total_historical_sales': historical_sales  # 真实的历史销量
                    }
                    sku_list.append(processed_item)

            logger.info(f"✅ 处理完成，返回 {len(sku_list)} 个SKU")

            # 获取供应商列表（从已获取的产品中提取）
            try:
                if raw_sku_list:
                    # 从已获取的SKU列表中提取供应商，避免额外查询
                    supplier_set = set()
                    for item in raw_sku_list:
                        supplier = item.get('supplier_code')
                        if supplier and supplier.strip():
                            supplier_set.add(supplier.strip())
                    ordered_suppliers = sorted(list(supplier_set))
                    logger.info(f"📊 从产品数据中提取到 {len(ordered_suppliers)} 个供应商")
                else:
                    ordered_suppliers = []
            except Exception as e:
                logger.error(f"❌ 供应商提取失败: {e}")
                ordered_suppliers = []

            end_time = time.time()
            logger.info(f'SKU列表API执行时间: {end_time - start_time:.2f}秒，返回{len(sku_list)}个SKU')

            return {
                'sku_list': sku_list,
                'ordered_suppliers': ordered_suppliers
            }

        # 使用缓存管理器
        data = cache_manager.get_cached_data(cache_key, get_sku_list_data)
        return jsonify(data)

    except Exception as e:
        logger.error(f'SKU列表API错误: {e}')
        return jsonify({'error': '服务器错误'}), 500

@app.route('/api/sku_detail')
@login_required
def api_sku_detail():
    """SKU详情API - 混合缓存版本"""
    try:
        sku = request.args.get('sku')
        time_range = request.args.get('time_range', '近30天')
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')

        if not sku:
            return jsonify({'error': '未提供SKU'}), 400

        sku = sku.strip()
        if not sku or len(sku) > 50:
            return jsonify({'error': '无效的SKU格式'}), 400

        # 构建缓存键
        cache_key = f"sku_detail_{sku}_{time_range}_{start_date}_{end_date}"

        # 定义获取SKU详情数据的函数
        def get_sku_detail_data():
            logger.info(f'请求SKU详情 - SKU: {sku}')
            result_data = get_sku_detail(sku)

            if not result_data:
                raise ValueError('SKU不存在')

            return result_data

        # 使用缓存管理器
        data = cache_manager.get_cached_data(cache_key, get_sku_detail_data)
        return jsonify(data)

    except ValueError as e:
        return jsonify({'error': str(e)}), 404
    except Exception as e:
        logger.error(f'处理SKU详情时出错: {str(e)}')
        return jsonify({'error': str(e)}), 500

@app.route('/api/sales_stock')
@login_required
@optimize_api_response
def api_sales_stock():
    """销量库存API - 混合缓存版本（图表数据关键API）"""
    try:
        sku = request.args.get('sku')
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')

        if not all([sku, start_date, end_date]):
            return jsonify({'error': '缺少必要参数'}), 400

        # 构建缓存键
        cache_key = f"sales_stock_{sku}_{start_date}_{end_date}"

        # 定义获取销量库存数据的函数
        def get_sales_stock_data():
            logger.info(f'请求销量和库存数据 - SKU: {sku}, 开始日期: {start_date}, 结束日期: {end_date}')

            # 生成日期范围
            start_dt = datetime.strptime(start_date, '%Y-%m-%d')
            end_dt = datetime.strptime(end_date, '%Y-%m-%d')
            date_range = []
            current_date = start_dt
            while current_date <= end_dt:
                date_range.append(current_date.strftime('%Y-%m-%d'))
                current_date += timedelta(days=1)

            # 获取销量数据
            sales_query = """
            SELECT DATE(order_date) as date, SUM(product_quantity) as sales
            FROM orders
            WHERE sku = %s AND order_date >= %s AND order_date <= %s
            GROUP BY DATE(order_date)
            ORDER BY date
            """
            sales_data = execute_query(sales_query, (sku, start_date, end_date))
            sales_dict = {item['date'].strftime('%Y-%m-%d'): int(item['sales']) for item in sales_data}

            # 获取库存数据（包含各种库存类型和预计到货时间）
            inventory_query = """
            SELECT DATE(date) as date, quantity, account_stock, sl_quantity, gwyj_quantity, estimated_restock_date
            FROM inventory
            WHERE sku = %s AND date >= %s AND date <= %s
            ORDER BY date
            """
            inventory_data = execute_query(inventory_query, (sku, start_date, end_date))

            # 构建各种库存字典
            public_stock_dict = {}  # 公共库存
            own_stock_dict = {}     # 自己库存 = SL + RW + GW
            total_stock_dict = {}   # 可用总库存

            # 计算实际到货时间：支持多种检测方式
            actual_arrival_dates = []  # 支持多个实际到货时间
            prev_restock_date = None

            def parse_date_range(date_range_str):
                """解析日期范围字符串，返回开始日期"""
                if not date_range_str or date_range_str == '' or date_range_str in ['None', 'NULL', '<NA>', 'nan', 'NaN']:
                    return None
                try:
                    # 转换为字符串并检查
                    date_str = str(date_range_str).strip()
                    if date_str in ['', 'None', 'NULL', '<NA>', 'nan', 'NaN']:
                        return None

                    if '~' in date_str:
                        start_date_str = date_str.split('~')[0].strip()
                        from datetime import datetime
                        return datetime.strptime(start_date_str, '%Y-%m-%d')
                    else:
                        from datetime import datetime
                        return datetime.strptime(date_str, '%Y-%m-%d')
                except:
                    return None

            def is_time_gap_significant(prev_range, curr_range, threshold_days=10):
                """检查两个时间段之间的差距是否超过阈值"""
                prev_date = parse_date_range(prev_range)
                curr_date = parse_date_range(curr_range)

                if prev_date and curr_date:
                    gap_days = abs((curr_date - prev_date).days)
                    return gap_days > threshold_days
                return False

            for item in inventory_data:
                date_str = item['date'].strftime('%Y-%m-%d')
                quantity = item['quantity'] or 0
                account_stock = item['account_stock'] or 0
                sl_quantity = item['sl_quantity'] or 0
                gwyj_quantity = item['gwyj_quantity'] or 0
                estimated_restock_date = item.get('estimated_restock_date')

                # 计算各种库存
                public_stock = quantity - account_stock  # 公共库存
                own_stock = sl_quantity + account_stock + gwyj_quantity  # 自己库存 = SL + RW + GW
                total_stock = public_stock + own_stock  # 可用总库存

                public_stock_dict[date_str] = public_stock
                own_stock_dict[date_str] = own_stock
                total_stock_dict[date_str] = total_stock

                # 增强的实际到货时间检测逻辑
                should_record_arrival = False

                if prev_restock_date is not None:
                    # 情况1: 从有值变为NULL
                    if prev_restock_date != '' and (estimated_restock_date is None or estimated_restock_date == ''):
                        should_record_arrival = True

                    # 情况2: 时间段变化超过10天
                    elif (prev_restock_date != '' and estimated_restock_date is not None and
                          estimated_restock_date != '' and prev_restock_date != estimated_restock_date):
                        if is_time_gap_significant(prev_restock_date, estimated_restock_date, 10):
                            should_record_arrival = True

                if should_record_arrival:
                    actual_arrival_dates.append(date_str)

                prev_restock_date = estimated_restock_date

            # 为了保持向后兼容，返回第一个实际到货时间
            actual_arrival_date = actual_arrival_dates[0] if actual_arrival_dates else None

            # 构建返回数据
            dates = []
            sales = []
            public_stock = []    # 公共库存（原来的库存）
            own_stock = []       # 自己库存
            total_stock = []     # 可用总库存

            for date_str in date_range:
                dates.append(date_str)
                sales.append(sales_dict.get(date_str, 0))
                public_stock.append(public_stock_dict.get(date_str, None))
                own_stock.append(own_stock_dict.get(date_str, None))
                total_stock.append(total_stock_dict.get(date_str, None))

            return {
                'dates': dates,
                'sales': sales,
                'stock': public_stock,      # 保持原有字段名，显示公共库存
                'public_stock': public_stock,
                'own_stock': own_stock,
                'total_stock': total_stock,
                'actual_arrival_date': actual_arrival_date,  # 第一个实际到货时间（向后兼容）
                'actual_arrival_dates': actual_arrival_dates  # 所有实际到货时间
            }

        # 使用缓存管理器
        data = cache_manager.get_cached_data(cache_key, get_sales_stock_data)
        return jsonify(data)

    except Exception as e:
        logger.error(f'处理销量和库存数据时出错: {str(e)}')
        return jsonify({'error': str(e)}), 500

@app.route('/api/sales_rank')
@login_required
def api_sales_rank():
    """销量排名API - 混合缓存版本"""
    try:
        # 获取参数
        time_range = request.args.get('time_range', '近30天')
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        limit = int(request.args.get('limit', 20))

        # 构建缓存键
        cache_key = f"sales_rank_{time_range}_{start_date}_{end_date}_{limit}"

        # 定义获取销量排名数据的函数
        def get_sales_rank_data():
            logger.info(f'查询销量排名 - 时间区间: {time_range}, 限制: {limit}')

            # 如果有具体的日期范围，使用日期范围查询
            if start_date and end_date:
                query = """
                SELECT o.sku, SUM(o.product_quantity) as product_quantity, COUNT(*) as order_count,
                       p.product_name, p.supplier_code, p.product_image
                FROM orders o
                LEFT JOIN products p ON o.sku = p.sku
                WHERE o.order_date >= %s AND o.order_date <= %s
                GROUP BY o.sku, p.product_name, p.supplier_code, p.product_image
                ORDER BY product_quantity DESC
                LIMIT %s
                """
                result_data = execute_query(query, (start_date, end_date, limit))
            else:
                # 使用时间范围
                days_map = {'近30天': 30, '近60天': 60, '近90天': 90}
                days = days_map.get(time_range, 30)

                query = """
                SELECT o.sku, SUM(o.product_quantity) as product_quantity, COUNT(*) as order_count,
                       p.product_name, p.supplier_code, p.product_image
                FROM orders o
                LEFT JOIN products p ON o.sku = p.sku
                WHERE o.order_date >= DATE_SUB(NOW(), INTERVAL %s DAY)
                GROUP BY o.sku, p.product_name, p.supplier_code, p.product_image
                ORDER BY product_quantity DESC
                LIMIT %s
                """
                result_data = execute_query(query, (days, limit))

            # 优化图片URL并确保数据格式正确
            for item in result_data:
                # 确保product_quantity是整数
                if 'product_quantity' in item:
                    item['product_quantity'] = int(item['product_quantity']) if item['product_quantity'] else 0

                # 优化图片URL
                if item.get('product_image'):
                    try:
                        item['product_image'] = ImageOptimizer.optimize_image_url(
                            str(item['product_image']),
                            user_agent='',
                            width=100,
                            height=100,
                            quality=30
                        )
                    except:
                        pass

            return result_data

        # 使用缓存管理器
        data = cache_manager.get_cached_data(cache_key, get_sales_rank_data)
        return jsonify(data)

    except Exception as e:
        logger.error(f'销量排名API错误: {e}')
        return jsonify({'error': str(e)}), 500

@app.route('/api/recommendations')
@login_required
@handle_errors
def api_recommendations():
    """推荐API - 混合缓存版本"""
    try:
        # 获取筛选参数
        product_type = request.args.get('type', 'all')
        supplier = request.args.get('supplier', '')
        sort_by = request.args.get('sort_by', '30d')
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 24, type=int)
        min_price = request.args.get('min_price', type=float)
        max_price = request.args.get('max_price', type=float)

        # 构建缓存键
        cache_key = f"recommendations_{product_type}_{supplier}_{sort_by}_{min_price}_{max_price}_{page}_{per_page}"

        # 定义获取推荐数据的函数
        def get_recommendations_data():
            start_time = time.time()
            logger.info(f'推荐API请求: 类型={product_type}, 供应商={supplier}')

            # 构建基础产品查询 - 包含到货时间和数量
            base_query = """
            SELECT p.sku, p.supplier_code, p.product_name, p.product_image,
                   p.available_stock, p.first_stock_date, p.Price as price,
                   i.quantity as current_stock, i.account_stock,
                   i.estimated_restock_date, i.estimated_restock_qty
            FROM (
                SELECT DISTINCT sku, supplier_code, product_name, product_image, available_stock, first_stock_date, Price
                FROM products
                WHERE sku IS NOT NULL AND sku != ''
            ) p
            LEFT JOIN (
                SELECT sku, quantity, account_stock, estimated_restock_date, estimated_restock_qty
                FROM inventory i1
                WHERE date = (SELECT MAX(date) FROM inventory i2 WHERE i2.sku = i1.sku)
            ) i ON p.sku = i.sku
            WHERE 1=1
            """
            params = []

            if supplier:
                base_query += " AND p.supplier_code = %s"
                params.append(supplier)

            if min_price is not None:
                base_query += " AND p.Price >= %s"
                params.append(min_price)

            if max_price is not None:
                base_query += " AND p.Price <= %s"
                params.append(max_price)

            base_query += " ORDER BY p.sku"

            products = execute_query(base_query, tuple(params))

            # 计算库存变化数据
            def calculate_inventory_changes(days):
                """计算指定天数内的库存变化"""
                query = """
                SELECT sku, date, quantity
                FROM inventory
                WHERE date >= DATE_SUB(NOW(), INTERVAL %s DAY)
                ORDER BY sku, date
                """
                inventory_data = execute_query(query, (days + 1,))  # 多取一天用于计算变化

                changes_dict = {}
                current_sku = None
                prev_quantity = None
                total_change = 0

                for row in inventory_data:
                    sku = row['sku']
                    quantity = row['quantity']

                    if sku != current_sku:
                        # 新的SKU，保存前一个SKU的结果
                        if current_sku is not None:
                            changes_dict[current_sku] = total_change

                        # 重置变量
                        current_sku = sku
                        prev_quantity = quantity
                        total_change = 0
                    else:
                        # 同一个SKU，计算库存变化
                        if prev_quantity is not None and quantity is not None:
                            change = prev_quantity - quantity  # 前一天库存 - 当天库存
                            if change > 0:  # 只计算正数（库存减少）
                                total_change += change
                        prev_quantity = quantity

                # 保存最后一个SKU的结果
                if current_sku is not None:
                    changes_dict[current_sku] = total_change

                return changes_dict

            # 计算30天和7天的库存变化
            inventory_changes_30d = calculate_inventory_changes(30)
            inventory_changes_7d = calculate_inventory_changes(7)

            # 处理每个产品
            all_items = []
            for product in products:
                sku = product['sku']

                # 安全转换函数
                def safe_str(val):
                    return str(val) if val is not None and str(val) != 'nan' else ''

                def safe_int(val):
                    try:
                        return int(val) if val is not None else 0
                    except (ValueError, TypeError):
                        return 0

                # 获取库存变化数据
                stock_change_30d = inventory_changes_30d.get(sku, 0)
                stock_change_7d = inventory_changes_7d.get(sku, 0)

                # 优化图片URL
                img_url = ''
                if product.get('product_image'):
                    try:
                        img_url = ImageOptimizer.optimize_image_url(
                            safe_str(product['product_image']),
                            user_agent='',
                            width=200,
                            height=200,
                            quality=60
                        )
                    except:
                        img_url = safe_str(product['product_image'])

                # 安全转换价格
                def safe_float(val):
                    try:
                        return float(val) if val is not None else 0.0
                    except (ValueError, TypeError):
                        return 0.0

                # 构建项目数据
                item = {
                    'sku': sku,
                    'supplier_code': safe_str(product.get('supplier_code', '')),
                    'product_name': safe_str(product.get('product_name', '')),
                    'product_image': img_url,
                    'available_stock': safe_int(product.get('available_stock', 0)),
                    'current_stock': safe_int(product.get('current_stock', 0)),
                    'account_stock': safe_int(product.get('account_stock', 0)),
                    'first_stock_date': safe_str(product.get('first_stock_date', '')),
                    'price': safe_float(product.get('price', 0.0)),  # 添加价格
                    'estimated_restock_date': safe_str(product.get('estimated_restock_date', '')),  # 添加预计到货时间
                    'estimated_restock_qty': safe_str(product.get('estimated_restock_qty', '')),  # 添加预计到货数量（区间格式）
                    'stock_change_30d': int(stock_change_30d),
                    'stock_change_7d': int(stock_change_7d),
                    'days_since_first_stock': 0  # 简化版本
                }

                all_items.append(item)

            # 排序 - 按库存变化排序，优先显示库存消耗大的SKU
            if sort_by == '30d':
                all_items.sort(key=lambda x: x['stock_change_30d'], reverse=True)
            else:
                all_items.sort(key=lambda x: x['stock_change_7d'], reverse=True)

            # 分页处理
            total_count = len(all_items)
            total_pages = (total_count + per_page - 1) // per_page

            start_index = (page - 1) * per_page
            end_index = start_index + per_page
            paginated_result = all_items[start_index:end_index]

            end_time = time.time()
            logger.info(f'推荐API执行时间: {end_time - start_time:.2f}秒，返回{len(paginated_result)}个结果，总共{total_count}个SKU')

            return {
                'items': paginated_result,
                'total': total_count,
                'page': page,
                'per_page': per_page,
                'pages': total_pages
            }

        # 使用缓存管理器
        data = cache_manager.get_cached_data(cache_key, get_recommendations_data)
        return jsonify(data)

    except Exception as e:
        logger.error(f'推荐API错误: {e}')
        return jsonify({'error': str(e)}), 500

@app.route('/api/restock_calendar')
@login_required
@handle_errors
def api_restock_calendar():
    """补货日历API - 支持基础和增强模式"""
    try:
        # 获取参数：enhanced=true 返回增强数据，否则返回基础数据
        enhanced = request.args.get('enhanced', 'false').lower() == 'true'

        if enhanced:
            # 返回增强数据
            cache_key = "enhanced_restock_calendar"

            def get_enhanced_restock_calendar_data():
                return get_enhanced_restock_calendar()

            data = cache_manager.get_cached_data(cache_key, get_enhanced_restock_calendar_data)
            logger.info(f"补货日历API返回增强数据")
        else:
            # 返回基础数据
            cache_key = "restock_calendar"
            data = cache_manager.get_cached_data(cache_key, get_restock_calendar)
            logger.info(f"补货日历API返回基础数据")

        return jsonify(data)

    except Exception as e:
        logger.error(f'补货日历API处理失败: {str(e)}')
        return jsonify({'error': f'服务器内部错误: {str(e)}'}), 500

def get_feishu_sku_mapping_cached():
    """获取缓存的飞书SKU映射数据 - 统一服务"""
    if not FEISHU_ENABLED:
        return {}

    try:
        cache_key = "feishu_sku_mapping"

        def get_mapping_data():
            return get_sku_shop_mapping()

        return cache_manager.get_cached_data(cache_key, get_mapping_data)
    except Exception as e:
        logger.error(f"获取飞书映射数据失败: {e}")
        return {}

def get_platform_store_mapping_cached():
    """获取缓存的平台店铺映射数据"""
    if not FEISHU_ENABLED:
        return {}

    try:
        cache_key = "platform_store_mapping"

        def get_mapping_data():
            return get_platform_store_mapping()

        return cache_manager.get_cached_data(cache_key, get_mapping_data)
    except Exception as e:
        logger.error(f"获取平台店铺映射数据失败: {e}")
        return {}

def convert_feishu_data_for_restock():
    """将飞书数据转换为补货周期页面需要的格式 - 缓存优化版本"""
    try:
        cache_key = "feishu_converted_data"

        def get_converted_data():
            # 获取缓存的飞书数据
            feishu_mapping = get_feishu_sku_mapping_cached()

            # 获取平台店铺映射数据
            platform_store_mapping = get_platform_store_mapping_cached()
            logger.info(f"获取到平台店铺映射: {platform_store_mapping}")

            # 定义固定的平台列表（来自飞书表格的表头）
            fixed_platforms = {'Amazon', 'eBay', 'Walmart'}

            # 平台映射函数（与SKU详情页面保持一致）
            def match_platform(channel):
                if not channel:
                    return '其他'
                channel_str = str(channel).lower()
                if 'amazon' in channel_str:
                    return 'Amazon'
                elif 'ebay' in channel_str:
                    return 'eBay'
                elif 'walmart' in channel_str:
                    return 'Walmart'
                elif 'shopify' in channel_str:
                    return 'Shopify'
                else:
                    return '其他'

            # 店铺到平台的映射函数（使用飞书平台店铺映射）
            def get_platform_for_store(store_name):
                """根据店铺名称获取对应的平台"""
                if not platform_store_mapping:
                    # 如果没有飞书映射，使用原来的逻辑
                    return match_platform(store_name)

                # 在飞书映射中查找店铺对应的平台
                for platform, stores in platform_store_mapping.items():
                    if store_name in stores:
                        return platform

                # 如果在飞书映射中找不到，使用原来的逻辑
                return match_platform(store_name)

            # 使用固定的平台列表，如果有飞书映射则使用映射中的平台，否则使用默认的三个平台
            if platform_store_mapping:
                # 确保飞书映射中的平台都在固定平台列表中
                real_platforms = set(platform_store_mapping.keys()) & fixed_platforms
                # 如果飞书映射为空或没有匹配的平台，使用默认平台
                if not real_platforms:
                    real_platforms = fixed_platforms
            else:
                real_platforms = fixed_platforms

            if not feishu_mapping:
                logger.warning("飞书SKU映射数据为空，使用真实平台数据")
                # 如果有平台店铺映射，返回所有店铺；否则返回空列表
                all_stores = set()
                if platform_store_mapping:
                    for stores_list in platform_store_mapping.values():
                        all_stores.update(stores_list)
                return {}, real_platforms, all_stores

            # 转换数据格式
            shop_sku_mapping = {}
            stores = set()

            for sku, shop_info_list in feishu_mapping.items():
                if not sku or not shop_info_list:
                    continue

                shop_sku_mapping[sku] = []

                for shop_info in shop_info_list:
                    shop_name = shop_info.get('shop_name', '未知店铺')
                    shop_sku = shop_info.get('shop_sku', '')

                    # 使用新的平台映射逻辑
                    platform = get_platform_for_store(shop_name)

                    stores.add(shop_name)

                    shop_sku_mapping[sku].append({
                        'shop_sku': shop_sku,
                        'platform': platform,
                        'store': shop_name
                    })

            logger.info(f"飞书数据转换完成: {len(shop_sku_mapping)} 个SKU, {len(real_platforms)} 个平台, {len(stores)} 个店铺")
            return shop_sku_mapping, real_platforms, stores

        # 使用缓存管理器
        return cache_manager.get_cached_data(cache_key, get_converted_data)

    except Exception as e:
        logger.error(f"转换飞书数据失败: {e}")
        return {}, set(), set()

def get_sku_shop_info(sku_list):
    """获取SKU的店铺信息"""
    if not FEISHU_ENABLED:
        return {}

    try:
        # 使用统一的飞书数据服务
        sku_mapping = get_feishu_sku_mapping_cached()

        # 为每个SKU构建店铺信息
        sku_shop_info = {}
        for sku in sku_list:
            if sku in sku_mapping:
                sku_shop_info[sku] = sku_mapping[sku]

        return sku_shop_info
    except Exception as e:
        logger.error(f"获取SKU店铺信息失败: {e}")
        return {}

@app.route('/api/inventory_alerts')
@login_required
@handle_errors
def api_inventory_alerts():
    """库存预警API - 智能按天缓存版本，包含店铺SKU信息"""
    try:
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')

        # 智能缓存策略：按天缓存，然后组合
        def get_smart_cached_alerts():
            # 生成日期范围
            start_dt = datetime.strptime(start_date, '%Y-%m-%d')
            end_dt = datetime.strptime(end_date, '%Y-%m-%d')

            all_alerts = []
            cache_hits = 0
            cache_misses = 0

            # 遍历每一天
            current_date = start_dt
            while current_date <= end_dt:
                date_str = current_date.strftime('%Y-%m-%d')
                daily_cache_key = f"inventory_alerts_daily_{date_str}"

                # 尝试从缓存获取单天数据
                try:
                    daily_data = cache_manager.get_cached_data(
                        daily_cache_key,
                        get_inventory_alerts,
                        date_str,
                        date_str
                    )

                    # 检查是否是缓存命中（简化统计）
                    if daily_data:
                        cache_hits += 1
                    else:
                        cache_misses += 1

                    # 添加到总结果中
                    if daily_data:
                        all_alerts.extend(daily_data)

                except Exception as e:
                    logger.error(f"获取{date_str}的库存提醒失败: {e}")

                current_date += timedelta(days=1)

            # 去重（基于SKU和售罄日期）
            seen = set()
            unique_alerts = []
            for alert in all_alerts:
                key = (alert.get('SKU', ''), alert.get('售罄日期', ''))
                if key not in seen:
                    seen.add(key)
                    unique_alerts.append(alert)

            logger.info(f"智能缓存统计: 命中{cache_hits}天, 未命中{cache_misses}天, 总计{len(unique_alerts)}条记录")
            return unique_alerts

        # 使用智能缓存策略
        data = get_smart_cached_alerts()

        # 获取所有SKU的店铺信息
        sku_list = [item.get('SKU', '') for item in data if item.get('SKU')]
        sku_shop_info = get_sku_shop_info(sku_list)

        # 为每个库存提醒项目添加店铺SKU信息
        for item in data:
            sku = item.get('SKU', '')
            if sku in sku_shop_info:
                # 有飞书映射的SKU，使用映射信息
                item['shop_skus'] = sku_shop_info[sku]
                item['has_shop_sku'] = True
            else:
                # 没有飞书映射的SKU，不显示店铺SKU
                item['shop_skus'] = []
                item['has_shop_sku'] = False

            # 检查是否有订单（用于颜色标记）
            item['has_orders'] = item.get('历史销量', 0) > 0

        # 按优先级排序：绿色（有订单）→ 橙色（有店铺SKU但无订单）→ 灰色（无店铺SKU且无订单）
        def sort_priority(item):
            has_orders = item.get('has_orders', False)
            has_shop_sku = item.get('has_shop_sku', False)

            if has_orders:
                return 0  # 绿色：有订单，最高优先级
            elif has_shop_sku:
                return 1  # 橙色：有店铺SKU但无订单，中等优先级
            else:
                return 2  # 灰色：无店铺SKU且无订单，最低优先级

        data.sort(key=sort_priority)

        logger.info(f"库存预警API返回{len(data)}条记录，其中{len(sku_shop_info)}个SKU有店铺映射")
        return jsonify(data)

    except Exception as e:
        logger.error(f'库存预警API处理失败: {str(e)}')
        return jsonify({'error': str(e)}), 500

@app.route('/api/stats')
@login_required
def api_stats():
    """仪表板统计API - 混合缓存版本"""
    try:
        # 使用缓存管理器
        stats = cache_manager.get_cached_data(
            'dashboard_stats',
            get_dashboard_stats
        )
        return jsonify(stats)
    except Exception as e:
        logger.error(f'获取统计数据时出错: {str(e)}')
        return jsonify({
            'total_skus': 0,
            'pending_restock': 0,
            'alerts_count': 0
        })

@app.route('/api/dashboard_alerts')
@login_required
@handle_errors
def api_dashboard_alerts():
    """仪表板库存提醒API - 按预计可售天数排序，1小时主缓存，2小时备用缓存"""
    try:
        start_time = time.time()
        # 构建缓存键
        cache_key = "dashboard_alerts"

        logger.info(f"🔍 仪表盘库存提醒API请求开始 - 缓存键: {cache_key}")

        def get_dashboard_alerts_data():
            """获取仪表板库存提醒数据，按预计可售天数排序"""
            query_start_time = time.time()
            logger.info("📊 开始执行仪表盘库存提醒数据库查询...")

            # 获取所有有库存的SKU及其预计可售天数
            query = """
            SELECT
                p.sku,
                p.product_image,
                p.supplier_code,
                COALESCE(o30.sales_30d, 0) as sales_30d,
                COALESCE(o60.sales_60d, 0) as sales_60d,
                COALESCE(i.quantity, 0) as 公共库存,
                COALESCE(i.quantity, 0) + COALESCE(i.sl_quantity, 0) + COALESCE(i.gwyj_quantity, 0) as 总库存,
                i.estimated_restock_date as 预计到货时间,
                i.estimated_restock_qty as 预计到货数量
            FROM products p
            LEFT JOIN (
                SELECT sku, SUM(product_quantity) as sales_30d
                FROM orders
                WHERE order_date >= DATE_SUB(NOW(), INTERVAL 30 DAY)
                GROUP BY sku
            ) o30 ON p.sku = o30.sku
            LEFT JOIN (
                SELECT sku, SUM(product_quantity) as sales_60d
                FROM orders
                WHERE order_date >= DATE_SUB(NOW(), INTERVAL 60 DAY)
                GROUP BY sku
            ) o60 ON p.sku = o60.sku
            LEFT JOIN (
                SELECT
                    sku,
                    date,
                    quantity,
                    sl_quantity,
                    gwyj_quantity,
                    estimated_restock_date,
                    estimated_restock_qty,
                    ROW_NUMBER() OVER (PARTITION BY sku ORDER BY date DESC) as rn
                FROM inventory
                WHERE sku IS NOT NULL AND sku != ''
            ) i ON p.sku = i.sku AND i.rn = 1
            WHERE p.sku IS NOT NULL AND p.sku != ''
            AND (COALESCE(i.quantity, 0) + COALESCE(i.sl_quantity, 0) + COALESCE(i.gwyj_quantity, 0)) > 0
            """

            raw_data = execute_query(query)
            query_time = time.time() - query_start_time
            logger.info(f"📊 数据库查询完成，耗时: {query_time:.2f}秒，返回 {len(raw_data)} 条原始数据")

            # 计算预计可售天数并排序
            processing_start_time = time.time()
            alerts_with_days = []

            for item in raw_data:
                # 计算可用总库存
                total_stock = (item.get('总库存', 0) or 0)
                sales_30d = item.get('sales_30d', 0) or 0
                sales_60d = item.get('sales_60d', 0) or 0
                public_stock = item.get('公共库存', 0) or 0

                # 计算预计可售天数（基于30天销量）
                if sales_30d > 0:
                    estimated_days = round((total_stock * 30) / sales_30d, 1)
                else:
                    estimated_days = float('inf')  # 无销量时设为无穷大

                # 只包含预计可售天数小于等于60天的SKU（2个月内需要补货）
                if estimated_days <= 60:
                    alerts_with_days.append({
                        'SKU': item.get('sku', ''),
                        '近30天销量': sales_30d,
                        '近60天销量': sales_60d,
                        '公共库存': public_stock,
                        '总库存': total_stock,
                        '预计到货时间': item.get('预计到货时间', '') or '',
                        '预计到货数量': item.get('预计到货数量', '') or '',
                        '产品主图': item.get('product_image', '') or '',
                        'supplier_code': item.get('supplier_code', '') or '',
                        'estimated_days': estimated_days
                    })

            # 按预计可售天数排序，天数越小排在前面
            alerts_with_days.sort(key=lambda x: x['estimated_days'])

            # 如果≤60天的SKU少于10个，扩展到≤120天
            if len(alerts_with_days) < 10:
                logger.info(f"≤60天SKU只有{len(alerts_with_days)}个，扩展查询≤120天的SKU")

                # 重新查询≤120天的SKU
                extended_alerts = []
                for item in raw_data:
                    total_stock = (item.get('总库存', 0) or 0)
                    sales_30d = item.get('sales_30d', 0) or 0
                    sales_60d = item.get('sales_60d', 0) or 0
                    public_stock = item.get('公共库存', 0) or 0

                    if sales_30d > 0:
                        estimated_days = round((total_stock * 30) / sales_30d, 1)
                    else:
                        estimated_days = float('inf')

                    # 扩展到120天
                    if estimated_days <= 120:
                        extended_alerts.append({
                            'SKU': item.get('sku', ''),
                            '近30天销量': sales_30d,
                            '近60天销量': sales_60d,
                            '公共库存': public_stock,
                            '总库存': total_stock,
                            '预计到货时间': item.get('预计到货时间', '') or '',
                            '预计到货数量': item.get('预计到货数量', '') or '',
                            '产品主图': item.get('product_image', '') or '',
                            'supplier_code': item.get('supplier_code', '') or '',
                            'estimated_days': estimated_days
                        })

                extended_alerts.sort(key=lambda x: x['estimated_days'])
                result = extended_alerts[:30]  # 扩展时返回30个
                logger.info(f"扩展查询完成，返回{len(result)}个SKU（≤120天）")
            else:
                # 限制返回前30个
                result = alerts_with_days[:30]

            processing_time = time.time() - processing_start_time
            total_time = time.time() - query_start_time
            logger.info(f"📊 数据处理完成，耗时: {processing_time:.2f}秒，总耗时: {total_time:.2f}秒")
            logger.info(f"📊 返回 {len(result)} 个库存提醒，最紧急的预计可售天数: {result[0]['estimated_days'] if result else 'N/A'}")

            return result

        # 使用缓存管理器 - 1小时主缓存，2小时备用缓存
        data = cache_manager.get_cached_data(cache_key, get_dashboard_alerts_data)

        # 记录API响应时间和缓存状态
        total_time = time.time() - start_time
        logger.info(f"✅ 仪表盘库存提醒API完成 - 总耗时: {total_time:.2f}秒，返回 {len(data)} 个提醒")

        return jsonify(data)

    except Exception as e:
        logger.error(f'❌ 获取仪表板库存提醒失败: {str(e)}')
        logger.error(f'❌ 错误详情: {traceback.format_exc()}')
        return jsonify([])  # 返回空数组

@app.route('/api/platform_sales')
@login_required
@optimize_api_response
def api_platform_sales():
    """平台销量API - 混合缓存版本"""
    try:
        # 获取参数
        time_range = request.args.get('time_range', '近30天')
        sku = request.args.get('sku', '')
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')

        # 构建缓存键
        cache_key = f"platform_sales_{time_range}_{sku}_{start_date}_{end_date}"

        # 定义获取平台销量数据的函数
        def get_platform_sales_data():
            logger.info(f'请求平台销量数据 - 时间区间: {time_range}')

            # 构建查询条件
            where_conditions = []
            params = []

            # 时间条件
            if start_date and end_date:
                where_conditions.append("order_date >= %s AND order_date <= %s")
                params.extend([start_date, end_date])
            else:
                # 根据时间范围设置条件
                if time_range == '近30天':
                    where_conditions.append("order_date >= DATE_SUB(NOW(), INTERVAL 30 DAY)")
                elif time_range == '近60天':
                    where_conditions.append("order_date >= DATE_SUB(NOW(), INTERVAL 60 DAY)")
                elif time_range == '近90天':
                    where_conditions.append("order_date >= DATE_SUB(NOW(), INTERVAL 90 DAY)")

            # SKU条件
            if sku:
                where_conditions.append("sku = %s")
                params.append(sku)

            # 构建完整查询
            where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"

            query = f"""
            SELECT platform_channel, SUM(product_quantity) as total_sales, COUNT(*) as order_count
            FROM orders
            WHERE {where_clause}
            GROUP BY platform_channel
            ORDER BY total_sales DESC
            """

            raw_data = execute_query(query, tuple(params))

            # 平台映射函数
            def match_platform(channel):
                if not channel:
                    return '其他'
                channel_str = str(channel).lower()
                if 'amazon' in channel_str:
                    return 'Amazon'
                elif 'ebay' in channel_str:
                    return 'eBay'
                elif 'walmart' in channel_str:
                    return 'Walmart'
                elif 'shopify' in channel_str:
                    return 'Shopify'
                else:
                    return '其他'

            # 处理数据
            platform_data = {}
            total_sales = 0

            for item in raw_data:
                platform = match_platform(item['platform_channel'])
                sales = int(item['total_sales'])

                if platform in platform_data:
                    platform_data[platform] += sales
                else:
                    platform_data[platform] = sales

                total_sales += sales

            # 格式化结果 - 按前端期望的格式返回
            result = {}

            # 为每个平台创建详细数据
            for platform, sales in platform_data.items():
                percentage = round((sales / total_sales) * 100, 1) if total_sales > 0 else 0

                # 获取该平台的店铺详情 - 使用store_account字段显示店铺名称
                shop_query = f"""
                SELECT store_account, SUM(product_quantity) as shop_sales, platform_channel
                FROM orders
                WHERE {where_clause} AND store_account IS NOT NULL AND store_account != ''
                GROUP BY store_account, platform_channel
                ORDER BY shop_sales DESC
                """
                shop_data = execute_query(shop_query, tuple(params))

                # 筛选属于当前平台的店铺
                shops = []
                for shop in shop_data:
                    if match_platform(shop['platform_channel']) == platform:
                        shops.append({
                            'name': shop['store_account'],  # 使用store_account作为店铺名称
                            'sales': int(shop['shop_sales'])
                        })

                result[platform] = {
                    'total_sales': sales,
                    'percentage': percentage,
                    'shops': shops
                }

            logger.info(f'平台销量数据，共{len(result)}个平台')
            return result

        # 使用缓存管理器
        data = cache_manager.get_cached_data(cache_key, get_platform_sales_data)
        return jsonify(data)

    except Exception as e:
        logger.error(f'平台销量API错误: {e}')
        return jsonify({'error': str(e)}), 500



@app.route('/api/cache_info')
@login_required
def api_cache_info():
    """缓存信息API - 混合缓存统计"""
    try:
        stats = cache_manager.get_cache_stats()

        # 获取仪表盘库存提醒的详细缓存状态（仅对EnhancedCacheManager有效）
        dashboard_alerts_status = None
        if hasattr(cache_manager, '_get_cache_detail'):
            dashboard_alerts_status = cache_manager._get_cache_detail('dashboard_alerts')
        else:
            # 对于RedisCacheManager，提供基本的缓存状态信息
            dashboard_alerts_status = {
                'cache_key': 'dashboard_alerts',
                'cache_type': 'Redis',
                'note': 'Redis缓存管理器不支持详细状态查询'
            }

        return jsonify({
            'status': 'enabled',
            'type': 'hybrid_cache',
            'description': '混合缓存架构 - Redis缓存 + 内存兜底' if isinstance(cache_manager, RedisCacheManager) else '内存缓存 + 数据库兜底',
            'timestamp': time.time(),
            'dashboard_alerts_cache': dashboard_alerts_status,
            **stats
        })
    except Exception as e:
        logger.error(f'缓存信息API错误: {e}')
        return jsonify({'error': str(e)}), 500

@app.route('/api/cache_stats')
@login_required
def api_cache_stats():
    """缓存统计API - 返回详细的缓存统计信息"""
    try:
        stats = cache_manager.get_cache_stats()
        return jsonify(stats)
    except Exception as e:
        logger.error(f'获取缓存统计失败: {str(e)}')
        return jsonify({'error': str(e)}), 500

@app.route('/api/db_stats')
@login_required
def api_db_stats():
    """数据库连接统计API"""
    try:
        from database_config import get_global_db_config
        db_config = get_global_db_config()

        stats = {
            'connection_stats': db_config.connection_stats,
            'current_mode': db_config.current_mode,
            'fallback_permanent': db_config.fallback_permanent,
            'pool_config': {
                'primary_pool_size': db_config.primary_config.get('pool_size', 0),
                'fallback_pool_size': db_config.fallback_config.get('pool_size', 0)
            }
        }
        return jsonify(stats)
    except Exception as e:
        logger.error(f'获取数据库统计失败: {str(e)}')
        return jsonify({'error': str(e)}), 500

@app.route('/api/cache_clear', methods=['POST'])
@login_required
def api_cache_clear_new():
    """清除缓存API"""
    try:
        cache_key = request.json.get('cache_key') if request.json else None

        # 清除缓存
        cache_manager.invalidate_cache(cache_key)

        message = f"缓存已清除: {cache_key}" if cache_key else "所有缓存已清除"
        logger.info(message)

        return jsonify({
            'success': True,
            'message': message,
            'timestamp': time.time()
        })

    except Exception as e:
        logger.error(f'清除缓存失败: {e}')
        return jsonify({'error': str(e)}), 500

@app.route('/api/performance_stats')
@login_required
def api_performance_stats():
    """性能统计API"""
    try:
        # 获取缓存统计
        cache_stats = cache_manager.get_cache_stats()

        # 获取数据库统计
        from database_config import get_global_db_config
        db_config = get_global_db_config()
        db_stats = db_config.get_connection_stats()

        # 获取预热统计
        warmup_stats = warmup_manager.get_progress()

        # 组合性能数据
        performance_data = {
            'cache_performance': {
                'hit_rate': cache_stats.get('hit_rate', '0%'),
                'total_requests': cache_stats.get('total_requests', 0),
                'cache_type': 'Redis' if REDIS_AVAILABLE and hasattr(cache_manager, 'redis_client') and cache_manager.redis_client else 'Memory'
            },
            'database_performance': {
                'current_mode': db_stats.get('current_mode', 'unknown'),
                'current_host': db_stats.get('current_host', 'unknown'),
                'total_connections': db_stats.get('total_connections', 0),
                'failed_connections': db_stats.get('failed_connections', 0)
            },
            'warmup_status': {
                'status': warmup_stats.get('status', 'unknown'),
                'completed_tasks': warmup_stats.get('completed_tasks', 0),
                'total_tasks': warmup_stats.get('total_tasks', 0),
                'errors': warmup_stats.get('errors', [])
            },
            'system_info': {
                'redis_available': REDIS_AVAILABLE,
                'feishu_enabled': FEISHU_ENABLED,
                'timestamp': time.time()
            }
        }

        return jsonify(performance_data)

    except Exception as e:
        logger.error(f'获取性能统计失败: {e}')
        return jsonify({'error': str(e)}), 500

# ==================== 静态文件处理 ====================
@app.route('/static/images/placeholder.png')
def placeholder_image():
    """占位图片处理"""
    try:
        return send_from_directory('static/images', 'placeholder.png')
    except Exception as e:
        logger.error(f'占位图片加载失败: {e}')
        # 返回SVG占位图片作为备选
        return send_from_directory('static/images', 'placeholder.svg')

# ==================== 错误处理 ====================
@app.errorhandler(404)
def not_found_error(error):
    """404错误处理"""
    # 如果是API请求，返回JSON
    if request.path.startswith('/api/'):
        return jsonify({
            'error': 'API endpoint not found',
            'path': request.path,
            'message': '请检查API路径是否正确'
        }), 404

    # 如果是静态文件请求，尝试返回占位图片
    if request.path.startswith('/static/images/') and request.path.endswith(('.png', '.jpg', '.jpeg', '.gif')):
        try:
            return send_from_directory('static/images', 'placeholder.png')
        except:
            return send_from_directory('static/images', 'placeholder.svg')

    # 其他情况返回404页面
    return render_template('404.html'), 404

# ================================================================
# 登录路由
# ================================================================

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        user_ip = request.remote_addr
        
        if is_ip_locked(user_ip):
            flash('登录尝试过多，请5分钟后再试！', 'error')
            return render_template('login.html')
        
        username = request.form.get('username', '').strip()
        password = request.form.get('password', '')
        
        if not username or not password:
            flash('请输入用户名和密码！', 'error')
            record_login_attempt(user_ip, success=False)
            return render_template('login.html')
        
        if username in LOGIN_USERS and LOGIN_USERS[username] == password:
            session['logged_in'] = True
            session['username'] = username
            record_login_attempt(user_ip, success=True)
            flash('登录成功！', 'success')
            next_page = request.args.get('next')
            return redirect(next_page) if next_page else redirect(url_for('index'))
        else:
            record_login_attempt(user_ip, success=False)
            flash('用户名或密码错误！', 'error')
    
    return render_template('login.html')

@app.route('/logout')
def logout():
    session.clear()
    flash('已成功退出登录！', 'success')
    return redirect(url_for('login'))

# ================================================================
# 页面路由
# ================================================================

@app.route('/')
@login_required
def index():
    return render_template('dashboard.html')

@app.route('/dashboard')
@login_required
def dashboard():
    return render_template('dashboard.html')

@app.route('/sku')
@app.route('/sku-detail')
@app.route('/sku_detail')
@login_required
def sku_detail():
    return render_template('sku_detail.html')

@app.route('/recommend')
@login_required
def recommend():
    return render_template('recommend.html')

@app.route('/restock')
@login_required
def restock():
    return render_template('restock.html')

@app.route('/alert')
@login_required
def alert():
    return render_template('alert.html')



@app.route('/health')
def health_check():
    """健康检查 - 缓存版本"""
    try:
        # 构建缓存键
        cache_key = "health_check"

        def get_health_status():
            """获取健康状态"""
            # 简单的数据库连接测试
            test_query = "SELECT 1 as test"
            result = execute_query(test_query)
            healthy = len(result) > 0

            return {
                'status': 'healthy' if healthy else 'unhealthy',
                'timestamp': time.time(),
                'database_connection': healthy
            }

        # 使用缓存管理器，短缓存时间（30秒）
        data = cache_manager.get_cached_data(cache_key, get_health_status)
        return jsonify(data)

    except Exception as e:
        return jsonify({
            'status': 'unhealthy',
            'error': str(e)
        }), 500

@app.route('/favicon.ico')
@app.route('/static/favicon.ico')
def favicon():
    return '', 204

# ==================== 缓存管理API ====================

@app.route('/api/clear_sku_cache')
@login_required
def api_clear_sku_cache():
    """清除SKU相关缓存API - 修复数据显示问题"""
    try:
        # 清除SKU详情和销量库存缓存
        keys_to_clear = []
        for key in list(cache_manager.cache.keys()):
            if key.startswith('sku_detail_') or key.startswith('sales_stock_'):
                keys_to_clear.append(key)

        for key in keys_to_clear:
            cache_manager.invalidate_cache(key)

        logger.info(f"🗑️ 手动清除SKU缓存: {len(keys_to_clear)} 个缓存")

        return jsonify({
            'success': True,
            'message': f'已清除 {len(keys_to_clear)} 个缓存',
            'cleared': len(keys_to_clear)
        })
    except Exception as e:
        logger.error(f'清除SKU缓存失败: {str(e)}')
        return jsonify({'error': str(e)}), 500

@app.route('/api/db_connection_status')
@login_required
def api_db_connection_status():
    """数据库连接状态API"""
    try:
        from database_config import DatabaseConfig
        db_config = DatabaseConfig()

        status = db_config.get_connection_status()
        stats = db_config.get_connection_stats()

        return jsonify({
            'connection_status': status,
            'connection_stats': stats,
            'timestamp': time.time()
        })
    except Exception as e:
        logger.error(f'获取数据库连接状态失败: {str(e)}')
        return jsonify({'error': str(e)}), 500

@app.route('/api/cache_preload')
@login_required
def api_cache_preload():
    """预加载缓存API"""
    try:
        # 预热仪表盘相关缓存
        cache_manager.get_cached_data('dashboard_stats', get_dashboard_stats)

        # 预热仪表盘库存提醒缓存
        def get_dashboard_alerts_data():
            """获取仪表板库存提醒数据，按预计可售天数排序"""
            query = """
            SELECT
                p.sku,
                p.product_image,
                p.supplier_code,
                COALESCE(o30.sales_30d, 0) as sales_30d,
                COALESCE(o60.sales_60d, 0) as sales_60d,
                COALESCE(i.quantity, 0) as 公共库存,
                COALESCE(i.quantity, 0) + COALESCE(i.sl_quantity, 0) + COALESCE(i.gwyj_quantity, 0) as 总库存,
                i.estimated_restock_date as 预计到货时间,
                i.estimated_restock_qty as 预计到货数量
            FROM products p
            LEFT JOIN (
                SELECT sku, SUM(product_quantity) as sales_30d
                FROM orders
                WHERE order_date >= DATE_SUB(NOW(), INTERVAL 30 DAY)
                GROUP BY sku
            ) o30 ON p.sku = o30.sku
            LEFT JOIN (
                SELECT sku, SUM(product_quantity) as sales_60d
                FROM orders
                WHERE order_date >= DATE_SUB(NOW(), INTERVAL 60 DAY)
                GROUP BY sku
            ) o60 ON p.sku = o60.sku
            LEFT JOIN (
                SELECT
                    sku,
                    date,
                    quantity,
                    sl_quantity,
                    gwyj_quantity,
                    estimated_restock_date,
                    estimated_restock_qty,
                    ROW_NUMBER() OVER (PARTITION BY sku ORDER BY date DESC) as rn
                FROM inventory
                WHERE sku IS NOT NULL AND sku != ''
            ) i ON p.sku = i.sku AND i.rn = 1
            WHERE p.sku IS NOT NULL AND p.sku != ''
            AND (COALESCE(i.quantity, 0) + COALESCE(i.sl_quantity, 0) + COALESCE(i.gwyj_quantity, 0)) > 0
            """

            raw_data = execute_query(query)

            # 计算预计可售天数并排序
            alerts_with_days = []
            for item in raw_data:
                total_stock = (item.get('总库存', 0) or 0)
                sales_30d = item.get('sales_30d', 0) or 0

                if sales_30d > 0:
                    estimated_days = round((total_stock * 30) / sales_30d, 1)
                else:
                    estimated_days = float('inf')

                if estimated_days <= 60:
                    alerts_with_days.append({
                        'SKU': item.get('sku', ''),
                        '近30天销量': sales_30d,
                        '近60天销量': item.get('sales_60d', 0) or 0,
                        '公共库存': item.get('公共库存', 0) or 0,
                        '总库存': total_stock,
                        '预计到货时间': item.get('预计到货时间', '') or '',
                        '预计到货数量': item.get('预计到货数量', '') or '',
                        '产品主图': item.get('product_image', '') or '',
                        'supplier_code': item.get('supplier_code', '') or '',
                        'estimated_days': estimated_days
                    })

            alerts_with_days.sort(key=lambda x: x['estimated_days'])
            return alerts_with_days[:30]  # 增加到30个

        cache_manager.get_cached_data('dashboard_alerts', get_dashboard_alerts_data)

        logger.info("🔥 仪表盘缓存预加载完成")

        return jsonify({
            'status': 'success',
            'message': '仪表盘缓存预加载已完成',
            'preloaded_caches': ['dashboard_stats', 'dashboard_alerts']
        })
    except Exception as e:
        logger.error(f'预加载缓存失败: {e}')
        return jsonify({'error': str(e)}), 500

# ==================== 简化后台任务 ====================
def enhanced_cache_cleaner():
    """增强缓存清理任务 - 支持双缓存机制"""
    logger.info("🔄 增强缓存清理任务已启动")

    while True:
        try:
            # 等待1分钟（更频繁的维护）
            time.sleep(60)

            # 执行后台维护
            if hasattr(cache_manager, 'background_maintenance'):
                cache_manager.background_maintenance()
                continue  # 跳过旧的清理逻辑

            logger.info("🔄 开始清理过期缓存...")

            # 清理过期的备用缓存
            current_time = time.time()
            expired_main_keys = []
            expired_backup_keys = []

            # 检查主缓存中完全过期的项（连备用TTL都过期了）
            for cache_key, cache_entry in cache_manager.cache.items():
                strategy = cache_manager._get_cache_strategy(cache_key)
                if current_time - cache_entry['timestamp'] > strategy['backup_ttl']:
                    expired_main_keys.append(cache_key)

            # 检查备用缓存中完全过期的项
            for cache_key, cache_entry in cache_manager.backup_cache.items():
                strategy = cache_manager._get_cache_strategy(cache_key)
                if current_time - cache_entry['timestamp'] > strategy['backup_ttl']:
                    expired_backup_keys.append(cache_key)

            # 清理过期缓存
            for key in expired_main_keys:
                if key in cache_manager.cache:
                    del cache_manager.cache[key]

            for key in expired_backup_keys:
                if key in cache_manager.backup_cache:
                    del cache_manager.backup_cache[key]

            total_cleaned = len(expired_main_keys) + len(expired_backup_keys)

            # 获取缓存统计
            stats = cache_manager.get_cache_stats()
            logger.info(f"🗑️ 清理了 {total_cleaned} 个过期缓存")
            logger.info(f"📊 缓存统计: 命中率 {stats['hit_rate']}, 总请求 {stats['total_requests']}, 主缓存 {stats['cache_size']}, 备用缓存 {stats['backup_cache_size']}")

        except Exception as e:
            logger.error(f"❌ 缓存清理失败: {e}")
            time.sleep(300)  # 出错时等待5分钟再重试

# ==================== 预热和更新状态API ====================
@app.route('/api/warmup_progress')
def api_warmup_progress():
    """预热进度API - 不需要登录，用于启动时检查"""
    try:
        progress = warmup_manager.get_progress()
        return jsonify(progress)
    except Exception as e:
        logger.error(f'获取预热进度失败: {str(e)}')
        return jsonify({'error': str(e)}), 500



# ==================== 增强补货日历函数 ====================
def get_enhanced_restock_calendar():
    """获取增强补货数据的独立函数"""
    # 获取基础补货数据
    base_restock_data = get_restock_calendar()
    logger.info(f"获取基础补货数据: {len(base_restock_data)} 天")

    # 使用统一的飞书数据服务
    shop_sku_mapping, platforms, stores = convert_feishu_data_for_restock()
    logger.info(f"转换后的数据 - 平台数: {len(platforms)}, 店铺数: {len(stores)}")

    # 获取平台店铺映射
    platform_store_mapping = {}
    if FEISHU_ENABLED:
        try:
            platform_store_mapping = get_platform_store_mapping_cached()
            logger.info(f"获取到平台店铺映射: {platform_store_mapping}")
        except Exception as e:
            logger.warning(f"获取平台店铺映射失败: {e}")
            platform_store_mapping = {}

    # 如果没有飞书数据，创建临时数据用于测试
    if not shop_sku_mapping:
        logger.warning("没有飞书数据，创建临时测试数据")
        all_skus = set()
        for skus in base_restock_data.values():
            for sku_data in skus:
                if isinstance(sku_data, dict):
                    all_skus.add(sku_data.get('SKU', ''))
                else:
                    all_skus.add(str(sku_data))

        # 创建模拟数据，使用真实平台信息
        shop_sku_mapping = {}
        for sku in all_skus:
            if sku:
                shop_sku_mapping[sku] = [{
                    'shop_sku': f'SHOP-{sku}',
                    'platform': list(platforms)[0] if platforms else 'Amazon',
                    'store': '默认店铺'
                }]

        # 如果没有真实平台数据，使用默认值
        if not platforms:
            platforms = {'Amazon'}
        stores = {'默认店铺'}
        logger.info(f"创建临时数据: {len(shop_sku_mapping)} 个SKU，平台: {platforms}")

    # 收集所有SKU用于获取详细信息
    all_skus = set()
    for skus in base_restock_data.values():
        for sku_data in skus:
            if isinstance(sku_data, dict):
                sku = sku_data.get('SKU', '')
                if sku:
                    all_skus.add(sku)

    logger.info(f"收集到的SKU总数: {len(all_skus)}")
    logger.info(f"前5个SKU示例: {list(all_skus)[:5]}")

    # 使用批量SQL查询获取SKU详细信息 - 高性能版本
    sku_details = {}
    logger.info(f"开始批量查询{len(all_skus)}个SKU的详细信息")

    if all_skus:
        try:
            sku_list = list(all_skus)
            placeholders = ','.join(['%s'] * len(sku_list))

            # 批量查询1：产品基本信息和价格
            logger.info("批量查询产品基本信息...")
            products_query = f"""
            SELECT sku, product_image, Price as price
            FROM products
            WHERE sku IN ({placeholders})
            """
            products_results = execute_query(products_query, sku_list)
            products_data = {row['sku']: row for row in products_results}
            logger.info(f"产品信息查询完成: {len(products_results)} 条记录")

            # 批量查询2：最新库存信息（包含各种库存类型）
            logger.info("批量查询库存信息...")
            inventory_query = f"""
            SELECT
                i.sku,
                COALESCE(i.quantity, 0) as quantity,
                COALESCE(i.account_stock, 0) as account_stock,
                COALESCE(i.sl_quantity, 0) as sl_quantity,
                COALESCE(i.gwyj_quantity, 0) as gwyj_quantity,
                i.estimated_restock_date,
                i.estimated_restock_qty
            FROM inventory i
            INNER JOIN (
                SELECT sku, MAX(date) as max_date
                FROM inventory
                WHERE sku IN ({placeholders})
                GROUP BY sku
            ) latest ON i.sku = latest.sku AND i.date = latest.max_date
            """
            inventory_results = execute_query(inventory_query, sku_list)
            inventory_data = {row['sku']: row for row in inventory_results}
            logger.info(f"库存信息查询完成: {len(inventory_results)} 条记录")

            # 批量查询3：近30天销量信息
            logger.info("批量查询销量信息...")
            sales_query = f"""
            SELECT
                sku,
                SUM(product_quantity) as total_sales
            FROM orders
            WHERE sku IN ({placeholders})
            AND order_date >= DATE_SUB(NOW(), INTERVAL 30 DAY)
            GROUP BY sku
            """
            sales_results = execute_query(sales_query, sku_list)
            sales_data = {row['sku']: row for row in sales_results}
            logger.info(f"销量信息查询完成: {len(sales_results)} 条记录")

            # 组装数据：计算可用总库存等信息
            logger.info("组装SKU详细信息...")
            for sku in sku_list:
                try:
                    product_info = products_data.get(sku, {})
                    inventory_info = inventory_data.get(sku, {})
                    sales_info = sales_data.get(sku, {})

                    # 计算各种库存（复用SKU详情页面的逻辑）
                    quantity = inventory_info.get('quantity', 0) or 0
                    account_stock = inventory_info.get('account_stock', 0) or 0
                    sl_quantity = inventory_info.get('sl_quantity', 0) or 0
                    gwyj_quantity = inventory_info.get('gwyj_quantity', 0) or 0

                    # 公共库存 = 总库存 - 账号内库存
                    public_stock = quantity - account_stock

                    # 可用总库存 = 公共库存 + SL库存 + RW库存 + GW库存
                    total_available_stock = public_stock + sl_quantity + account_stock + gwyj_quantity

                    sku_details[sku] = {
                        'current_stock': total_available_stock,  # 使用可用总库存
                        'estimated_restock_date': inventory_info.get('estimated_restock_date', ''),
                        'estimated_restock_qty': inventory_info.get('estimated_restock_qty', ''),
                        'product_image': product_info.get('product_image', ''),
                        'sales_before_stockout': sales_info.get('total_sales', 0) or 0,
                        'last_sale_date': ''  # 暂时设为空，可以后续优化
                    }

                except Exception as sku_error:
                    logger.error(f"组装SKU {sku} 数据失败: {sku_error}")
                    continue

            logger.info(f"批量查询完成，共处理{len(sku_details)}个SKU")

            # 调试：检查特定SKU的数据
            for test_sku in ['W999125017', 'W3175S00010']:
                if test_sku in sku_details:
                    logger.info(f"{test_sku} SKU数据: {sku_details[test_sku]}")

        except Exception as e:
            logger.error(f"批量查询SKU详细信息失败: {e}")
            sku_details = {}

    # 构建增强数据
    enhanced_data = {}
    for date_str, sku_list in base_restock_data.items():
        enhanced_data[date_str] = []
        for sku_data in sku_list:
            sku = sku_data.get('SKU', '')

            # 创建增强的SKU数据
            enhanced_sku_data = sku_data.copy()

            # 添加店铺信息
            if sku in shop_sku_mapping:
                # 有店铺SKU映射的商品
                enhanced_sku_data['shop_skus'] = shop_sku_mapping[sku]
                enhanced_sku_data['shop_info'] = shop_sku_mapping[sku]  # 兼容前端筛选逻辑
                enhanced_sku_data['has_shop_sku'] = True
                enhanced_sku_data['is_listed'] = True  # 有店铺映射的认为是已上架
            else:
                # 没有店铺SKU映射的商品
                enhanced_sku_data['shop_skus'] = []
                enhanced_sku_data['shop_info'] = []  # 兼容前端筛选逻辑
                enhanced_sku_data['has_shop_sku'] = False
                enhanced_sku_data['is_listed'] = False  # 没有店铺映射的认为是未上架

            # 添加库存详细信息
            if sku in sku_details:
                enhanced_sku_data.update(sku_details[sku])
                # 设置字段别名以兼容前端悬浮卡片
                enhanced_sku_data['arrival_date'] = sku_details[sku].get('estimated_restock_date', '')
                enhanced_sku_data['arrival_quantity'] = sku_details[sku].get('estimated_restock_qty', '')
                # 确保产品图片字段存在 - 优先使用库存查询的图片，然后是基础数据的图片
                if sku_details[sku].get('product_image'):
                    enhanced_sku_data['product_image'] = sku_details[sku]['product_image']
                elif not enhanced_sku_data.get('product_image'):
                    enhanced_sku_data['product_image'] = ''
            else:
                # 设置默认值
                enhanced_sku_data['current_stock'] = 0
                enhanced_sku_data['estimated_restock_date'] = ''
                enhanced_sku_data['estimated_restock_qty'] = ''
                # 保留基础数据中的产品图片（如果有的话）
                if not enhanced_sku_data.get('product_image'):
                    enhanced_sku_data['product_image'] = ''

            enhanced_data[date_str].append(enhanced_sku_data)

    return {
        'calendar_data': enhanced_data,
        'platforms': sorted(list(platforms)),
        'stores': sorted(list(stores)),
        'platform_store_mapping': platform_store_mapping,
        'total_listed_skus': len(shop_sku_mapping),
        'last_updated': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    }

# 注意：增强补货日历API已合并到 /api/restock_calendar?enhanced=true

# ==================== 物流追踪功能 ====================

def truncate_status_text(text, max_length=95):
    """
    截断状态文本以适应数据库字段长度限制
    数据库status字段为VARCHAR(100)，留5个字符缓冲
    """
    if not text:
        return text

    if len(text) <= max_length:
        return text

    return text[:max_length-3] + '...'

def filter_new_tracking_numbers(tracking_items):
    """
    过滤出需要注册的新跟踪单号，避免重复注册
    """
    try:
        # 获取数据库中已存在的跟踪单号
        all_numbers = [item['number'] for item in tracking_items]
        if not all_numbers:
            return tracking_items

        placeholders = ','.join(['%s'] * len(all_numbers))
        check_query = f"""
        SELECT DISTINCT tracking_number
        FROM order_tracking_details
        WHERE tracking_number IN ({placeholders})
        """
        existing_records = execute_query(check_query, all_numbers)

        if existing_records:
            existing_tracking_numbers = {record['tracking_number'] for record in existing_records}
            logger.info(f"📋 数据库中已存在 {len(existing_tracking_numbers)} 个跟踪单号")

            # 筛选出需要注册的新单号
            new_tracking_items = []
            skipped_count = 0
            for item in tracking_items:
                if item['number'] not in existing_tracking_numbers:
                    new_tracking_items.append(item)
                else:
                    skipped_count += 1

            logger.info(f"🆕 需要注册的新跟踪单号: {len(new_tracking_items)} 个")
            logger.info(f"⏭️ 跳过已存在的跟踪单号: {skipped_count} 个")

            return new_tracking_items
        else:
            logger.info("📋 数据库中没有找到已存在的跟踪单号")
            return tracking_items

    except Exception as e:
        logger.error(f"❌ 检查已存在跟踪单号时出错: {e}")
        # 如果检查失败，返回原始列表
        return tracking_items

# 17track API辅助函数
async def process_tracking_numbers(tracking_items):
    """
    注册物流单号并获取追踪信息
    """
    if not TRACK_API_KEY:
        logger.warning('17track API Key未配置，跳过API操作')
        return

    chunk_size = 40
    total_items = len(tracking_items)

    logger.info(f"🚀 开始处理 {total_items} 个物流单号，分 {(total_items + chunk_size - 1) // chunk_size} 批次处理")

    # 步骤0: 检查已存在的跟踪单号，避免重复注册
    logger.info('--- 检查已存在的跟踪单号 ---')
    new_tracking_items = filter_new_tracking_numbers(tracking_items)

    # 步骤1: 注册新的物流单号（如果有的话）
    if new_tracking_items:
        logger.info('--- 开始17track注册 ---')
        for i in range(0, len(new_tracking_items), chunk_size):
            chunk = new_tracking_items[i:i + chunk_size]
            register_payload = []
            for item in chunk:
                payload = {'number': item['number']}
                if item.get('carrier') is not None:
                    payload['carrier'] = item['carrier']
                    payload['auto_detection'] = False
                else:
                    payload['auto_detection'] = True
                register_payload.append(payload)

            # 简化的注册逻辑，避免复杂的重试缩进问题
            try:
                logger.info(f"注册 {len(chunk)} 个物流单号...")
                headers = {
                    '17token': TRACK_API_KEY,
                    'Content-Type': 'application/json',
                }
                response = requests.post(TRACK_REGISTER_URL, headers=headers, json=register_payload, timeout=30)

                if response.status_code == 200:
                    api_response = response.json()
                    logger.info(f'17track注册API响应: {json.dumps(api_response, indent=2, ensure_ascii=False)}')

                    if api_response.get('code') == 0:
                        data = api_response.get('data', {})
                        if isinstance(data, dict):
                            accepted = data.get('accepted', [])
                            rejected = data.get('rejected', [])

                            if accepted:
                                logger.info(f"✅ 成功注册 {len(accepted)} 个物流单号")
                            if rejected:
                                logger.warning(f"❌ 注册被拒绝 {len(rejected)} 个物流单号")
                                for rejected_item in rejected:
                                    if isinstance(rejected_item, dict):
                                        number = rejected_item.get('number')
                                        reason = rejected_item.get('error', {}).get('message', '未知原因')
                                        if number:
                                            status_text = truncate_status_text(f'Registration Rejected: {reason}')
                                            tracking_data = {
                                                'tracking_number': number,
                                                'carrier_code': next((item.get('carrier') for item in chunk if item['number'] == number), None),
                                                'platform': next((item.get('platform', '') for item in chunk if item['number'] == number), ''),
                                                'shop': next((item.get('shop', '') for item in chunk if item['number'] == number), ''),
                                                'status': status_text
                                            }
                                            insert_or_update_tracking(tracking_data)
                else:
                    logger.error(f"17track注册API HTTP错误: {response.status_code}")

            except Exception as e:
                logger.error(f"17track注册API错误: {e}")
                for item in chunk:
                    tracking_data = {
                        'tracking_number': item['number'],
                        'carrier_code': item.get('carrier'),
                        'platform': item.get('platform', ''),
                        'shop': item.get('shop', ''),
                        'status': 'Registration Failed'
                    }
                    insert_or_update_tracking(tracking_data)
    else:
        logger.info("✅ 所有跟踪单号都已存在，跳过注册步骤")


    logger.info('--- 17track注册完成 ---')



    # 步骤2: 获取物流信息
    logger.info('--- 开始获取物流信息 ---')
    all_numbers = [item['number'] for item in tracking_items]

    for i in range(0, len(all_numbers), chunk_size):
        chunk_numbers = all_numbers[i:i + chunk_size]
        get_info_payload = []
        for number in chunk_numbers:
            item = next((ti for ti in tracking_items if ti['number'] == number), None)
            payload = {'number': number}
            if item and item.get('carrier') is not None:
                payload['carrier'] = item['carrier']
            get_info_payload.append(payload)

        # 添加重试机制
        max_retries = 3
        retry_delay = 2

        for retry in range(max_retries):
            try:
                logger.info(f"获取 {len(chunk_numbers)} 个物流单号的信息... (尝试 {retry + 1}/{max_retries})")
                headers = {
                    '17token': TRACK_API_KEY,
                    'Content-Type': 'application/json',
                }
                response = requests.post(TRACK_GET_INFO_URL, headers=headers, json=get_info_payload, timeout=30)

                # 检查HTTP状态码
                if response.status_code != 200:
                    logger.warning(f"17track获取信息API HTTP状态码异常: {response.status_code}")
                    if retry < max_retries - 1:
                        time.sleep(retry_delay)
                        continue
                    else:
                        raise requests.exceptions.RequestException(f"HTTP {response.status_code}")

                # 解析JSON响应
                try:
                    api_response = response.json()
                except json.JSONDecodeError as e:
                    logger.error(f"17track获取信息API JSON解析错误: {response.text}")
                    if retry < max_retries - 1:
                        time.sleep(retry_delay)
                        continue
                    else:
                        raise

                logger.info(f'17track获取信息API响应 (尝试 {retry + 1}): {json.dumps(api_response, indent=2, ensure_ascii=False)}')

                # 更灵活的响应格式处理
                if isinstance(api_response, dict):
                    if api_response.get('code') == 0:
                        data = api_response.get('data', {})
                        if isinstance(data, dict):
                            accepted = data.get('accepted', [])
                            rejected = data.get('rejected', [])

                            # 处理成功获取的物流信息
                            if accepted:
                                logger.info(f"✅ 成功获取 {len(accepted)} 个物流单号的信息")
                                for track_item in accepted:
                                    if not isinstance(track_item, dict):
                                        logger.warning(f"跳过无效的track_item: {track_item}")
                                        continue

                                    tracking_number = track_item.get('number')
                                    if not tracking_number:
                                        logger.warning(f"跳过没有tracking_number的item: {track_item}")
                                        continue

                                    status = 'Unknown'
                                    latest_event = None
                                    full_track_info = None

                                    # 解析物流信息
                                    if track_item.get('track_info'):
                                        full_track_info = track_item['track_info']

                                        # 获取状态信息
                                        if isinstance(full_track_info, dict):
                                            if full_track_info.get('latest_status'):
                                                latest_status = full_track_info['latest_status']
                                                if isinstance(latest_status, dict):
                                                    status = latest_status.get('status', 'Unknown')
                                                else:
                                                    status = str(latest_status)

                                            # 获取最新事件
                                            if full_track_info.get('latest_event'):
                                                latest_event = full_track_info['latest_event']
                                                if not isinstance(latest_event, dict):
                                                    latest_event = None

                                    # 获取现有数据以保留备注和邀评状态
                                    existing = get_tracking_by_number(tracking_number) or {}

                                    tracking_data = {
                                        'tracking_number': tracking_number,
                                        'carrier_code': track_item.get('carrier') or next((item.get('carrier') for item in tracking_items if item['number'] == tracking_number), None),
                                        'platform': existing.get('platform', '') or next((item.get('platform', '') for item in tracking_items if item['number'] == tracking_number), ''),
                                        'shop': existing.get('shop', '') or next((item.get('shop', '') for item in tracking_items if item['number'] == tracking_number), ''),
                                        'status': status,
                                        'latest_event_description': latest_event.get('description', '') if latest_event else '',
                                        'latest_event_location': latest_event.get('location', '') if latest_event else '',
                                        'latest_event_time': latest_event.get('time_iso') if latest_event else None,
                                        'remark': existing.get('remark', ''),
                                        'is_uninvited_review': existing.get('is_uninvited_review', False),
                                        'full_track_info': full_track_info
                                    }

                                    try:
                                        insert_or_update_tracking(tracking_data)
                                        logger.info(f"✅ 更新物流数据: {tracking_number} -> {status}")
                                    except Exception as e:
                                        logger.error(f"❌ 更新物流数据失败 {tracking_number}: {e}")

                            # 处理被拒绝的物流单号
                            if rejected:
                                logger.warning(f"❌ 获取信息被拒绝 {len(rejected)} 个物流单号: {rejected}")
                                for rejected_item in rejected:
                                    if isinstance(rejected_item, dict):
                                        number = rejected_item.get('number')
                                        reason = rejected_item.get('error', {}).get('message', '未知原因')
                                    else:
                                        number = str(rejected_item)
                                        reason = '格式错误'

                                    if number:
                                        existing = get_tracking_by_number(number) or {}

                                        # 使用通用函数截断过长的状态信息
                                        status_text = truncate_status_text(f'Info Rejected: {reason}')

                                        tracking_data = {
                                            'tracking_number': number,
                                            'carrier_code': existing.get('carrier_code'),
                                            'platform': existing.get('platform', ''),
                                            'shop': existing.get('shop', ''),
                                            'status': status_text,
                                            'remark': existing.get('remark', ''),
                                            'is_uninvited_review': existing.get('is_uninvited_review', False)
                                        }
                                        insert_or_update_tracking(tracking_data)

                            break  # 成功处理，跳出重试循环
                        else:
                            logger.warning(f"17track获取信息API data字段格式异常: {type(data)}")
                    else:
                        error_code = api_response.get('code', 'unknown')
                        error_msg = api_response.get('message', '未知错误')
                        logger.warning(f"17track获取信息API返回错误码 {error_code}: {error_msg}")

                        if retry < max_retries - 1:
                            time.sleep(retry_delay)
                            continue
                else:
                    logger.warning(f"17track获取信息API响应格式异常: {type(api_response)}")
                    if retry < max_retries - 1:
                        time.sleep(retry_delay)
                        continue

            except requests.exceptions.Timeout:
                logger.error(f"17track获取信息API超时 (尝试 {retry + 1}/{max_retries})")
                if retry < max_retries - 1:
                    time.sleep(retry_delay)
                    continue
            except requests.exceptions.RequestException as e:
                logger.error(f"17track获取信息API网络错误 (尝试 {retry + 1}/{max_retries}): {e}")
                if retry < max_retries - 1:
                    time.sleep(retry_delay)
                    continue
            except Exception as e:
                logger.error(f"17track获取信息API未知错误 (尝试 {retry + 1}/{max_retries}): {e}")
                if retry < max_retries - 1:
                    time.sleep(retry_delay)
                    continue

        else:
            # 所有重试都失败了
            logger.error(f"17track获取信息API重试 {max_retries} 次后仍然失败")
            for number in chunk_numbers:
                existing = get_tracking_by_number(number) or {}
                tracking_data = {
                    'tracking_number': number,
                    'carrier_code': existing.get('carrier_code'),
                    'platform': existing.get('platform', ''),
                    'shop': existing.get('shop', ''),
                    'status': 'Info Fetch Failed - Max Retries',  # 缩短状态信息
                    'remark': existing.get('remark', ''),
                    'is_uninvited_review': existing.get('is_uninvited_review', False)
                }
                insert_or_update_tracking(tracking_data)

    logger.info('--- 获取物流信息完成 ---')

def read_tracking_numbers_from_excel(file_path):
    """
    从Excel文件读取物流单号
    """
    tracking_items = []
    try:
        workbook = openpyxl.load_workbook(file_path)
        sheet = workbook.active

        for row_index in range(2, sheet.max_row + 1):
            tracking_number = sheet.cell(row=row_index, column=1).value  # A列
            carrier_code = sheet.cell(row=row_index, column=2).value     # B列
            platform = sheet.cell(row=row_index, column=3).value         # C列
            shop = sheet.cell(row=row_index, column=4).value             # D列

            if tracking_number:
                item = {
                    'number': str(tracking_number),
                    'carrier': int(carrier_code) if carrier_code is not None else None,
                    'platform': str(platform) if platform is not None else '',
                    'shop': str(shop) if shop is not None else ''
                }
                tracking_items.append(item)
    except FileNotFoundError:
        logger.error(f"Excel文件未找到: {file_path}")
    except Exception as e:
        logger.error(f"读取Excel文件错误: {e}")

    return tracking_items

def is_long_transit(track_info):
    """判断是否为运输过久（运输时间大于7天）"""
    if not track_info or not isinstance(track_info, dict):
        return False

    status = track_info.get('status', 'Unknown')
    if status not in ['InTransit', 'OutForDelivery', 'InfoReceived']:
        return False

    days = 0
    full_track_info = track_info.get('full_track_info', {})
    if isinstance(full_track_info, str):
        try:
            full_track_info = json.loads(full_track_info)
        except:
            full_track_info = {}

    time_metrics = full_track_info.get('time_metrics', {})

    if time_metrics:
        days = time_metrics.get('days_of_transit', 0) or time_metrics.get('days_after_last_update', 0)
    else:
        # 备用计算：从最新事件时间计算
        latest_event_time = track_info.get('latest_event_time')
        if latest_event_time:
            try:
                from datetime import datetime
                if isinstance(latest_event_time, str):
                    event_time = datetime.fromisoformat(latest_event_time.replace('Z', '+00:00'))
                else:
                    event_time = latest_event_time
                now = datetime.now(event_time.tzinfo) if event_time.tzinfo else datetime.now()
                days = (now - event_time).days
            except:
                days = 0

    return days > 7

# 物流追踪路由
@app.route('/tracking')
@login_required
def tracking():
    """物流追踪页面"""
    return render_template('tracking.html')

@app.route('/api/tracking')
@login_required
@handle_errors
def api_tracking():
    """获取物流追踪数据API"""
    try:
        tracking_data = get_tracking_data()

        # 处理数据格式
        processed_data = {}
        for item in tracking_data:
            tracking_number = item.get('tracking_number', '').strip()

            # 处理full_track_info
            full_track_info = item.get('full_track_info')
            if isinstance(full_track_info, str):
                try:
                    full_track_info = json.loads(full_track_info)
                except:
                    full_track_info = {}

            # 处理latest_event
            latest_event = None
            if item.get('latest_event_description'):
                latest_event = {
                    'description': item.get('latest_event_description', ''),
                    'location': item.get('latest_event_location', ''),
                    'time_iso': item.get('latest_event_time').isoformat() if item.get('latest_event_time') else ''
                }

            # 使用order_number作为key，如果没有tracking_number的话
            key = tracking_number if tracking_number else f"order_{item.get('order_number', '')}"

            processed_data[key] = {
                'order_number': item.get('order_number', ''),
                'tracking_number': tracking_number,
                'status': item.get('status', 'No Tracking'),
                'latestEvent': latest_event,
                'fullTrackInfo': full_track_info,
                'lastUpdated': item.get('updated_at').isoformat() if item.get('updated_at') else '',
                'updated_at': item.get('updated_at').isoformat() if item.get('updated_at') else '',
                'remark': item.get('remark', ''),
                'is_uninvited_review': item.get('is_uninvited_review', False),
                'platform': item.get('platform', ''),
                'platform_id': item.get('platform_id', ''),
                'shop': item.get('shop', ''),
                'order_date': item.get('order_date').isoformat() if item.get('order_date') else ''
            }

        # 创建响应并添加缓存控制头
        response = jsonify(processed_data)
        response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
        response.headers['Pragma'] = 'no-cache'
        response.headers['Expires'] = '0'
        return response
    except Exception as e:
        logger.error(f'物流追踪API错误: {e}')
        return jsonify({'error': '服务器错误'}), 500

@app.route('/webhook', methods=['POST'])
@handle_errors
def webhook():
    """17track webhook处理"""
    try:
        logger.info('收到17track webhook数据:', json.dumps(request.json, indent=2))
        webhook_payload = request.json

        if webhook_payload and webhook_payload.get('data') and webhook_payload['data'].get('trackList'):
            for track_item in webhook_payload['data']['trackList']:
                tracking_number = track_item.get('number')
                status = 'Unknown'
                latest_event = None
                full_track_info = None

                if track_item.get('track_info'):
                    full_track_info = track_item['track_info']
                    if full_track_info.get('latest_status'):
                        status = full_track_info['latest_status'].get('status', 'Unknown')
                    if full_track_info.get('latest_event'):
                        latest_event = full_track_info['latest_event']

                if tracking_number:
                    # 获取现有数据以保留备注和邀评状态
                    existing = get_tracking_by_number(tracking_number)

                    tracking_data = {
                        'tracking_number': tracking_number,
                        'carrier_code': existing.get('carrier_code'),
                        'platform': existing.get('platform', ''),
                        'shop': existing.get('shop', ''),
                        'status': status,
                        'latest_event_description': latest_event.get('description', '') if latest_event else '',
                        'latest_event_location': latest_event.get('location', '') if latest_event else '',
                        'latest_event_time': latest_event.get('time_iso') if latest_event else None,
                        'remark': existing.get('remark', ''),
                        'is_uninvited_review': existing.get('is_uninvited_review', False),
                        'full_track_info': full_track_info
                    }
                    insert_or_update_tracking(tracking_data)
                    logger.info(f"Webhook更新物流数据: {tracking_number}")
                else:
                    logger.warning('Webhook数据缺少tracking_number:', track_item)
        else:
            logger.warning('Webhook数据格式异常或缺少trackList:', webhook_payload)

        return jsonify({'status': 'success'}), 200
    except Exception as e:
        logger.error(f'Webhook处理错误: {e}')
        return jsonify({'error': '服务器错误'}), 500

@app.route('/api/update_local_data', methods=['POST'])
@login_required
@handle_errors
def api_update_local_data():
    """更新本地物流数据API"""
    try:
        data = request.json
        order_number = data.get('orderNumber', '')
        original_tracking_number = data.get('originalTrackingNumber', '').strip()
        new_tracking_number = data.get('newTrackingNumber', '').strip()
        remark = data.get('remark', '')
        is_uninvited_review = data.get('isUninvitedReview', False)

        if not order_number:
            return jsonify({'status': 'error', 'message': '订单号不能为空'}), 400

        # 首先更新orders表
        update_orders_query = """
        UPDATE orders
        SET tracking_number = %s, remark = %s, is_uninvited_review = %s, updated_at = CURRENT_TIMESTAMP
        WHERE order_number = %s
        """
        execute_query(update_orders_query, (new_tracking_number, remark, is_uninvited_review, order_number))

        # 如果有新的跟踪单号，需要处理order_tracking_details表
        if new_tracking_number:
            # 如果原来有跟踪单号且发生变化，更新order_tracking_details表
            if original_tracking_number and original_tracking_number != new_tracking_number:
                # 更新order_tracking_details表中的跟踪单号
                update_tracking_query = """
                UPDATE order_tracking_details
                SET tracking_number = %s, updated_at = CURRENT_TIMESTAMP
                WHERE tracking_number = %s
                """
                execute_query(update_tracking_query, (new_tracking_number, original_tracking_number))
                logger.info(f"跟踪单号更新: {original_tracking_number} -> {new_tracking_number}")

            # 如果原来没有跟踪单号，创建新的order_tracking_details记录
            elif not original_tracking_number:
                # 从orders表获取相关信息
                order_info_query = """
                SELECT platform_channel, store_account FROM orders WHERE order_number = %s
                """
                order_info = execute_query(order_info_query, (order_number,))

                if order_info:
                    platform = order_info[0].get('platform_channel', '')
                    shop = order_info[0].get('store_account', '')

                    # 创建新的tracking记录
                    insert_tracking_query = """
                    INSERT INTO order_tracking_details
                    (order_number, tracking_number, platform_channel, store_account, status, created_at, updated_at)
                    VALUES (%s, %s, %s, %s, 'Pending', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                    ON DUPLICATE KEY UPDATE
                    platform_channel = VALUES(platform_channel),
                    store_account = VALUES(store_account),
                    updated_at = CURRENT_TIMESTAMP
                    """
                    execute_query(insert_tracking_query, (order_number, new_tracking_number, platform, shop))
                    logger.info(f"创建新跟踪记录: {order_number} -> {new_tracking_number}")

        logger.info(f"订单数据更新成功: {order_number}, 跟踪单号='{new_tracking_number}', 备注='{remark}', 邀评={is_uninvited_review}")
        return jsonify({'status': 'success', 'message': '数据更新成功'}), 200

    except Exception as e:
        logger.error(f'更新本地数据错误: {e}')
        return jsonify({'error': '服务器错误'}), 500

@app.route('/api/dashboard_summary')
@login_required
@handle_errors
def api_dashboard_summary():
    """获取物流追踪仪表板统计API"""
    try:
        # 获取基础统计
        summary = get_tracking_dashboard_summary()

        # 计算运输过久数量
        tracking_data = get_tracking_data()
        long_transit_count = 0

        for item in tracking_data:
            if is_long_transit(item):
                long_transit_count += 1

        summary['long_transit_count'] = long_transit_count

        return jsonify(summary)
    except Exception as e:
        logger.error(f'物流仪表板统计API错误: {e}')
        return jsonify({'error': '服务器错误'}), 500

@app.route('/api/upload_tracking_excel', methods=['POST'])
@login_required
@handle_errors
def api_upload_tracking_excel():
    """上传物流追踪Excel文件API"""
    try:
        if 'file' not in request.files:
            return jsonify({'error': '没有文件上传'}), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': '没有选择文件'}), 400

        if file and file.filename.endswith(('.xlsx', '.xls')):
            # 保存临时文件
            import tempfile
            with tempfile.NamedTemporaryFile(delete=False, suffix='.xlsx') as tmp_file:
                file.save(tmp_file.name)

                # 读取Excel数据
                tracking_items = read_tracking_numbers_from_excel(tmp_file.name)

                if tracking_items:
                    # 批量插入到数据库
                    batch_insert_tracking_from_excel(tracking_items)

                    # 异步处理17track API
                    if TRACK_API_KEY:
                        def async_process():
                            import asyncio
                            asyncio.run(process_tracking_numbers(tracking_items))

                        threading.Thread(target=async_process, daemon=True).start()
                        message = f'成功上传 {len(tracking_items)} 个物流单号，正在后台处理17track API'
                    else:
                        message = f'成功上传 {len(tracking_items)} 个物流单号，但17track API未配置'

                    # 删除临时文件
                    os.unlink(tmp_file.name)

                    return jsonify({
                        'status': 'success',
                        'message': message,
                        'count': len(tracking_items)
                    }), 200
                else:
                    os.unlink(tmp_file.name)
                    return jsonify({'error': 'Excel文件中没有找到有效的物流单号'}), 400
        else:
            return jsonify({'error': '请上传Excel文件(.xlsx或.xls)'}), 400

    except Exception as e:
        logger.error(f'上传物流Excel文件错误: {e}')
        return jsonify({'error': '服务器错误'}), 500

@app.route('/api/sync_tracking_from_db', methods=['POST'])
@login_required
@handle_errors
def api_sync_tracking_from_db():
    """从数据库中获取tracking_number并同步17track信息"""
    try:
        # 获取数据库中所有的tracking_number
        tracking_data = get_tracking_data()

        if not tracking_data:
            return jsonify({'status': 'error', 'message': '数据库中没有找到物流单号'}), 400

        # 准备tracking_items格式
        tracking_items = []
        for item in tracking_data:
            tracking_item = {
                'number': item['tracking_number'],
                'carrier': item.get('carrier_code'),
                'platform': item.get('platform', ''),
                'shop': item.get('shop', '')
            }
            tracking_items.append(tracking_item)

        logger.info(f"从数据库获取到 {len(tracking_items)} 个物流单号，开始同步17track信息")

        if TRACK_API_KEY:
            # 异步处理17track API
            def async_process():
                import asyncio
                asyncio.run(process_tracking_numbers(tracking_items))

            threading.Thread(target=async_process, daemon=True).start()
            message = f'开始同步 {len(tracking_items)} 个物流单号的17track信息，请稍后刷新查看结果'
        else:
            return jsonify({'status': 'error', 'message': '17track API Key未配置，无法同步信息'}), 400

        return jsonify({
            'status': 'success',
            'message': message,
            'count': len(tracking_items)
        }), 200

    except Exception as e:
        logger.error(f'同步数据库物流信息错误: {e}')
        return jsonify({'error': '服务器错误'}), 500

@app.route('/api/add_tracking_numbers', methods=['POST'])
@login_required
@handle_errors
def api_add_tracking_numbers():
    """手动添加物流单号到数据库"""
    try:
        data = request.json
        tracking_numbers = data.get('tracking_numbers', [])

        if not tracking_numbers:
            return jsonify({'status': 'error', 'message': '请提供物流单号列表'}), 400

        # 批量插入物流单号到数据库
        added_count = 0
        for tracking_number in tracking_numbers:
            if isinstance(tracking_number, dict):
                # 如果是对象格式 {number: "xxx", carrier: 123, platform: "xxx", shop: "xxx"}
                tracking_data = {
                    'tracking_number': tracking_number.get('number', ''),
                    'carrier_code': tracking_number.get('carrier'),
                    'platform': tracking_number.get('platform', ''),
                    'shop': tracking_number.get('shop', ''),
                    'status': 'Pending'
                }
            else:
                # 如果是字符串格式
                tracking_data = {
                    'tracking_number': str(tracking_number),
                    'carrier_code': None,
                    'platform': '',
                    'shop': '',
                    'status': 'Pending'
                }

            if tracking_data['tracking_number']:
                success = insert_or_update_tracking(tracking_data)
                if success:
                    added_count += 1

        return jsonify({
            'status': 'success',
            'message': f'成功添加 {added_count} 个物流单号',
            'count': added_count
        }), 200

    except Exception as e:
        logger.error(f'添加物流单号错误: {e}')
        return jsonify({'error': '服务器错误'}), 500

@app.route('/api/tracking/webhook', methods=['POST'])
def tracking_webhook():
    """17track webhook接收端点"""
    try:
        # 获取webhook数据
        webhook_data = request.get_json()

        if not webhook_data:
            logger.warning("收到空的webhook数据")
            return jsonify({'status': 'error', 'message': 'No data received'}), 400

        logger.info(f"收到17track webhook数据: {webhook_data}")

        # 验证webhook签名（如果17track提供）
        # signature = request.headers.get('X-17track-Signature')
        # if not verify_webhook_signature(request.data, signature):
        #     return jsonify({'status': 'error', 'message': 'Invalid signature'}), 401

        # 处理webhook数据
        processed_count = process_tracking_webhook(webhook_data)

        logger.info(f"Webhook处理完成，更新了 {processed_count} 个物流单号")

        return jsonify({
            'status': 'success',
            'message': f'Processed {processed_count} tracking updates',
            'timestamp': time.time()
        })

    except Exception as e:
        logger.error(f"处理17track webhook失败: {e}")
        return jsonify({'status': 'error', 'message': str(e)}), 500

def process_tracking_webhook(webhook_data):
    """处理17track webhook数据"""
    try:
        processed_count = 0

        # 17track webhook数据格式通常包含tracking数组
        trackings = webhook_data.get('data', {}).get('tracking', [])

        if not trackings:
            # 尝试其他可能的数据格式
            trackings = webhook_data.get('tracking', [])
            if not trackings and 'number' in webhook_data:
                # 单个追踪号格式
                trackings = [webhook_data]

        for tracking_info in trackings:
            tracking_number = tracking_info.get('number')
            if not tracking_number:
                continue

            # 更新数据库中的物流信息
            success = update_tracking_from_webhook(tracking_number, tracking_info)
            if success:
                processed_count += 1

                # 发送实时更新到前端
                send_realtime_tracking_update(tracking_number, tracking_info)

        return processed_count

    except Exception as e:
        logger.error(f"处理webhook数据失败: {e}")
        return 0

def update_tracking_from_webhook(tracking_number, tracking_info):
    """从webhook数据更新物流信息 - 更新orders表"""
    try:
        # 提取关键信息
        status = tracking_info.get('status', '')
        latest_event = None

        # 提取最新事件
        if 'track' in tracking_info and tracking_info['track'].get('events'):
            events = tracking_info['track']['events']
            if events:
                latest_event_data = events[0]  # 通常第一个是最新的
                latest_event = {
                    'description': latest_event_data.get('description', ''),
                    'location': latest_event_data.get('location', ''),
                    'time_iso': latest_event_data.get('time_iso', ''),
                    'sub_status': latest_event_data.get('sub_status', '')
                }

        # 更新数据库 - 使用orders表
        update_query = """
        UPDATE orders
        SET status = %s,
            latest_event_description = %s,
            latest_event_location = %s,
            latest_event_time = %s,
            full_track_info = %s,
            updated_at = NOW()
        WHERE tracking_number = %s
        """

        import json
        result = execute_query(update_query, (
            status,
            latest_event.get('description', '') if latest_event else '',
            latest_event.get('location', '') if latest_event else '',
            latest_event.get('time_iso', '') if latest_event else None,
            json.dumps(tracking_info),
            tracking_number
        ))

        logger.info(f"Webhook更新物流信息成功: {tracking_number}")
        return True

    except Exception as e:
        logger.error(f"更新物流信息失败 {tracking_number}: {e}")
        return False

def send_realtime_tracking_update(tracking_number, tracking_info):
    """发送实时更新到前端（WebSocket或Server-Sent Events）"""
    try:
        # 这里可以实现WebSocket推送或Server-Sent Events
        # 暂时记录日志，后续可以扩展
        logger.info(f"实时更新推送: {tracking_number} - {tracking_info.get('status', '')}")

        # TODO: 实现WebSocket推送
        # socketio.emit('tracking_update', {
        #     'tracking_number': tracking_number,
        #     'status': tracking_info.get('status', ''),
        #     'latest_event': tracking_info.get('track', {}).get('events', [{}])[0] if tracking_info.get('track', {}).get('events') else None
        # })

    except Exception as e:
        logger.error(f"发送实时更新失败: {e}")

@app.route('/api/tracking/events')
@login_required
def tracking_events():
    """Server-Sent Events端点，用于实时推送物流更新"""
    def event_stream():
        """生成SSE事件流"""
        try:
            # 发送初始连接确认
            yield f"data: {json.dumps({'type': 'connected', 'message': '实时更新已连接'})}\n\n"

            # 这里可以实现实时事件推送逻辑
            # 暂时每30秒发送一次心跳
            import time
            while True:
                time.sleep(30)
                yield f"data: {json.dumps({'type': 'heartbeat', 'timestamp': time.time()})}\n\n"

        except GeneratorExit:
            logger.info("SSE连接已断开")

    return Response(
        event_stream(),
        mimetype='text/event-stream',
        headers={
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'Access-Control-Allow-Origin': '*'
        }
    )

# 全局变量存储SSE连接（生产环境建议使用Redis等）
sse_connections = set()

def broadcast_tracking_update(tracking_number, tracking_info):
    """广播物流更新到所有SSE连接"""
    try:
        update_data = {
            'type': 'tracking_update',
            'tracking_number': tracking_number,
            'status': tracking_info.get('status', ''),
            'timestamp': time.time()
        }

        # 提取最新事件
        if 'track' in tracking_info and tracking_info['track'].get('events'):
            events = tracking_info['track']['events']
            if events:
                update_data['latest_event'] = {
                    'description': events[0].get('description', ''),
                    'location': events[0].get('location', ''),
                    'time_iso': events[0].get('time_iso', '')
                }

        message = f"data: {json.dumps(update_data)}\n\n"

        # 这里可以实现向所有连接的客户端广播
        # 暂时记录日志
        logger.info(f"广播物流更新: {message.strip()}")

    except Exception as e:
        logger.error(f"广播物流更新失败: {e}")

if __name__ == '__main__':
    logger.info("🚀 启动KKUGUAN库存管理系统 - 优化版 (混合缓存架构)")
    logger.info("🔥 简化缓存管理器已初始化")

    # 检查预热模式（用于调试）
    import os
    skip_warmup = os.getenv('SKIP_WARMUP', 'true').lower() == 'true'  # 默认跳过预热
    quick_warmup = os.getenv('QUICK_WARMUP', 'false').lower() == 'true'

    if skip_warmup:
        logger.info("🚫 跳过预热 - 快速启动模式")
    elif quick_warmup:
        logger.info("⚡ 快速预热模式 - 仅预热基础数据")
        # 只预热最基础的数据
        def quick_warmup_task():
            try:
                quick_warmup_manager = EnhancedCacheWarmupManager()
                quick_warmup_manager.warmup_sku_list()
                quick_warmup_manager.warmup_dashboard_stats()
                quick_warmup_manager.warmup_dashboard_alerts()
                logger.info("⚡ 快速预热完成")
            except Exception as e:
                logger.error(f"快速预热失败: {e}")

        threading.Thread(target=quick_warmup_task, daemon=True).start()
    else:
        # 启动完整应用预热
        start_application_warmup()

    # 启动后台缓存清理线程（延迟启动）
    def delayed_cache_cleaner():
        import time
        time.sleep(10)  # 延迟10秒启动
        try:
            enhanced_cache_cleaner()
        except Exception as e:
            logger.error(f"缓存清理任务启动失败: {e}")

    cleaner_thread = threading.Thread(target=delayed_cache_cleaner, daemon=True)
    cleaner_thread.start()
    logger.info("🔄 增强缓存清理任务已安排延迟启动")

    # 检查是否为生产环境
    is_production = os.environ.get('FLASK_ENV') == 'production'

    # 从环境变量获取端口配置
    port = int(os.getenv('PORT', 5002))
    host = os.getenv('HOST', '0.0.0.0')

    logger.info(f"🌐 准备启动Flask应用: {host}:{port}")
    logger.info(f"🔧 调试模式: {'关闭' if is_production else '开启'}")

    if is_production:
        # 生产环境：关闭调试模式，减少进程
        app.run(host=host, port=port, debug=False, threaded=True)
    else:
        # 开发环境：保持调试模式
        app.run(host=host, port=port, debug=True)
