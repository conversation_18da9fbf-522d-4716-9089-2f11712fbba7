#!/bin/bash
# 服务器健康检查和testdisk卸载脚本

echo "🚀 服务器健康检查开始..."
echo "=================================="

# 1. 系统基本信息
echo "📊 系统信息:"
echo "操作系统: $(uname -a)"
echo "当前时间: $(date)"
echo "运行时间: $(uptime)"
echo

# 2. 磁盘空间检查
echo "💾 磁盘空间使用情况:"
df -h
echo

# 3. 内存使用情况
echo "🧠 内存使用情况:"
free -h
echo

# 4. CPU使用情况
echo "⚡ CPU使用情况:"
top -bn1 | head -20
echo

# 5. 检查MySQL进程
echo "🗄️  MySQL进程状态:"
ps aux | grep mysql | grep -v grep
echo

# 6. 检查MySQL服务状态
echo "🔧 MySQL服务状态:"
if command -v systemctl &> /dev/null; then
    systemctl status mysql || systemctl status mysqld
elif command -v service &> /dev/null; then
    service mysql status || service mysqld status
else
    echo "无法检查MySQL服务状态"
fi
echo

# 7. 检查网络连接
echo "🌐 网络连接状态:"
netstat -tlnp | grep :3306
echo

# 8. 检查磁盘I/O
echo "📈 磁盘I/O状态:"
if command -v iostat &> /dev/null; then
    iostat -x 1 1
else
    echo "iostat未安装，跳过I/O检查"
fi
echo

# 9. 检查系统日志错误
echo "📝 系统日志错误 (最近10条):"
if [ -f /var/log/syslog ]; then
    tail -10 /var/log/syslog | grep -i error
elif [ -f /var/log/messages ]; then
    tail -10 /var/log/messages | grep -i error
else
    echo "未找到系统日志文件"
fi
echo

# 10. 检查MySQL错误日志
echo "🗄️  MySQL错误日志 (最近10条):"
mysql_error_log=$(mysql -e "SHOW VARIABLES LIKE 'log_error';" 2>/dev/null | grep log_error | awk '{print $2}')
if [ -n "$mysql_error_log" ] && [ -f "$mysql_error_log" ]; then
    tail -10 "$mysql_error_log"
else
    # 尝试常见的MySQL错误日志位置
    for log_path in /var/log/mysql/error.log /var/log/mysqld.log /var/lib/mysql/$(hostname).err; do
        if [ -f "$log_path" ]; then
            echo "找到MySQL错误日志: $log_path"
            tail -10 "$log_path"
            break
        fi
    done
fi
echo

# 11. 检查testdisk是否安装
echo "🔍 检查testdisk安装状态:"
if command -v testdisk &> /dev/null; then
    echo "✅ testdisk已安装"
    testdisk --version
    
    echo
    echo "🗑️  开始卸载testdisk..."
    
    # 检测包管理器并卸载
    if command -v apt-get &> /dev/null; then
        echo "使用apt-get卸载testdisk..."
        sudo apt-get remove --purge testdisk -y
        sudo apt-get autoremove -y
    elif command -v yum &> /dev/null; then
        echo "使用yum卸载testdisk..."
        sudo yum remove testdisk -y
    elif command -v dnf &> /dev/null; then
        echo "使用dnf卸载testdisk..."
        sudo dnf remove testdisk -y
    elif command -v zypper &> /dev/null; then
        echo "使用zypper卸载testdisk..."
        sudo zypper remove testdisk -y
    else
        echo "❌ 无法识别包管理器，请手动卸载testdisk"
    fi
    
    # 验证卸载
    echo
    echo "🔍 验证testdisk卸载状态:"
    if command -v testdisk &> /dev/null; then
        echo "⚠️  testdisk仍然存在，可能需要手动删除"
        which testdisk
    else
        echo "✅ testdisk已成功卸载"
    fi
else
    echo "ℹ️  testdisk未安装"
fi
echo

# 12. 检查临时文件和缓存
echo "🧹 检查临时文件:"
echo "临时目录大小:"
du -sh /tmp 2>/dev/null || echo "无法访问/tmp"
du -sh /var/tmp 2>/dev/null || echo "无法访问/var/tmp"

echo
echo "MySQL临时文件:"
find /tmp -name "MY*" -type f 2>/dev/null | head -10

echo

# 13. 检查进程和资源使用
echo "🔄 高资源使用进程:"
ps aux --sort=-%cpu | head -10

echo
echo "=================================="
echo "✅ 服务器检查完成"
echo
echo "💡 建议操作:"
echo "1. 如果磁盘空间不足，清理临时文件"
echo "2. 如果MySQL连接有问题，检查配置和权限"
echo "3. 如果系统负载高，考虑优化或重启服务"
echo "4. 定期监控系统资源使用情况"
