#!/usr/bin/env python3
"""
检查MySQL配置、索引和用户信息
"""

import os
import sys
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 添加当前目录到Python路径
sys.path.append('.')

def check_mysql_user_and_config():
    """检查MySQL用户配置"""
    print("🔍 检查MySQL用户配置...")
    
    # 从环境变量检查
    print("\n📋 环境变量配置:")
    db_user = os.getenv('DB_USER', 'Seller')
    db_password = os.getenv('DB_PASSWORD', '98c06z27W@')
    db_host_primary = os.getenv('DB_HOST_PRIMARY', '*************')
    db_host_fallback = os.getenv('DB_HOST_FALLBACK', '127.0.0.1')
    db_name = os.getenv('DB_NAME', 'kkuguan_db')
    
    print(f"   数据库用户: {db_user}")
    print(f"   数据库名称: {db_name}")
    print(f"   主数据库主机: {db_host_primary}")
    print(f"   备用数据库主机: {db_host_fallback}")
    print(f"   密码: {'*' * len(db_password) if db_password else '未设置'}")
    
    # 从代码配置检查
    print("\n📋 代码中的配置:")
    try:
        from database_config import DatabaseConfig
        db_config = DatabaseConfig()
        
        print(f"   主配置主机: {db_config.primary_config.get('host', 'N/A')}")
        print(f"   主配置用户: {db_config.primary_config.get('user', 'N/A')}")
        print(f"   主配置数据库: {db_config.primary_config.get('database', 'N/A')}")
        
        if hasattr(db_config, 'fallback_config'):
            print(f"   备用配置主机: {db_config.fallback_config.get('host', 'N/A')}")
            print(f"   备用配置用户: {db_config.fallback_config.get('user', 'N/A')}")
    except Exception as e:
        print(f"   ❌ 无法读取代码配置: {e}")

def check_mysql_indexes():
    """检查MySQL索引"""
    print("\n🔍 检查MySQL索引...")
    
    try:
        from database_config import execute_query
        
        # 检查主要表的索引
        main_tables = ['products', 'inventory', 'orders', 'tracking']
        
        for table in main_tables:
            print(f"\n📊 {table} 表的索引:")
            try:
                # 显示表的索引
                index_query = f"SHOW INDEX FROM {table}"
                indexes = execute_query(index_query)
                
                if indexes:
                    print(f"   共有 {len(indexes)} 个索引:")
                    current_index = None
                    for idx in indexes:
                        index_name = idx.get('Key_name', 'N/A')
                        column_name = idx.get('Column_name', 'N/A')
                        is_unique = "唯一" if idx.get('Non_unique', 1) == 0 else "非唯一"
                        
                        if index_name != current_index:
                            print(f"   - {index_name} ({is_unique}): {column_name}")
                            current_index = index_name
                        else:
                            print(f"     + {column_name}")
                else:
                    print("   ⚠️ 没有找到索引")
                    
            except Exception as e:
                if "doesn't exist" in str(e).lower():
                    print(f"   ⚠️ 表 {table} 不存在")
                else:
                    print(f"   ❌ 查询索引失败: {e}")
        
        # 检查索引使用情况
        print(f"\n📈 索引使用统计:")
        try:
            stats_query = """
            SELECT 
                TABLE_SCHEMA,
                TABLE_NAME,
                INDEX_NAME,
                CARDINALITY,
                NULLABLE
            FROM information_schema.STATISTICS 
            WHERE TABLE_SCHEMA = DATABASE()
            ORDER BY TABLE_NAME, INDEX_NAME
            """
            stats = execute_query(stats_query)
            
            if stats:
                current_table = None
                for stat in stats:
                    table_name = stat.get('TABLE_NAME', 'N/A')
                    index_name = stat.get('INDEX_NAME', 'N/A')
                    cardinality = stat.get('CARDINALITY', 0)
                    
                    if table_name != current_table:
                        print(f"\n   📋 {table_name}:")
                        current_table = table_name
                    
                    print(f"      {index_name}: 基数={cardinality}")
            else:
                print("   ⚠️ 无法获取索引统计信息")
                
        except Exception as e:
            print(f"   ❌ 获取索引统计失败: {e}")
            
    except Exception as e:
        print(f"❌ 检查索引失败: {e}")

def check_table_structure():
    """检查表结构"""
    print("\n🔍 检查主要表结构...")
    
    try:
        from database_config import execute_query
        
        main_tables = ['products', 'inventory', 'orders']
        
        for table in main_tables:
            print(f"\n📋 {table} 表结构:")
            try:
                desc_query = f"DESCRIBE {table}"
                columns = execute_query(desc_query)
                
                if columns:
                    print("   字段名 | 类型 | 是否为空 | 键 | 默认值")
                    print("   " + "-" * 50)
                    for col in columns:
                        field = col.get('Field', 'N/A')
                        type_info = col.get('Type', 'N/A')
                        null_info = col.get('Null', 'N/A')
                        key_info = col.get('Key', '')
                        default_info = col.get('Default', '')
                        
                        key_symbol = ""
                        if key_info == 'PRI':
                            key_symbol = "🔑"
                        elif key_info == 'MUL':
                            key_symbol = "📇"
                        elif key_info == 'UNI':
                            key_symbol = "🔒"
                        
                        print(f"   {field} | {type_info} | {null_info} | {key_symbol} | {default_info}")
                else:
                    print("   ⚠️ 无法获取表结构")
                    
            except Exception as e:
                if "doesn't exist" in str(e).lower():
                    print(f"   ⚠️ 表 {table} 不存在")
                else:
                    print(f"   ❌ 查询表结构失败: {e}")
                    
    except Exception as e:
        print(f"❌ 检查表结构失败: {e}")

def suggest_index_optimizations():
    """建议索引优化"""
    print("\n💡 索引优化建议:")
    print("1. 常用查询字段应该有索引:")
    print("   - products表: sku, supplier_code")
    print("   - inventory表: sku, date")
    print("   - orders表: sku, order_date, platform")
    print("   - tracking表: tracking_number, status")
    
    print("\n2. 复合索引建议:")
    print("   - products: (sku, supplier_code)")
    print("   - inventory: (sku, date)")
    print("   - orders: (sku, order_date)")
    
    print("\n3. 索引维护:")
    print("   - 定期检查索引使用情况")
    print("   - 删除未使用的索引")
    print("   - 监控查询性能")

def main():
    """主函数"""
    print("🚀 MySQL配置和索引检查工具")
    print("=" * 60)
    
    # 检查用户配置
    check_mysql_user_and_config()
    
    # 检查索引
    check_mysql_indexes()
    
    # 检查表结构
    check_table_structure()
    
    # 建议优化
    suggest_index_optimizations()
    
    print("\n" + "=" * 60)
    print("✅ 检查完成")

if __name__ == '__main__':
    main()
