#!/bin/bash
# KKUGUAN项目清理脚本
# 清理临时文件、缓存文件和日志文件

echo "🧹 开始清理KKUGUAN项目..."

# 清理Python缓存
echo "🗑️  清理Python缓存文件..."
find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null
find . -name "*.pyc" -delete 2>/dev/null
find . -name "*.pyo" -delete 2>/dev/null

# 清理临时文件
echo "🗑️  清理临时文件..."
find . -name "*~" -delete 2>/dev/null
find . -name "*.tmp" -delete 2>/dev/null
find . -name "*.bak" -delete 2>/dev/null

# 清理日志文件（保留app.log但清空内容）
echo "🗑️  清理日志文件..."
if [ -f "app.log" ]; then
    > app.log
    echo "   ✅ app.log已清空"
fi

# 清理可能的测试文件
echo "🗑️  清理测试文件..."
rm -f test_*.py check_*.py db_*.py direct_*.py fix_*.py server_*.sh 2>/dev/null

echo "✅ 清理完成！"
echo
echo "📋 保留的核心文件:"
echo "   - app.py (主应用)"
echo "   - database_config.py (数据库配置)"
echo "   - feishu_*.py (飞书集成)"
echo "   - start_*.py (启动脚本)"
echo "   - templates/ (模板文件)"
echo "   - static/ (静态资源)"
echo "   - utils/ (工具函数)"
echo "   - 配置文件 (requirements.txt, nginx.conf等)"
