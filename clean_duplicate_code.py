#!/usr/bin/env python3
"""
清理app.py中的重复代码
"""

def clean_duplicate_code():
    """清理重复代码"""
    
    # 读取文件
    with open('app.py', 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # 找到第一个正确的函数定义位置
    clean_lines = []
    in_duplicate_section = False
    
    for i, line in enumerate(lines):
        # 检查是否到达重复代码开始位置
        if i >= 3982 and not in_duplicate_section:
            # 跳过重复代码，直到找到正确的路由定义
            if '@app.route(' in line and '/api/tracking' in line:
                in_duplicate_section = False
                clean_lines.append(line)
            else:
                continue
        else:
            clean_lines.append(line)
    
    # 写回文件
    with open('app.py', 'w', encoding='utf-8') as f:
        f.writelines(clean_lines)
    
    print("✅ 重复代码清理完成")

if __name__ == '__main__':
    clean_duplicate_code()
