#!/bin/bash

# KKUGUAN 库存管理系统部署脚本
# 使用方法: ./deploy.sh [production|staging|development]

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查环境参数
ENVIRONMENT=${1:-production}
if [[ ! "$ENVIRONMENT" =~ ^(production|staging|development)$ ]]; then
    log_error "无效的环境参数: $ENVIRONMENT"
    log_info "使用方法: ./deploy.sh [production|staging|development]"
    exit 1
fi

log_info "开始部署 KKUGUAN 库存管理系统 - 环境: $ENVIRONMENT"

# 检查必要的文件
log_info "检查部署文件..."
required_files=("Dockerfile" "docker-compose.yml" "requirements.txt" "app.py" "wsgi.py")
for file in "${required_files[@]}"; do
    if [[ ! -f "$file" ]]; then
        log_error "缺少必要文件: $file"
        exit 1
    fi
done
log_success "所有必要文件检查通过"

# 检查环境变量文件
if [[ ! -f ".env" ]]; then
    log_warning "未找到 .env 文件"
    if [[ -f ".env.example" ]]; then
        log_info "复制 .env.example 到 .env"
        cp .env.example .env
        log_warning "请编辑 .env 文件并填入正确的配置值"
        log_info "编辑完成后重新运行部署脚本"
        exit 1
    else
        log_error "未找到 .env.example 文件"
        exit 1
    fi
fi

# 检查Docker
if ! command -v docker &> /dev/null; then
    log_error "Docker 未安装，请先安装 Docker"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    log_error "Docker Compose 未安装，请先安装 Docker Compose"
    exit 1
fi

# 检查Docker服务
if ! docker info &> /dev/null; then
    log_error "Docker 服务未运行，请启动 Docker 服务"
    exit 1
fi

log_success "Docker 环境检查通过"

# 创建必要的目录
log_info "创建必要的目录..."
mkdir -p logs ssl data
log_success "目录创建完成"

# 停止现有容器
log_info "停止现有容器..."
docker-compose down --remove-orphans || true

# 构建镜像
log_info "构建应用镜像..."
docker-compose build --no-cache

# 启动服务
log_info "启动服务..."
if [[ "$ENVIRONMENT" == "production" ]]; then
    docker-compose up -d
else
    docker-compose up -d
fi

# 等待服务启动
log_info "等待服务启动..."
sleep 10

# 健康检查
log_info "执行健康检查..."
max_attempts=30
attempt=1

while [[ $attempt -le $max_attempts ]]; do
    if curl -f http://localhost:5002/health &> /dev/null; then
        log_success "应用健康检查通过"
        break
    else
        log_info "健康检查失败，等待重试... ($attempt/$max_attempts)"
        sleep 5
        ((attempt++))
    fi
done

if [[ $attempt -gt $max_attempts ]]; then
    log_error "健康检查失败，部署可能有问题"
    log_info "查看应用日志:"
    docker-compose logs kkuguan-app
    exit 1
fi

# 显示部署信息
log_success "🎉 KKUGUAN 库存管理系统部署成功!"
echo ""
log_info "部署信息:"
echo "  环境: $ENVIRONMENT"
echo "  访问地址: http://localhost:5002"
echo "  健康检查: http://localhost:5002/health"
echo ""
log_info "常用命令:"
echo "  查看日志: docker-compose logs -f"
echo "  重启服务: docker-compose restart"
echo "  停止服务: docker-compose down"
echo "  查看状态: docker-compose ps"
echo ""
log_info "默认登录信息:"
echo "  用户名: admin"
echo "  密码: admin123"
