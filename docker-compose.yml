version: '3.8'

services:
  kkuguan-app:
    build: .
    container_name: kkuguan-app
    restart: unless-stopped
    ports:
      - "5002:5002"
    environment:
      # Flask配置
      - FLASK_ENV=production
      - SECRET_KEY=${SECRET_KEY:-your-secret-key-here}
      
      # 数据库配置
      - DB_HOST=${DB_HOST:-*************}
      - DB_USER=${DB_USER:-Seller}
      - DB_PASSWORD=${DB_PASSWORD}
      - DB_NAME=${DB_NAME:-kkuguan_db}
      
      # 飞书API配置
      - FEISHU_APP_ID=${FEISHU_APP_ID}
      - FEISHU_APP_SECRET=${FEISHU_APP_SECRET}
      - FEISHU_SPREADSHEET_TOKEN=${FEISHU_SPREADSHEET_TOKEN}
      
      # Redis配置
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_DB=0

      # 应用配置
      - SKIP_WARMUP=${SKIP_WARMUP:-false}
      - QUICK_WARMUP=${QUICK_WARMUP:-false}
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
    networks:
      - kkuguan-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5002/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Redis服务
  redis:
    image: redis:7-alpine
    container_name: kkuguan-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    networks:
      - kkuguan-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: kkuguan-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx_docker.conf:/etc/nginx/nginx.conf:ro
      - ./proxy_params:/etc/nginx/proxy_params:ro
      - ./static:/app/static:ro
      - ./ssl:/etc/nginx/ssl:ro
      - nginx_cache:/var/cache/nginx
      - nginx_logs:/var/log/nginx
    depends_on:
      kkuguan-app:
        condition: service_healthy
    networks:
      - kkuguan-network
    healthcheck:
      test: ["CMD", "nginx", "-t"]
      interval: 30s
      timeout: 10s
      retries: 3

networks:
  kkuguan-network:
    driver: bridge

volumes:
  logs:
  data:
  redis_data:
  nginx_cache:
  nginx_logs:
