"""
飞书API配置文件
请根据实际情况修改配置信息
"""

import os
from feishu_api import init_feishu_client

# 飞书应用配置
FEISHU_CONFIG = {
    # 飞书应用ID（从飞书开发者后台获取）
    "app_id": os.getenv("FEISHU_APP_ID", "cli_a8bfa17e98ff5013"),

    # 飞书应用密钥（从飞书开发者后台获取）
    "app_secret": os.getenv("FEISHU_APP_SECRET", "NPc5L89gxoHjHormGATNMeJHWsz0aqpU"),

    # 飞书表格Token（从表格URL中获取）
    "spreadsheet_token": os.getenv("FEISHU_SPREADSHEET_TOKEN", "XsU4sD1n9hgQaDt4UZ3cFOuXnme"),

    # 表格类型：sheets（普通表格）或 bitable（多维表格）
    "table_type": "sheets"
}

def setup_feishu():
    """设置飞书API"""
    app_id = FEISHU_CONFIG["app_id"]
    app_secret = FEISHU_CONFIG["app_secret"]
    spreadsheet_token = FEISHU_CONFIG["spreadsheet_token"]
    table_type = FEISHU_CONFIG["table_type"]

    # 检查配置是否完整
    if (app_id == "your_app_id_here" or
        app_secret == "your_app_secret_here" or
        spreadsheet_token == "your_spreadsheet_token_here"):
        print("⚠️  飞书API配置未完成，请在 feishu_config.py 中配置正确的参数")
        print("   或通过环境变量设置：")
        print("   export FEISHU_APP_ID=your_app_id")
        print("   export FEISHU_APP_SECRET=your_app_secret")
        print("   export FEISHU_SPREADSHEET_TOKEN=your_spreadsheet_token")
        return False

    # 初始化飞书客户端
    init_feishu_client(app_id, app_secret, spreadsheet_token, table_type)
    print("✅ 飞书API配置完成（普通表格模式）")
    return True

# 缓存配置
FEISHU_CACHE_CONFIG = {
    # SKU映射缓存时间（秒）
    "sku_mapping_cache_ttl": 3600,  # 1小时
    
    # 是否启用飞书集成
    "enable_feishu": True,
}
