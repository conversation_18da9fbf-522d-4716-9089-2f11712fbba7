#!/usr/bin/env python3
"""
17track问题诊断脚本
分析为什么很多跟踪单号获取不到物流信息
"""

import os
import requests
import json
import time
from datetime import datetime

# 配置
TRACK_API_KEY = os.getenv('TRACK_API_KEY') or os.getenv('API_KEY') or 'TEST_API_KEY_FOR_DIAGNOSIS'
TRACK_REGISTER_URL = 'https://api.17track.net/track/v2.2/register'
TRACK_GET_INFO_URL = 'https://api.17track.net/track/v2.2/gettrackinfo'

def check_api_configuration():
    """检查API配置"""
    print("🔧 检查17track API配置")
    print("=" * 50)
    
    if not TRACK_API_KEY:
        print("❌ TRACK_API_KEY 未配置")
        print("   请设置环境变量 TRACK_API_KEY 或 API_KEY")
        return False
    else:
        print(f"✅ TRACK_API_KEY 已配置: {TRACK_API_KEY[:10]}...")
        
    print(f"📡 注册URL: {TRACK_REGISTER_URL}")
    print(f"📡 获取信息URL: {TRACK_GET_INFO_URL}")
    return True

def test_api_connectivity():
    """测试API连通性"""
    print("\n🌐 测试API连通性")
    print("=" * 50)
    
    # 测试注册API
    test_payload = [{"number": "TEST123456789", "auto_detection": True}]
    headers = {
        '17token': TRACK_API_KEY,
        'Content-Type': 'application/json',
    }
    
    try:
        print("📤 测试注册API...")
        response = requests.post(TRACK_REGISTER_URL, headers=headers, json=test_payload, timeout=10)
        print(f"   HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"   响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
                
                # 分析响应
                if data.get('code') == 0:
                    print("   ✅ 注册API正常")
                elif data.get('code') == 4031:
                    print("   ⚠️ API密钥无效或已过期")
                elif data.get('code') == 4032:
                    print("   ⚠️ API配额不足")
                else:
                    print(f"   ⚠️ API返回错误码: {data.get('code')}")
                    
            except json.JSONDecodeError:
                print(f"   ❌ JSON解析失败: {response.text}")
        else:
            print(f"   ❌ HTTP错误: {response.status_code}")
            print(f"   响应: {response.text}")
            
    except requests.exceptions.Timeout:
        print("   ❌ 请求超时")
    except requests.exceptions.ConnectionError:
        print("   ❌ 连接错误")
    except Exception as e:
        print(f"   ❌ 未知错误: {e}")

def test_get_info_api():
    """测试获取信息API"""
    print("\n📋 测试获取信息API")
    print("=" * 50)
    
    # 使用一些常见的测试跟踪号
    test_numbers = [
        {"number": "1Z999AA1234567890", "carrier": "ups"},  # UPS测试号
        {"number": "TEST123456789"},  # 通用测试号
        {"number": "9400111899562537624095"}  # USPS测试号
    ]
    
    headers = {
        '17token': TRACK_API_KEY,
        'Content-Type': 'application/json',
    }
    
    try:
        print("📤 测试获取信息API...")
        response = requests.post(TRACK_GET_INFO_URL, headers=headers, json=test_numbers, timeout=15)
        print(f"   HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"   响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
                
                # 分析响应
                if data.get('code') == 0:
                    print("   ✅ 获取信息API正常")
                    
                    # 分析数据结构
                    api_data = data.get('data', {})
                    if isinstance(api_data, dict):
                        accepted = api_data.get('accepted', [])
                        rejected = api_data.get('rejected', [])
                        
                        print(f"   📊 成功获取: {len(accepted)} 个")
                        print(f"   📊 获取失败: {len(rejected)} 个")
                        
                        if rejected:
                            print("   ❌ 失败原因:")
                            for item in rejected[:3]:  # 只显示前3个
                                if isinstance(item, dict):
                                    number = item.get('number', 'Unknown')
                                    error = item.get('error', {})
                                    message = error.get('message', 'Unknown error')
                                    print(f"      {number}: {message}")
                                    
                elif data.get('code') == 4031:
                    print("   ⚠️ API密钥无效或已过期")
                elif data.get('code') == 4032:
                    print("   ⚠️ API配额不足")
                else:
                    print(f"   ⚠️ API返回错误码: {data.get('code')}")
                    print(f"   错误信息: {data.get('message', 'Unknown')}")
                    
            except json.JSONDecodeError:
                print(f"   ❌ JSON解析失败: {response.text}")
        else:
            print(f"   ❌ HTTP错误: {response.status_code}")
            print(f"   响应: {response.text}")
            
    except requests.exceptions.Timeout:
        print("   ❌ 请求超时")
    except requests.exceptions.ConnectionError:
        print("   ❌ 连接错误")
    except Exception as e:
        print(f"   ❌ 未知错误: {e}")

def analyze_common_issues():
    """分析常见问题"""
    print("\n🔍 常见问题分析")
    print("=" * 50)
    
    issues = [
        {
            "问题": "API密钥问题",
            "症状": "所有请求都失败",
            "解决方案": [
                "检查API密钥是否正确配置",
                "确认API密钥是否已过期",
                "检查API密钥权限设置"
            ]
        },
        {
            "问题": "API配额不足",
            "症状": "部分请求成功，部分失败",
            "解决方案": [
                "检查17track账户余额",
                "升级API套餐",
                "优化API调用频率"
            ]
        },
        {
            "问题": "跟踪号格式问题",
            "症状": "特定跟踪号总是失败",
            "解决方案": [
                "检查跟踪号格式是否正确",
                "确认承运商代码是否匹配",
                "使用auto_detection自动识别"
            ]
        },
        {
            "问题": "网络连接问题",
            "症状": "间歇性连接失败",
            "解决方案": [
                "检查服务器网络连接",
                "增加重试机制",
                "调整超时设置"
            ]
        },
        {
            "问题": "17track服务问题",
            "症状": "API返回5xx错误",
            "解决方案": [
                "等待17track服务恢复",
                "联系17track技术支持",
                "实施降级策略"
            ]
        }
    ]
    
    for i, issue in enumerate(issues, 1):
        print(f"{i}. {issue['问题']}")
        print(f"   症状: {issue['症状']}")
        print("   解决方案:")
        for solution in issue['解决方案']:
            print(f"   - {solution}")
        print()

def provide_recommendations():
    """提供改进建议"""
    print("\n💡 改进建议")
    print("=" * 50)
    
    recommendations = [
        "增加更详细的错误日志记录",
        "实施指数退避重试策略",
        "添加API配额监控",
        "实现批量处理优化",
        "添加备用数据源",
        "实施缓存机制减少API调用",
        "添加健康检查端点",
        "实现异步处理提高性能"
    ]
    
    for i, rec in enumerate(recommendations, 1):
        print(f"{i}. {rec}")

def main():
    """主函数"""
    print("🔍 17track问题诊断工具")
    print("=" * 60)
    print(f"诊断时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 检查配置
    if not check_api_configuration():
        print("\n❌ 配置检查失败，请先配置API密钥")
        return
    
    # 测试连通性
    test_api_connectivity()
    
    # 测试获取信息API
    test_get_info_api()
    
    # 分析常见问题
    analyze_common_issues()
    
    # 提供建议
    provide_recommendations()
    
    print("\n" + "=" * 60)
    print("🎯 诊断完成")
    print("\n如果问题仍然存在，请:")
    print("1. 检查17track账户状态和余额")
    print("2. 联系17track技术支持")
    print("3. 查看应用详细日志")

if __name__ == '__main__':
    main()
