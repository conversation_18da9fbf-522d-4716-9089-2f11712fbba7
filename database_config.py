import mysql.connector
import pandas as pd
import os
import json
import decimal
from typing import Optional, Dict, Any
import logging
from contextlib import contextmanager
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def convert_decimal(obj):
    """转换Decimal类型为float，用于JSON序列化"""
    if isinstance(obj, decimal.Decimal):
        return float(obj)
    elif isinstance(obj, list):
        return [convert_decimal(item) for item in obj]
    elif isinstance(obj, dict):
        return {key: convert_decimal(value) for key, value in obj.items()}
    return obj

class DatabaseConfig:
    def __init__(self):
        # 检测运行环境
        is_docker = os.path.exists('/.dockerenv')
        is_cloud_server = os.getenv('FLASK_ENV') == 'production'

        # 云服务器部署时的数据库配置
        if is_cloud_server or is_docker:
            # 云服务器上，优先使用本地MySQL，然后是远程MySQL
            primary_host = os.getenv('DB_HOST_PRIMARY', '127.0.0.1')
            fallback_host = os.getenv('DB_HOST_FALLBACK', '*************')
        else:
            # 开发环境，优先使用远程MySQL
            primary_host = os.getenv('DB_HOST_PRIMARY', '*************')
            fallback_host = os.getenv('DB_HOST_FALLBACK', '127.0.0.1')

        # MySQL数据库配置 - 智能连接策略
        self.primary_config = {
            'host': primary_host,
            'user': os.getenv('DB_USER', 'Seller'),
            'password': os.getenv('DB_PASSWORD', '98c06z27W@'),
            'database': os.getenv('DB_NAME', 'kkuguan_db'),
            'charset': 'utf8mb4',
            'port': int(os.getenv('DB_PORT', '3306')),
            'connection_timeout': 10,   # 增加超时时间
            'sql_mode': 'STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO',
            'use_unicode': True,
            'buffered': True,
            'raise_on_warnings': False,
            'get_warnings': False,
            'autocommit': True,
            'auth_plugin': 'mysql_native_password',
        }

        self.fallback_config = {
            'host': fallback_host,
            'user': os.getenv('DB_USER', 'Seller'),
            'password': os.getenv('DB_PASSWORD', '98c06z27W@'),
            'database': os.getenv('DB_NAME', 'kkuguan_db'),
            'charset': 'utf8mb4',
            'port': int(os.getenv('DB_PORT', '3306')),
            'connection_timeout': 30,  # 远程连接超时时间长
            'sql_mode': 'STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO',
            'use_unicode': True,
            'buffered': True,
            'raise_on_warnings': False,
            'get_warnings': False,
            'autocommit': True,
            'auth_plugin': 'mysql_native_password',
        }

        logger.info(f"数据库配置初始化 - 主连接: {primary_host}, 备用连接: {fallback_host}")

        # 当前使用的配置
        self.config = self.primary_config.copy()
        self.current_mode = 'primary'  # 'primary' 或 'fallback'
        self.fallback_permanent = False  # 是否永久使用备用连接

        # 连接池统计
        self.connection_stats = {
            'total_connections': 0,
            'active_connections': 0,
            'failed_connections': 0,
            'connection_timeouts': 0
        }

        # 连接限制
        self.max_concurrent_connections = 3  # 最大并发连接数
        self.connection_semaphore = None

    def get_connection(self, retry_count=3):
        """获取数据库连接 - 带并发限制的连接管理"""
        import threading

        # 初始化信号量（只初始化一次）
        if self.connection_semaphore is None:
            self.connection_semaphore = threading.Semaphore(self.max_concurrent_connections)

        # 获取连接许可
        if not self.connection_semaphore.acquire(blocking=False):
            logger.warning("⚠️ 达到最大并发连接数限制，等待连接释放...")
            self.connection_semaphore.acquire()  # 阻塞等待

        try:
            # 如果已经永久切换到备用连接，直接使用备用连接
            if self.fallback_permanent:
                return self._attempt_connection(self.fallback_config, retry_count)

            # 如果当前是fallback模式，直接使用备用连接
            if self.current_mode == 'fallback':
                return self._attempt_connection(self.fallback_config, retry_count)

            # 尝试主连接（本地连接）
            connection = self._attempt_connection(self.primary_config, retry_count)
            if connection:
                return connection

            # 主连接失败，永久切换到备用连接
            logger.warning("⚠️ 本地数据库连接失败，切换到远程连接...")
            self.current_mode = 'fallback'
            self.config = self.fallback_config.copy()
            self.fallback_permanent = True  # 标记为永久使用备用连接

            connection = self._attempt_connection(self.fallback_config, retry_count)
            if connection:
                logger.info("✅ 远程数据库连接成功，将永久使用远程连接")
                return connection
            else:
                logger.error("❌ 远程数据库连接也失败")
                return None
        finally:
            # 如果连接失败，释放信号量
            if 'connection' not in locals() or not connection:
                self.connection_semaphore.release()



    def _attempt_connection(self, config, retry_count):
        """尝试连接指定配置 - 简化连接管理，不使用连接池"""
        import time
        import random

        for attempt in range(retry_count):
            try:
                # 添加随机延迟，避免同时大量连接
                if attempt > 0:
                    delay = random.uniform(0.5, 2.0)
                    time.sleep(delay)

                self.connection_stats['total_connections'] += 1

                # 直接连接，不使用连接池
                connection = mysql.connector.connect(**config)
                self.connection_stats['active_connections'] += 1

                # 测试连接是否有效
                if connection.is_connected():
                    return connection
                else:
                    connection.close()
                    raise mysql.connector.Error("连接无效")

            except mysql.connector.Error as err:
                self.connection_stats['failed_connections'] += 1
                if attempt < retry_count - 1:
                    wait_time = (attempt + 1) * 1  # 缩短等待时间
                    logger.warning(f"数据库连接失败 (尝试 {attempt + 1}/{retry_count}): {err}，{wait_time}秒后重试...")
                    time.sleep(wait_time)
                else:
                    logger.error(f"数据库连接最终失败: {err}")
                    return None
            except Exception as e:
                self.connection_stats['connection_timeouts'] += 1
                if attempt < retry_count - 1:
                    wait_time = (attempt + 1) * 1
                    logger.warning(f"数据库连接超时 (尝试 {attempt + 1}/{retry_count}): {e}，{wait_time}秒后重试...")
                    time.sleep(wait_time)
                else:
                    logger.error(f"数据库连接最终超时: {e}")
                    return None

        return None

    def get_connection_stats(self):
        """获取连接池统计信息"""
        stats = self.connection_stats.copy()
        stats['current_mode'] = self.current_mode
        stats['current_host'] = self.config.get('host', 'unknown')
        stats['primary_host'] = self.primary_config.get('host', 'unknown')
        stats['fallback_host'] = self.fallback_config.get('host', 'unknown')
        return stats

    def get_connection_status(self):
        """获取当前连接状态"""
        return {
            'current_mode': self.current_mode,
            'current_host': self.config.get('host', 'unknown'),
            'primary_host': self.primary_config.get('host', 'unknown'),
            'fallback_host': self.fallback_config.get('host', 'unknown'),
            'fallback_permanent': self.fallback_permanent,
            'description': '永久使用远程连接' if self.fallback_permanent else ('使用本地连接' if self.current_mode == 'primary' else '临时使用远程连接')
        }

    def reset_connection_stats(self):
        """重置连接池统计"""
        self.connection_stats = {
            'total_connections': 0,
            'active_connections': 0,
            'failed_connections': 0,
            'connection_timeouts': 0
        }

    @contextmanager
    def get_connection_context(self):
        """获取数据库连接的上下文管理器，确保连接正确关闭"""
        connection = None
        try:
            connection = self.get_connection()
            if connection:
                yield connection
            else:
                yield None
        except mysql.connector.Error as err:
            logger.error(f"数据库连接失败: {err}")
            yield None
        finally:
            if connection and connection.is_connected():
                try:
                    connection.close()
                    self.connection_stats['active_connections'] = max(0, self.connection_stats['active_connections'] - 1)
                    # 释放信号量
                    if self.connection_semaphore:
                        self.connection_semaphore.release()
                except:
                    pass
            elif self.connection_semaphore:
                # 如果连接失败，也要释放信号量
                self.connection_semaphore.release()
    
    def create_database(self):
        """创建数据库"""
        try:
            # 先连接到MySQL服务器（不指定数据库）
            temp_config = self.config.copy()
            del temp_config['database']
            connection = mysql.connector.connect(**temp_config)
            cursor = connection.cursor()
            
            # 创建数据库
            cursor.execute(f"CREATE DATABASE IF NOT EXISTS {self.config['database']} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
            logger.info(f"数据库 {self.config['database']} 创建成功")
            
            cursor.close()
            connection.close()
            return True
        except mysql.connector.Error as err:
            logger.error(f"创建数据库失败: {err}")
            return False
    
    def create_tables(self):
        """创建数据表 - 统一使用英文字段名"""
        connection = self.get_connection()
        if not connection:
            return False

        cursor = connection.cursor()

        try:
            # 创建订单数据表 - 英文字段
            orders_table = """
            CREATE TABLE IF NOT EXISTS orders (
                id INT AUTO_INCREMENT PRIMARY KEY,
                platform_code VARCHAR(255),
                order_number VARCHAR(255),
                sku VARCHAR(255),
                asin VARCHAR(255),
                product_name TEXT,
                product_price DECIMAL(10,2),
                product_total INT,
                product_quantity INT,
                multi_product_name VARCHAR(255),
                order_amount DECIMAL(10,2),
                shipping_fee DECIMAL(10,2),
                order_date DATETIME,
                platform_channel VARCHAR(255),
                shop_account VARCHAR(255),
                product_sku VARCHAR(255),
                supplier VARCHAR(255),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_sku (sku),
                INDEX idx_order_date (order_date),
                INDEX idx_platform (platform_channel)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """

            # 创建库存信息表 - 英文字段，添加缺少的字段
            inventory_table = """
            CREATE TABLE IF NOT EXISTS inventory (
                id INT AUTO_INCREMENT PRIMARY KEY,
                date DATE,
                sku VARCHAR(255),
                quantity INT,
                account_stock INT,
                account_inventory INT,
                sl_quantity INT DEFAULT 0,
                gwyj_quantity INT DEFAULT 0,
                estimated_restock_date VARCHAR(255),
                estimated_restock_qty VARCHAR(255),
                estimated_restock_quantity INT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_sku (sku),
                INDEX idx_date (date)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """
            
            # 创建产品详细信息表 - 英文字段，添加Price字段
            products_table = """
            CREATE TABLE IF NOT EXISTS products (
                id INT AUTO_INCREMENT PRIMARY KEY,
                supplier_code VARCHAR(255),
                shop_name VARCHAR(255),
                giga_index DECIMAL(10,2),
                seller_type VARCHAR(255),
                support_stock VARCHAR(10),
                sku VARCHAR(255),
                product_name TEXT,
                product_group VARCHAR(255),
                is_combo VARCHAR(10),
                sub_product VARCHAR(255),
                package_count INT,
                length_inch DECIMAL(10,2),
                width_inch DECIMAL(10,2),
                height_inch DECIMAL(10,2),
                weight_pound DECIMAL(10,2),
                available_stock INT,
                in_transit_stock VARCHAR(255),
                estimated_arrival_time VARCHAR(255),
                unit_price_usd DECIMAL(10,2),
                discount_rate DECIMAL(10,2),
                discount_price_usd DECIMAL(10,2),
                promotion_end_time VARCHAR(255),
                shipping_fee_usd DECIMAL(10,2),
                total_price_usd DECIMAL(10,2),
                amazon_shipping_fee_usd DECIMAL(10,2),
                amazon_total_price_usd DECIMAL(10,2),
                shipping_fee_min_usd DECIMAL(10,2),
                shipping_fee_max_usd DECIMAL(10,2),
                total_price_min_usd DECIMAL(10,2),
                total_price_max_usd DECIMAL(10,2),
                complex_transaction VARCHAR(255),
                first_stock_date DATE,
                material_download_count INT,
                return_rate VARCHAR(255),
                shipping_method VARCHAR(255),
                notes TEXT,
                product_category TEXT,
                upc VARCHAR(255),
                color VARCHAR(255),
                material VARCHAR(255),
                origin_country VARCHAR(255),
                map_price_usd DECIMAL(10,2),
                restricted_platforms VARCHAR(255),
                product_image TEXT,
                product_description TEXT,
                Price DECIMAL(10,2),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_sku (sku),
                INDEX idx_supplier (supplier_code),
                INDEX idx_product_group (product_group)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """
            
            # 创建物流追踪表
            tracking_table = """
            CREATE TABLE IF NOT EXISTS tracking (
                id INT AUTO_INCREMENT PRIMARY KEY,
                tracking_number VARCHAR(255) NOT NULL UNIQUE,
                carrier_code INT,
                platform VARCHAR(255),
                shop VARCHAR(255),
                status VARCHAR(100) DEFAULT 'Pending',
                latest_event_description TEXT,
                latest_event_location VARCHAR(255),
                latest_event_time DATETIME,
                remark TEXT,
                is_uninvited_review BOOLEAN DEFAULT FALSE,
                full_track_info JSON,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_tracking_number (tracking_number),
                INDEX idx_status (status),
                INDEX idx_platform (platform),
                INDEX idx_shop (shop),
                INDEX idx_updated_at (updated_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """

            # 创建订单物流追踪详情表
            order_tracking_details_table = """
            CREATE TABLE IF NOT EXISTS order_tracking_details (
                id INT AUTO_INCREMENT PRIMARY KEY,
                tracking_number VARCHAR(255) NOT NULL UNIQUE,
                carrier_code VARCHAR(50),
                platform_channel VARCHAR(255),
                store_account VARCHAR(255),
                status VARCHAR(100) DEFAULT 'Pending',
                latest_event_description TEXT,
                latest_event_location VARCHAR(255),
                latest_event_time DATETIME,
                remark TEXT,
                is_uninvited_review BOOLEAN DEFAULT FALSE,
                full_track_info JSON,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_tracking_number (tracking_number),
                INDEX idx_status (status),
                INDEX idx_platform_channel (platform_channel),
                INDEX idx_store_account (store_account),
                INDEX idx_updated_at (updated_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """

            cursor.execute(orders_table)
            cursor.execute(inventory_table)
            cursor.execute(products_table)
            cursor.execute(tracking_table)
            cursor.execute(order_tracking_details_table)

            logger.info("数据表创建成功")
            return True
            
        except mysql.connector.Error as err:
            logger.error(f"创建数据表失败: {err}")
            return False
        finally:
            cursor.close()
            connection.close()

    def update_table_structure(self):
        """更新数据表结构 - 添加缺少的字段"""
        connection = self.get_connection()
        if not connection:
            return False

        cursor = connection.cursor()

        try:
            # 更新inventory表，添加缺少的字段（兼容MySQL 5.7）
            alter_queries = [
                "ALTER TABLE inventory ADD COLUMN account_stock INT DEFAULT 0",
                "ALTER TABLE inventory ADD COLUMN sl_quantity INT DEFAULT 0",
                "ALTER TABLE inventory ADD COLUMN gwyj_quantity INT DEFAULT 0",
                "ALTER TABLE inventory MODIFY COLUMN estimated_restock_date VARCHAR(255)",
                "ALTER TABLE inventory ADD COLUMN estimated_restock_qty VARCHAR(255)",

                # 更新products表，添加Price字段
                "ALTER TABLE products ADD COLUMN Price DECIMAL(10,2)",

                # 为orders表添加物流追踪相关字段
                "ALTER TABLE orders ADD COLUMN carrier_code VARCHAR(50)",
                "ALTER TABLE orders ADD COLUMN status VARCHAR(50) DEFAULT 'Pending'",
                "ALTER TABLE orders ADD COLUMN latest_event_description TEXT",
                "ALTER TABLE orders ADD COLUMN latest_event_location VARCHAR(255)",
                "ALTER TABLE orders ADD COLUMN latest_event_time DATETIME",
                "ALTER TABLE orders ADD COLUMN remark TEXT",
                "ALTER TABLE orders ADD COLUMN is_uninvited_review BOOLEAN DEFAULT FALSE",
                "ALTER TABLE orders ADD COLUMN full_track_info JSON",
                "ALTER TABLE orders ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"
            ]

            # 创建物流追踪表（如果不存在）
            tracking_table_create = """
            CREATE TABLE IF NOT EXISTS tracking (
                id INT AUTO_INCREMENT PRIMARY KEY,
                tracking_number VARCHAR(255) NOT NULL UNIQUE,
                carrier_code INT,
                platform VARCHAR(255),
                shop VARCHAR(255),
                status VARCHAR(100) DEFAULT 'Pending',
                latest_event_description TEXT,
                latest_event_location VARCHAR(255),
                latest_event_time DATETIME,
                remark TEXT,
                is_uninvited_review BOOLEAN DEFAULT FALSE,
                full_track_info JSON,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_tracking_number (tracking_number),
                INDEX idx_status (status),
                INDEX idx_platform (platform),
                INDEX idx_shop (shop),
                INDEX idx_updated_at (updated_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """

            # 先创建物流追踪表
            try:
                cursor.execute(tracking_table_create)
                logger.info("物流追踪表创建/检查完成")
            except mysql.connector.Error as err:
                logger.warning(f"物流追踪表创建失败: {err}")

            for query in alter_queries:
                try:
                    cursor.execute(query)
                    logger.info(f"表结构更新成功: {query}")
                except mysql.connector.Error as err:
                    if "Duplicate column name" in str(err) or "check that column" in str(err):
                        logger.info(f"字段已存在，跳过: {query}")
                    else:
                        logger.warning(f"表结构更新失败: {query}, 错误: {err}")

            connection.commit()
            logger.info("数据表结构更新完成")
            return True

        except mysql.connector.Error as err:
            logger.error(f"更新数据表结构失败: {err}")
            return False
        finally:
            cursor.close()
            connection.close()

def import_csv_to_mysql(csv_file: str, table_name: str, database_config: DatabaseConfig):
    """将CSV文件导入到MySQL数据库"""
    try:
        # 读取CSV文件
        logger.info(f"开始读取CSV文件: {csv_file}")
        
        # 根据文件大小选择不同的读取方式
        if os.path.getsize(csv_file) > 50 * 1024 * 1024:  # 大于50MB
            # 分块读取大文件
            chunk_size = 10000
            df_iterator = pd.read_csv(csv_file, chunksize=chunk_size, encoding='utf-8', low_memory=False)
        else:
            df = pd.read_csv(csv_file, encoding='utf-8', low_memory=False)
            df_iterator = [df]
        
        connection = database_config.get_connection()
        if not connection:
            return False
        
        cursor = connection.cursor()
        
        total_rows = 0
        
        for chunk in df_iterator:
            # 获取目标表的字段名
            if table_name == 'products':
                cursor.execute(f"DESCRIBE {table_name}")
                table_columns = set([row[0] for row in cursor.fetchall()])
            else:
                table_columns = set(chunk.columns)

            # 清理数据
            chunk = chunk.fillna('')
            
            # 简化的列名映射 - 假设CSV已经使用英文字段名
            # 只保留数据库表中存在的字段
            available_columns = [col for col in chunk.columns if col in table_columns]
            chunk = chunk[available_columns]
            
            # 处理日期字段 - 英文字段名
            date_columns = ['order_date', 'date', 'estimated_restock_date', 'first_stock_date']
            for col in date_columns:
                if col in chunk.columns:
                    chunk[col] = pd.to_datetime(chunk[col], errors='coerce')

            # 处理数值字段，将空字符串转换为0或NULL - 英文字段名
            numeric_columns = ['product_price', 'product_total', 'product_quantity', 'order_amount', 'shipping_fee',
                             'quantity', 'account_inventory', 'estimated_restock_quantity', 'giga_index', 'package_count',
                             'length_inch', 'width_inch', 'height_inch', 'weight_pound',
                             'available_stock', 'material_download_count']
            
            for col in numeric_columns:
                if col in chunk.columns:
                    # 将空字符串转换为0
                    chunk[col] = chunk[col].replace('', 0)
                    # 转换为数值类型，错误值设为0
                    chunk[col] = pd.to_numeric(chunk[col], errors='coerce').fillna(0)
            
            # 构建INSERT语句
            columns = ', '.join([f'`{col}`' for col in chunk.columns])
            placeholders = ', '.join(['%s'] * len(chunk.columns))
            
            insert_query = f"INSERT INTO {table_name} ({columns}) VALUES ({placeholders})"
            
            # 执行批量插入
            values = [tuple(row) for row in chunk.values]
            cursor.executemany(insert_query, values)
            
            total_rows += len(chunk)
            logger.info(f"已导入 {total_rows} 行数据")
        
        connection.commit()
        cursor.close()
        connection.close()
        
        logger.info(f"CSV文件 {csv_file} 导入完成，共导入 {total_rows} 行数据")
        return True
        
    except Exception as e:
        logger.error(f"导入CSV文件失败: {e}")
        return False

# 全局数据库配置实例 - 保持连接状态
_global_db_config = None

def get_global_db_config():
    """获取全局数据库配置实例"""
    global _global_db_config
    if _global_db_config is None:
        _global_db_config = DatabaseConfig()
    return _global_db_config

def execute_query(query: str, params: tuple = None, retry_count: int = 3) -> list:
    """执行查询并返回结果 - 使用连接上下文管理器确保连接正确释放"""
    import time

    for attempt in range(retry_count):
        try:
            db_config = get_global_db_config()

            # 使用连接上下文管理器
            with db_config.get_connection_context() as connection:
                if not connection:
                    if attempt < retry_count - 1:
                        logger.warning(f"数据库连接失败，{2 ** attempt}秒后重试... (尝试 {attempt + 1}/{retry_count})")
                        time.sleep(2 ** attempt)
                        continue
                    else:
                        logger.error("数据库连接最终失败")
                        return []

                cursor = connection.cursor(dictionary=True)
                cursor.execute(query, params or ())

                # 检查是否是查询语句（SELECT、WITH、SHOW、DESCRIBE等）
                query_upper = query.strip().upper()
                if (query_upper.startswith('SELECT') or
                    query_upper.startswith('WITH') or
                    query_upper.startswith('SHOW') or
                    query_upper.startswith('DESCRIBE') or
                    query_upper.startswith('DESC')):
                    results = cursor.fetchall()
                else:
                    connection.commit()
                    results = cursor.rowcount

                cursor.close()

                # 转换Decimal类型
                return convert_decimal(results) if isinstance(results, list) else results

        except Exception as e:
            if attempt < retry_count - 1:
                logger.warning(f"查询执行失败，{2 ** attempt}秒后重试: {e} (尝试 {attempt + 1}/{retry_count})")
                time.sleep(2 ** attempt)
            else:
                logger.error(f"查询执行最终失败: {e}")
                logger.error(f"SQL: {query}")
                if params:
                    logger.error(f"参数: {params}")
                return []

    return []

def get_sku_list() -> list:
    """获取SKU列表"""
    query = """
    SELECT DISTINCT p.sku, p.supplier_code, p.product_image, p.first_stock_date, p.available_stock,
           i.quantity as current_stock, i.account_stock
    FROM products p
    LEFT JOIN inventory i ON p.sku = i.sku
    WHERE p.sku IS NOT NULL AND p.sku != ''
    ORDER BY p.sku
    """
    return execute_query(query)

def get_sku_detail(sku: str) -> dict:
    """获取SKU详情，包含预计可售天数计算和价格信息"""
    # 获取产品基本信息，包含价格字段
    product_query = """
    SELECT *, Price as price
    FROM products
    WHERE sku = %s LIMIT 1
    """
    product_data = execute_query(product_query, (sku,))

    # 获取最新库存信息
    inventory_query = """
    SELECT * FROM inventory WHERE sku = %s ORDER BY date DESC LIMIT 1
    """
    inventory_data = execute_query(inventory_query, (sku,))

    if not product_data:
        return {}

    result = product_data[0]
    if inventory_data:
        result.update(inventory_data[0])

    # 计算各种库存类型和预计可售天数
    try:
        # 获取近30天销量
        sales_query = """
        SELECT SUM(product_quantity) as total_sales
        FROM orders
        WHERE sku = %s
        AND order_date >= DATE_SUB(NOW(), INTERVAL 30 DAY)
        """
        sales_data = execute_query(sales_query, (sku,))

        # 计算各种库存
        quantity = result.get('quantity', 0) or 0  # 总库存
        account_stock = result.get('account_stock', 0) or 0  # 账号内库存（现在叫RW库存）
        sl_quantity = result.get('sl_quantity', 0) or 0  # SL库存
        gwyj_quantity = result.get('gwyj_quantity', 0) or 0  # GW库存

        # 公共库存 = 总库存 - 账号内库存
        public_stock = quantity - account_stock

        # 可用总库存 = 公共库存 + SL库存 + RW库存 + GW库存
        total_available_stock = public_stock + sl_quantity + account_stock + gwyj_quantity

        # 添加计算后的库存字段到结果中
        result['public_stock'] = public_stock  # 公共库存
        result['sl_stock'] = sl_quantity  # SL库存
        result['rw_stock'] = account_stock  # RW库存（原账号内库存）
        result['gw_stock'] = gwyj_quantity  # GW库存
        result['total_available_stock'] = total_available_stock  # 可用总库存

        # 计算近30天总销量
        total_sales_30d = 0
        if sales_data and sales_data[0] and sales_data[0]['total_sales']:
            total_sales_30d = sales_data[0]['total_sales']

        # 计算预计可售天数
        if total_sales_30d > 0:
            estimated_days = round((total_available_stock * 30) / total_sales_30d, 1)
        else:
            estimated_days = "♾️"  # 无销量时显示无穷符号

        result['estimated_days'] = estimated_days

    except Exception as e:
        logger.error(f"计算预计可售天数失败 {sku}: {e}")
        result['estimated_days'] = '-'
        # 设置默认值
        result['public_stock'] = 0
        result['sl_stock'] = 0
        result['rw_stock'] = 0
        result['gw_stock'] = 0
        result['total_available_stock'] = 0

    return result

def get_sales_data(sku: str = None, start_date: str = None, end_date: str = None) -> list:
    """获取销量数据"""
    query = """
    SELECT sku, product_quantity, order_date, platform_channel, order_amount
    FROM orders
    WHERE 1=1
    """
    params = []

    if sku:
        query += " AND sku = %s"
        params.append(sku)

    if start_date:
        query += " AND order_date >= %s"
        params.append(start_date)

    if end_date:
        query += " AND order_date <= %s"
        params.append(end_date)

    query += " ORDER BY order_date DESC"

    return execute_query(query, tuple(params))

def get_sales_rank(days: int = 30, limit: int = 20) -> list:
    """获取销量排名"""
    query = """
    SELECT sku, SUM(product_quantity) as total_sales, COUNT(*) as order_count
    FROM orders
    WHERE order_date >= DATE_SUB(NOW(), INTERVAL %s DAY)
    GROUP BY sku
    ORDER BY total_sales DESC
    LIMIT %s
    """
    return execute_query(query, (days, limit))

def get_inventory_alerts(start_date=None, end_date=None) -> list:
    """获取库存预警 - 售罄SKU记录

    售罄判断标准：可用总库存 = quantity + sl_quantity + gwyj_quantity = 0
    且前一天的可用总库存 > 0
    """
    try:
        # 构建查询条件
        date_condition = ""
        params = []

        if start_date and end_date:
            date_condition = "AND i.date BETWEEN %s AND %s"
            params.extend([start_date, end_date])

        # 查找售罄记录：在指定日期范围内，从有可用总库存变成0可用总库存的SKU
        # 可用总库存 = quantity + sl_quantity + gwyj_quantity
        query = f"""
        WITH inventory_changes AS (
            SELECT
                i.sku,
                i.date,
                COALESCE(i.quantity, 0) + COALESCE(i.sl_quantity, 0) + COALESCE(i.gwyj_quantity, 0) as total_available_stock,
                LAG(COALESCE(i.quantity, 0) + COALESCE(i.sl_quantity, 0) + COALESCE(i.gwyj_quantity, 0))
                    OVER (PARTITION BY i.sku ORDER BY i.date) as prev_total_available_stock
            FROM inventory i
            WHERE i.sku IS NOT NULL AND i.sku != ''
        ),
        sold_out_events AS (
            SELECT
                ic.sku,
                ic.date as sold_out_date,
                COALESCE(ic.prev_total_available_stock, 0) as previous_stock
            FROM inventory_changes ic
            WHERE ic.total_available_stock = 0
            AND COALESCE(ic.prev_total_available_stock, 0) > 0
            {date_condition.replace('i.date', 'ic.date') if date_condition else ''}
        )
        SELECT
            soe.sku as SKU,
            soe.sold_out_date as 售罄日期,
            soe.previous_stock as 之前库存,
            p.product_image as 产品主图,
            p.supplier_code,
            COALESCE(sales.total_sales, 0) as historical_sales,
            latest_inv.estimated_restock_date as 预计到货时间,
            latest_inv.estimated_restock_qty as 预计到货数量
        FROM sold_out_events soe
        LEFT JOIN products p ON soe.sku = p.sku
        LEFT JOIN (
            SELECT sku, SUM(product_quantity) as total_sales
            FROM orders
            GROUP BY sku
        ) sales ON soe.sku = sales.sku
        LEFT JOIN (
            SELECT
                sku,
                estimated_restock_date,
                estimated_restock_qty,
                ROW_NUMBER() OVER (PARTITION BY sku ORDER BY date DESC) as rn
            FROM inventory
            WHERE sku IS NOT NULL AND sku != ''
        ) latest_inv ON soe.sku = latest_inv.sku AND latest_inv.rn = 1
        ORDER BY
            CASE WHEN COALESCE(sales.total_sales, 0) > 0 THEN 0 ELSE 1 END,  -- 有历史销量的排在前面
            COALESCE(sales.total_sales, 0) DESC,  -- 按历史销量降序
            soe.sold_out_date DESC  -- 最后按售罄日期降序
        """

        result = execute_query(query, params)

        # 检查查询结果是否有效
        if not isinstance(result, list):
            print(f"获取库存预警失败: 查询返回非列表类型: {type(result)}")
            return []

        if not result:
            return []

        # 处理结果，确保字段名称正确
        processed_result = []
        for item in result:
            # 处理预计到货时间格式 - 支持英文和中文字段名
            restock_date = item.get('预计到货时间', '') or item.get('estimated_restock_date', '')
            if restock_date and restock_date != '':
                try:
                    # 如果是datetime对象，转换为字符串
                    if hasattr(restock_date, 'strftime'):
                        restock_date = restock_date.strftime('%Y-%m-%d')
                    # 如果是字符串，保持原样
                    elif isinstance(restock_date, str):
                        restock_date = restock_date
                    else:
                        restock_date = str(restock_date)
                except:
                    restock_date = ''

            # 处理预计到货数量 - 支持英文和中文字段名
            restock_qty = item.get('预计到货数量', '') or item.get('estimated_restock_qty', '')
            if restock_qty and restock_qty != '':
                try:
                    restock_qty = str(restock_qty)  # 保持原始字符串格式，如"0~19"
                except:
                    restock_qty = ''

            processed_item = {
                'SKU': item.get('SKU', ''),
                '售罄日期': item.get('售罄日期', '').strftime('%Y-%m-%d') if item.get('售罄日期') else '',
                '之前库存': item.get('之前库存', 0),
                '产品主图': item.get('产品主图', ''),
                'supplier_code': item.get('supplier_code', ''),
                'historical_sales': item.get('historical_sales', 0),
                '预计到货时间': restock_date,
                '预计到货数量': restock_qty
            }
            processed_result.append(processed_item)

        return processed_result

    except Exception as e:
        print(f"获取库存预警失败: {e}")
        return []

def get_restock_calendar() -> dict:
    """获取补货日历 - 按日期分组的格式，包含实际到货时间计算"""
    try:
        # 获取预计到货的SKU数据
        query = """
        SELECT i1.sku, i1.estimated_restock_date, i1.estimated_restock_qty, p.product_image
        FROM inventory i1
        LEFT JOIN products p ON i1.sku = p.sku
        WHERE i1.estimated_restock_date IS NOT NULL
        AND i1.estimated_restock_date != ''
        AND i1.sku IS NOT NULL
        AND i1.sku != ''
        AND i1.date = (SELECT MAX(date) FROM inventory i2 WHERE i2.sku = i1.sku)
        AND i1.date >= DATE_SUB(CURDATE(), INTERVAL 3 DAY)
        ORDER BY i1.estimated_restock_date ASC
        """

        raw_data = execute_query(query)

        # 计算实际到货时间 - 增强版：支持时间段变化检测
        actual_arrival_query = """
        WITH inventory_changes AS (
            SELECT
                i.sku,
                i.date,
                i.estimated_restock_date,
                LAG(i.estimated_restock_date) OVER (PARTITION BY i.sku ORDER BY i.date) as prev_estimated_restock_date
            FROM inventory i
            WHERE i.sku IS NOT NULL AND i.sku != ''
            AND i.date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
        ),
        arrival_detection AS (
            SELECT
                ic.sku,
                ic.date as actual_arrival_date,
                ic.estimated_restock_date,
                ic.prev_estimated_restock_date,
                CASE
                    -- 情况1: 从有值变为NULL
                    WHEN ic.prev_estimated_restock_date IS NOT NULL
                    AND ic.prev_estimated_restock_date != ''
                    AND (ic.estimated_restock_date IS NULL OR ic.estimated_restock_date = '')
                    THEN 'NULL_CHANGE'

                    -- 情况2: 时间段变化超过10天
                    WHEN ic.prev_estimated_restock_date IS NOT NULL
                    AND ic.prev_estimated_restock_date != ''
                    AND ic.estimated_restock_date IS NOT NULL
                    AND ic.estimated_restock_date != ''
                    AND ic.prev_estimated_restock_date != ic.estimated_restock_date
                    THEN 'TIME_CHANGE'

                    ELSE 'NO_CHANGE'
                END as change_type
            FROM inventory_changes ic
        )
        SELECT
            ad.sku,
            ad.actual_arrival_date,
            ad.change_type,
            ad.prev_estimated_restock_date,
            ad.estimated_restock_date
        FROM arrival_detection ad
        WHERE ad.change_type IN ('NULL_CHANGE', 'TIME_CHANGE')
        """

        actual_arrivals_data = execute_query(actual_arrival_query)

        # 构建实际到货时间映射 - 支持多个到货日期和时间段变化检测
        actual_arrivals = {}  # {sku: [date1, date2, ...]}

        def parse_date_range(date_range_str):
            """解析日期范围字符串，返回开始日期"""
            if not date_range_str or date_range_str == '':
                return None
            try:
                # 处理格式如 "2025-06-25~2025-07-02" 或 "2025-07-14~2025-07-21"
                if '~' in date_range_str:
                    start_date_str = date_range_str.split('~')[0].strip()
                    from datetime import datetime
                    return datetime.strptime(start_date_str, '%Y-%m-%d')
                else:
                    # 单个日期
                    from datetime import datetime
                    return datetime.strptime(date_range_str.strip(), '%Y-%m-%d')
            except:
                return None

        def is_time_gap_significant(prev_range, curr_range, threshold_days=10):
            """检查两个时间段之间的差距是否超过阈值"""
            prev_date = parse_date_range(prev_range)
            curr_date = parse_date_range(curr_range)

            if prev_date and curr_date:
                gap_days = abs((curr_date - prev_date).days)
                return gap_days > threshold_days
            return False

        for item in actual_arrivals_data:
            sku = item.get('sku', '')
            arrival_date = item.get('actual_arrival_date')
            change_type = item.get('change_type', 'NULL_CHANGE')
            prev_restock_date = item.get('prev_estimated_restock_date', '')
            curr_restock_date = item.get('estimated_restock_date', '')

            if sku and arrival_date:
                if hasattr(arrival_date, 'strftime'):
                    arrival_date_str = arrival_date.strftime('%Y-%m-%d')
                else:
                    arrival_date_str = str(arrival_date)

                # 判断是否应该记录为实际到货
                should_record = False

                if change_type == 'NULL_CHANGE':
                    # 从有值变为NULL，直接记录
                    should_record = True
                elif change_type == 'TIME_CHANGE':
                    # 时间段变化，检查是否超过10天
                    if is_time_gap_significant(prev_restock_date, curr_restock_date, 10):
                        should_record = True

                if should_record:
                    if sku not in actual_arrivals:
                        actual_arrivals[sku] = []
                    actual_arrivals[sku].append(arrival_date_str)

        # 按日期分组数据
        calendar_data = {}

        for item in raw_data:
            sku = item.get('sku', '')
            restock_date = item.get('estimated_restock_date', '')
            restock_qty = item.get('estimated_restock_qty', 0)
            product_image = item.get('product_image', '')

            if not sku or not restock_date:
                continue

            # 先设置默认值
            is_actually_arrived = False
            actual_arrival_date = None

            # 处理日期格式
            try:
                # 如果是日期时间对象，转换为字符串
                if hasattr(restock_date, 'strftime'):
                    date_str = restock_date.strftime('%Y-%m-%d')
                else:
                    # 如果是字符串，处理可能的日期范围格式
                    date_str = str(restock_date).strip()

                    # 处理各种日期范围格式
                    if '~' in date_str:
                        # 处理 "2025-06-15~2025-06-23" 格式
                        date_str = date_str.split('~')[0].strip()
                    elif '-' in date_str and len(date_str.split('-')) > 3:
                        # 处理 "6.15-6.23" 或类似格式
                        parts = date_str.split('-')
                        if len(parts) >= 2:
                            # 尝试解析第一个日期部分
                            first_part = parts[0].strip()
                            if '.' in first_part:
                                # 处理 "6.15" 格式，假设是当前年份
                                month_day = first_part.split('.')
                                if len(month_day) == 2:
                                    from datetime import datetime
                                    current_year = datetime.now().year
                                    month = int(month_day[0])
                                    day = int(month_day[1])
                                    date_str = f"{current_year}-{month:02d}-{day:02d}"

                    # 验证日期格式
                    from datetime import datetime
                    datetime.strptime(date_str, '%Y-%m-%d')

                    # 检查当前日期是否是实际到货日期
                    if sku in actual_arrivals and date_str in actual_arrivals[sku]:
                        is_actually_arrived = True
                        actual_arrival_date = date_str

                # 添加到日历数据
                if date_str not in calendar_data:
                    calendar_data[date_str] = []

                # 处理补货数量（可能是范围值）
                processed_qty = 0
                if restock_qty:
                    try:
                        qty_str = str(restock_qty).strip()
                        if '~' in qty_str:
                            # 处理范围值，取中间值
                            parts = qty_str.split('~')
                            if len(parts) == 2:
                                start = int(parts[0])
                                end = int(parts[1])
                                processed_qty = (start + end) // 2
                        elif '+' in qty_str:
                            # 处理"100+"这样的值，取基数
                            processed_qty = int(qty_str.replace('+', ''))
                        elif 'less than' in qty_str.lower():
                            # 处理"less than 20"这样的值
                            processed_qty = 10  # 默认值
                        else:
                            processed_qty = int(qty_str)
                    except (ValueError, TypeError) as qty_error:
                        print(f"处理补货数量失败 - SKU: {sku}, 数量: {restock_qty}, 错误: {qty_error}")
                        processed_qty = 0

                calendar_data[date_str].append({
                    'SKU': sku,
                    '预计补货数量': processed_qty,
                    'product_image': product_image,
                    'is_actually_arrived': is_actually_arrived,
                    'actual_arrival_date': actual_arrival_date
                })

            except (ValueError, TypeError) as e:
                print(f"处理补货日期失败 - SKU: {sku}, 日期: {restock_date}, 错误: {e}")
                continue

        # 批量获取实际到货SKU的产品图片
        if actual_arrivals:
            all_arrival_skus = list(actual_arrivals.keys())
            if all_arrival_skus:
                # 批量查询产品图片
                placeholders = ','.join(['%s'] * len(all_arrival_skus))
                product_query = f"SELECT sku, product_image FROM products WHERE sku IN ({placeholders})"
                try:
                    product_results = execute_query(product_query, all_arrival_skus)
                    product_images = {row['sku']: row['product_image'] for row in product_results}
                except:
                    product_images = {}

                # 添加实际到货的SKU到对应的日期
                for sku, arrival_dates in actual_arrivals.items():
                    product_image = product_images.get(sku, '')
                    for arrival_date in arrival_dates:
                        # 添加到对应日期
                        if arrival_date not in calendar_data:
                            calendar_data[arrival_date] = []

                        # 检查是否已经存在（避免重复）
                        existing = False
                        for existing_item in calendar_data[arrival_date]:
                            if existing_item['SKU'] == sku and existing_item.get('is_actually_arrived', False):
                                existing = True
                                break

                        if not existing:
                            calendar_data[arrival_date].append({
                                'SKU': sku,
                                '预计补货数量': 0,  # 实际到货的数量设为0，因为已经到货了
                                'product_image': product_image,
                                'is_actually_arrived': True,
                                'actual_arrival_date': arrival_date
                            })

        return calendar_data

    except Exception as e:
        print(f"获取补货日历失败: {e}")
        return {}

def get_dashboard_stats() -> dict:
    """获取仪表板统计数据"""
    stats = {}

    # 总SKU数 - 统计products表中的SKU数量
    total_sku_query = "SELECT COUNT(DISTINCT sku) as count FROM products WHERE sku IS NOT NULL AND sku != ''"
    result = execute_query(total_sku_query)
    stats['total_skus'] = result[0]['count'] if result else 0  # 修改为复数形式

    # 待补货SKU数 - 有预计到货时间的SKU
    restock_query = """
    SELECT COUNT(DISTINCT i.sku) as count
    FROM inventory i
    WHERE i.estimated_restock_date IS NOT NULL
    AND i.estimated_restock_date != ''
    AND i.date = (SELECT MAX(date) FROM inventory i2 WHERE i2.sku = i.sku)
    """
    result = execute_query(restock_query)
    stats['pending_restock'] = result[0]['count'] if result else 0  # 修改字段名

    # 库存提醒数 - 售罄SKU数量
    alert_query = """
    WITH inventory_changes AS (
        SELECT
            i.sku,
            i.date,
            COALESCE(i.quantity, 0) + COALESCE(i.sl_quantity, 0) + COALESCE(i.gwyj_quantity, 0) as total_available_stock,
            LAG(COALESCE(i.quantity, 0) + COALESCE(i.sl_quantity, 0) + COALESCE(i.gwyj_quantity, 0))
                OVER (PARTITION BY i.sku ORDER BY i.date) as prev_total_available_stock
        FROM inventory i
        WHERE i.sku IS NOT NULL AND i.sku != ''
    ),
    sold_out_events AS (
        SELECT DISTINCT sku
        FROM inventory_changes
        WHERE total_available_stock = 0
        AND prev_total_available_stock > 0
        AND date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
    )
    SELECT COUNT(*) as count FROM sold_out_events
    """
    result = execute_query(alert_query)
    stats['alerts_count'] = result[0]['count'] if result else 0  # 修改为复数形式

    return stats

# ==================== 物流追踪相关数据库操作函数 ====================

def get_tracking_data() -> list:
    """获取最近3个月的所有订单数据 - 支持多跟踪单号（简化版）"""

    # 先获取所有订单数据（最近3个月）
    orders_query = """
    SELECT
        o.order_number,
        o.tracking_number,
        o.platform_channel as platform,
        o.platform_id,
        o.store_account as shop,
        o.remark,
        o.is_uninvited_review,
        o.order_date,
        o.created_at,
        o.updated_at
    FROM orders o
    WHERE o.order_date >= DATE_SUB(NOW(), INTERVAL 3 MONTH)
    ORDER BY o.updated_at DESC
    LIMIT 1000
    """

    orders_data = execute_query(orders_query)
    if not orders_data:
        return []

    # 收集所有跟踪单号
    all_tracking_numbers = set()
    for order in orders_data:
        tracking_str = (order.get('tracking_number') or '').strip()
        if tracking_str:
            tracking_numbers = [t.strip() for t in tracking_str.split(';') if t.strip()]
            all_tracking_numbers.update(tracking_numbers)

    # 批量获取所有跟踪详情
    tracking_details = {}
    if all_tracking_numbers:
        tracking_list = list(all_tracking_numbers)
        placeholders = ','.join(['%s'] * len(tracking_list))
        details_query = f"""
        SELECT tracking_number, carrier_code, status, latest_event_description,
               latest_event_location, latest_event_time, tracking_info, updated_at
        FROM order_tracking_details
        WHERE tracking_number IN ({placeholders})
        """
        details_data = execute_query(details_query, tracking_list)
        if details_data:
            for detail in details_data:
                tracking_details[detail['tracking_number']] = detail

    # 构建结果
    result = []
    for order in orders_data:
        tracking_str = (order.get('tracking_number') or '').strip()

        if not tracking_str:
            # 没有跟踪单号的订单
            result.append({
                'order_number': order.get('order_number', ''),
                'tracking_number': '',
                'carrier_code': '',
                'platform': order.get('platform', ''),
                'platform_id': order.get('platform_id', ''),
                'shop': order.get('shop', ''),
                'status': 'No Tracking',
                'latest_event_description': '',
                'latest_event_location': '',
                'latest_event_time': order.get('order_date'),
                'remark': order.get('remark', ''),
                'is_uninvited_review': order.get('is_uninvited_review', False),
                'full_track_info': '{}',
                'order_date': order.get('order_date'),
                'created_at': order.get('created_at'),
                'updated_at': order.get('updated_at')
            })
        else:
            # 拆分多个跟踪单号
            tracking_numbers = [t.strip() for t in tracking_str.split(';') if t.strip()]

            for tracking_num in tracking_numbers:
                detail = tracking_details.get(tracking_num)

                result.append({
                    'order_number': order.get('order_number', ''),
                    'tracking_number': tracking_num,
                    'carrier_code': detail.get('carrier_code', '') if detail else '',
                    'platform': order.get('platform', ''),
                    'platform_id': order.get('platform_id', ''),
                    'shop': order.get('shop', ''),
                    'status': detail.get('status', 'Pending') if detail else 'Pending',
                    'latest_event_description': detail.get('latest_event_description', '') if detail else '',
                    'latest_event_location': detail.get('latest_event_location', '') if detail else '',
                    'latest_event_time': detail.get('latest_event_time') or order.get('order_date') if detail else order.get('order_date'),
                    'remark': order.get('remark', ''),
                    'is_uninvited_review': order.get('is_uninvited_review', False),
                    'full_track_info': detail.get('tracking_info', '{}') if detail else '{}',
                    'order_date': order.get('order_date'),
                    'created_at': order.get('created_at'),
                    'updated_at': detail.get('updated_at') or order.get('updated_at') if detail else order.get('updated_at')
                })

    # 按下单日期排序，越新的订单排在前面
    result.sort(key=lambda x: x.get('order_date') or x.get('created_at'), reverse=True)

    return result

def get_tracking_by_number(tracking_number: str) -> dict:
    """根据物流单号获取追踪信息 - 从order_tracking_details表和orders表联合查询"""
    query = """
    SELECT
        otd.tracking_number,
        otd.carrier_code,
        COALESCE(o.platform_channel, '') as platform,
        COALESCE(o.store_account, '') as shop,
        otd.status,
        otd.latest_event_description,
        otd.latest_event_location,
        otd.latest_event_time,
        COALESCE(o.remark, '') as remark,
        COALESCE(o.is_uninvited_review, FALSE) as is_uninvited_review,
        otd.tracking_info as full_track_info,
        otd.created_at,
        otd.updated_at
    FROM order_tracking_details otd
    LEFT JOIN orders o ON otd.tracking_number = o.tracking_number
    WHERE otd.tracking_number = %s
    """
    result = execute_query(query, (tracking_number,))
    return result[0] if result else {}

def insert_or_update_tracking(tracking_data: dict) -> bool:
    """插入或更新物流追踪数据 - 更新order_tracking_details表和orders表"""
    try:
        tracking_number = tracking_data.get('tracking_number', '')
        if not tracking_number:
            logger.error("tracking_number不能为空")
            return False

        # 检查order_tracking_details表中是否已存在
        check_otd_query = "SELECT id FROM order_tracking_details WHERE tracking_number = %s"
        existing_otd = execute_query(check_otd_query, (tracking_number,))

        # 更新或插入order_tracking_details表
        if existing_otd:
            # 更新order_tracking_details表
            otd_query = """
            UPDATE order_tracking_details SET
                carrier_code = %s, status = %s,
                latest_event_description = %s, latest_event_location = %s,
                latest_event_time = %s, tracking_info = %s, updated_at = CURRENT_TIMESTAMP
            WHERE tracking_number = %s
            """
            otd_params = (
                tracking_data.get('carrier_code'),
                tracking_data.get('status', 'Pending'),
                tracking_data.get('latest_event_description', ''),
                tracking_data.get('latest_event_location', ''),
                tracking_data.get('latest_event_time'),
                json.dumps(tracking_data.get('full_track_info', {})) if tracking_data.get('full_track_info') else None,
                tracking_number
            )
        else:
            # 插入新记录到order_tracking_details表
            otd_query = """
            INSERT INTO order_tracking_details (tracking_number, carrier_code, status,
                                               latest_event_description, latest_event_location,
                                               latest_event_time, tracking_info)
            VALUES (%s, %s, %s, %s, %s, %s, %s)
            """
            otd_params = (
                tracking_number,
                tracking_data.get('carrier_code'),
                tracking_data.get('status', 'Pending'),
                tracking_data.get('latest_event_description', ''),
                tracking_data.get('latest_event_location', ''),
                tracking_data.get('latest_event_time'),
                json.dumps(tracking_data.get('full_track_info', {})) if tracking_data.get('full_track_info') else None
            )

        # 执行order_tracking_details表的操作
        otd_result = execute_query(otd_query, otd_params)

        # 如果有平台和店铺信息，同时更新orders表
        if tracking_data.get('platform') or tracking_data.get('shop') or tracking_data.get('remark'):
            orders_query = """
            UPDATE orders SET
                platform_channel = %s, store_account = %s, remark = %s,
                is_uninvited_review = %s, updated_at = CURRENT_TIMESTAMP
            WHERE tracking_number = %s
            """
            orders_params = (
                tracking_data.get('platform', ''),
                tracking_data.get('shop', ''),
                tracking_data.get('remark', ''),
                tracking_data.get('is_uninvited_review', False),
                tracking_number
            )
            execute_query(orders_query, orders_params)

        return otd_result is not None

    except Exception as e:
        logger.error(f"插入或更新物流追踪数据失败: {e}")
        return False

def update_tracking_remark(tracking_number: str, remark: str, is_uninvited_review: bool) -> bool:
    """更新物流追踪的备注和邀评状态 - 更新orders表"""
    try:
        query = """
        UPDATE orders SET
            remark = %s, is_uninvited_review = %s, updated_at = CURRENT_TIMESTAMP
        WHERE tracking_number = %s
        """
        result = execute_query(query, (remark, is_uninvited_review, tracking_number))
        return result is not None
    except Exception as e:
        logger.error(f"更新物流追踪备注失败: {e}")
        return False

def get_tracking_dashboard_summary() -> dict:
    """获取物流追踪仪表板统计数据 - 基于2025年7月1日之后的订单"""
    try:
        # 已送达数量
        delivered_query = """
        SELECT COUNT(*) as count
        FROM orders o
        LEFT JOIN order_tracking_details otd ON o.tracking_number = otd.tracking_number
        WHERE o.order_date >= '2025-07-01' AND COALESCE(otd.status, 'No Tracking') = 'Delivered'
        """
        delivered_result = execute_query(delivered_query)
        delivered_count = delivered_result[0]['count'] if delivered_result else 0

        # 运输中数量
        in_transit_query = """
        SELECT COUNT(*) as count
        FROM orders o
        LEFT JOIN order_tracking_details otd ON o.tracking_number = otd.tracking_number
        WHERE o.order_date >= '2025-07-01' AND COALESCE(otd.status, 'No Tracking') IN ('InTransit', 'OutForDelivery')
        """
        in_transit_result = execute_query(in_transit_query)
        in_transit_count = in_transit_result[0]['count'] if in_transit_result else 0

        # 异常数量
        exception_statuses = ['Exception', 'DeliveryFailure', 'NotFound', 'Unknown']
        exception_placeholders = ','.join(['%s'] * len(exception_statuses))
        exception_query = f"""
        SELECT COUNT(*) as count
        FROM orders o
        LEFT JOIN order_tracking_details otd ON o.tracking_number = otd.tracking_number
        WHERE o.order_date >= '2025-07-01' AND COALESCE(otd.status, 'No Tracking') IN ({exception_placeholders})
        """
        exception_result = execute_query(exception_query, exception_statuses)
        exception_count = exception_result[0]['count'] if exception_result else 0

        # 可邀评数量（已送达且未邀评）
        invitable_query = """
        SELECT COUNT(*) as count
        FROM orders o
        LEFT JOIN order_tracking_details otd ON o.tracking_number = otd.tracking_number
        WHERE o.order_date >= '2025-07-01'
        AND COALESCE(otd.status, 'No Tracking') = 'Delivered'
        AND COALESCE(o.is_uninvited_review, FALSE) = FALSE
        """
        invitable_result = execute_query(invitable_query)
        invitable_count = invitable_result[0]['count'] if invitable_result else 0

        # 运输过久数量（需要通过full_track_info计算）
        long_transit_count = 0  # 暂时设为0，后续可以通过解析JSON数据计算

        return {
            'delivered_count': delivered_count,
            'in_transit_count': in_transit_count,
            'long_transit_count': long_transit_count,
            'exception_count': exception_count,
            'invitable_count': invitable_count
        }

    except Exception as e:
        logger.error(f"获取物流追踪统计数据失败: {e}")
        return {
            'delivered_count': 0,
            'in_transit_count': 0,
            'long_transit_count': 0,
            'exception_count': 0,
            'invitable_count': 0
        }

def batch_insert_tracking_from_excel(tracking_items: list) -> bool:
    """从Excel数据批量插入物流追踪记录"""
    try:
        for item in tracking_items:
            tracking_data = {
                'tracking_number': item.get('number', ''),
                'carrier_code': item.get('carrier'),
                'platform': item.get('platform', ''),
                'shop': item.get('shop', ''),
                'status': 'Pending',
                'remark': '',
                'is_uninvited_review': False,
                'full_track_info': {}
            }
            insert_or_update_tracking(tracking_data)
        return True
    except Exception as e:
        logger.error(f"批量插入物流追踪数据失败: {e}")
        return False