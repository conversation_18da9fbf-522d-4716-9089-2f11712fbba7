#!/usr/bin/env python3
"""
KKUGUAN 库存管理系统启动脚本
用于生产环境部署
"""

import os
import sys
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 导入主应用
from app import app, logger

def main():
    """主启动函数"""
    try:
        # 从环境变量获取配置
        port = int(os.getenv('PORT', 5002))
        host = os.getenv('HOST', '0.0.0.0')
        workers = int(os.getenv('WORKERS', 4))
        timeout = int(os.getenv('TIMEOUT', 120))
        
        # 检查是否为生产环境
        is_production = os.environ.get('FLASK_ENV') == 'production'
        
        logger.info(f"🚀 启动KKUGUAN库存管理系统")
        logger.info(f"📍 监听地址: {host}:{port}")
        logger.info(f"🌍 环境模式: {'生产' if is_production else '开发'}")
        
        if is_production:
            # 生产环境：关闭调试模式
            logger.info("🔧 生产环境配置已启用")
            app.run(
                host=host,
                port=port,
                debug=False,
                threaded=True
            )
        else:
            # 开发环境：启用调试模式
            logger.info("🔧 开发环境配置已启用")
            app.run(
                host=host,
                port=port,
                debug=True
            )
            
    except Exception as e:
        logger.error(f"❌ 应用启动失败: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
