#!/usr/bin/env python3
"""
数据库健康检查工具
检查数据库连接、磁盘空间、表状态等
"""

import os
import sys
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 添加当前目录到Python路径
sys.path.append('.')

def check_database_health():
    """检查数据库健康状态"""
    print("🏥 数据库健康检查开始...")
    
    try:
        from database_config import DatabaseConfig, execute_query
        
        # 创建数据库配置
        db_config = DatabaseConfig()
        
        # 测试连接
        connection = db_config.get_connection()
        if not connection:
            print("❌ 数据库连接失败")
            return False
        
        print("✅ 数据库连接成功")
        
        # 1. 检查数据库版本
        try:
            version_result = execute_query("SELECT VERSION() as version")
            if version_result:
                print(f"📊 数据库版本: {version_result[0]['version']}")
        except Exception as e:
            print(f"⚠️ 无法获取数据库版本: {e}")
        
        # 2. 检查磁盘空间（如果支持）
        try:
            disk_query = """
            SELECT 
                table_schema as 'Database',
                ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) as 'Size_MB'
            FROM information_schema.tables 
            WHERE table_schema = DATABASE()
            GROUP BY table_schema
            """
            disk_result = execute_query(disk_query)
            if disk_result:
                print(f"💾 数据库大小: {disk_result[0]['Size_MB']} MB")
        except Exception as e:
            print(f"⚠️ 无法获取磁盘使用情况: {e}")
        
        # 3. 检查表状态
        try:
            tables_query = "SHOW TABLE STATUS"
            tables_result = execute_query(tables_query)
            if tables_result:
                print(f"📋 数据库表数量: {len(tables_result)}")
                
                # 显示主要表的信息
                main_tables = ['products', 'inventory', 'orders']
                for table_info in tables_result:
                    table_name = table_info.get('Name', '')
                    if table_name in main_tables:
                        rows = table_info.get('Rows', 0)
                        data_length = table_info.get('Data_length', 0)
                        size_mb = round(data_length / 1024 / 1024, 2) if data_length else 0
                        print(f"   📊 {table_name}: {rows} 行, {size_mb} MB")
        except Exception as e:
            print(f"⚠️ 无法获取表状态: {e}")
        
        # 4. 测试简单查询
        print("\n🔍 测试基本查询...")
        test_queries = [
            ("products表记录数", "SELECT COUNT(*) as count FROM products"),
            ("inventory表记录数", "SELECT COUNT(*) as count FROM inventory"),
            ("orders表记录数", "SELECT COUNT(*) as count FROM orders"),
        ]
        
        for desc, query in test_queries:
            try:
                result = execute_query(query)
                if result:
                    count = result[0]['count']
                    print(f"   ✅ {desc}: {count}")
                else:
                    print(f"   ⚠️ {desc}: 查询返回空结果")
            except Exception as e:
                print(f"   ❌ {desc}: {e}")
        
        # 5. 测试复杂查询（可能导致磁盘空间问题的查询）
        print("\n🔍 测试复杂查询...")
        try:
            complex_query = """
            SELECT p.sku, p.supplier_code
            FROM products p
            WHERE p.sku IS NOT NULL AND p.sku != ''
            LIMIT 10
            """
            result = execute_query(complex_query)
            if result:
                print(f"   ✅ 复杂查询成功: 返回 {len(result)} 条记录")
                # 显示前3条记录
                for i, item in enumerate(result[:3]):
                    print(f"      {i+1}. SKU: {item.get('sku', 'N/A')}, 供应商: {item.get('supplier_code', 'N/A')}")
            else:
                print("   ⚠️ 复杂查询返回空结果")
        except Exception as e:
            print(f"   ❌ 复杂查询失败: {e}")
            if "No space left on device" in str(e):
                print("   💡 检测到磁盘空间不足问题！")
        
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ 数据库健康检查失败: {e}")
        return False

def suggest_solutions():
    """建议解决方案"""
    print("\n💡 解决方案建议:")
    print("1. 磁盘空间问题:")
    print("   - 联系数据库管理员清理临时文件")
    print("   - 清理 /tmp 目录下的 MySQL 临时文件")
    print("   - 增加数据库服务器磁盘空间")
    print("   - 优化查询，减少临时表使用")
    
    print("\n2. 查询优化:")
    print("   - 使用 LIMIT 限制结果集大小")
    print("   - 避免大型 JOIN 操作")
    print("   - 分批查询数据")
    print("   - 添加适当的索引")
    
    print("\n3. 应用层优化:")
    print("   - 使用缓存减少数据库查询")
    print("   - 实现数据分页")
    print("   - 异步加载数据")

def main():
    """主函数"""
    print("🚀 KKUGUAN 数据库健康检查工具")
    print("=" * 50)
    
    if check_database_health():
        print("\n" + "=" * 50)
        print("✅ 数据库健康检查完成")
    else:
        print("\n" + "=" * 50)
        print("❌ 数据库健康检查失败")
    
    suggest_solutions()

if __name__ == '__main__':
    main()
