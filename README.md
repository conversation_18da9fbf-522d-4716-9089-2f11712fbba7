# KKUGUAN 库存管理系统

基于Flask的库存管理和销量分析系统，使用MySQL数据库存储数据。

## 功能特性

- 📊 **数据分析**：销量统计、库存监控、趋势分析
- 🔍 **SKU管理**：产品详情查看、库存状态跟踪  
- 📈 **智能推荐**：基于销量和库存的补货建议
- 📅 **补货计划**：预计到货时间和数量管理
- ⚠️ **库存预警**：低库存和缺货提醒

## 技术栈

- **后端**: Flask (Python)
- **数据库**: MySQL  
- **前端**: HTML + JavaScript + Tailwind CSS
- **数据处理**: Pandas

## 快速开始

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 配置数据库
编辑 `database_config.py` 文件，设置MySQL连接参数：
```python
MYSQL_CONFIG = {
    'host': 'your_mysql_host',
    'user': 'your_username', 
    'password': 'your_password',
    'database': 'your_database'
}
```

### 3. 导入数据（可选）
如果有CSV数据文件需要导入：
```bash
python import_csv_to_mysql.py
```

### 4. 启动应用
```bash
python app.py
```

应用将在 `http://localhost:5002` 启动

## 文件结构

```
KKUGUAN/
├── app.py                 # 主应用文件
├── database_config.py     # 数据库配置
├── import_csv_to_mysql.py # 数据导入工具
├── requirements.txt       # Python依赖
├── wsgi.py               # WSGI入口
├── templates/            # HTML模板
├── static/               # 静态资源
├── utils/                # 工具模块
└── data/                 # 数据文件
```

## 主要页面

- `/` - 主控制台
- `/recommend` - 推荐页面
- `/sku-detail` - SKU详情
- `/restock` - 补货计划
- `/alert` - 库存预警

## 默认登录

- 用户名: `admin`
- 密码: `admin123`

## 许可证

MIT License 