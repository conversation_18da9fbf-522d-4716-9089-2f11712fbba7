import re
import time
from urllib.parse import urlparse, urlunparse, parse_qs, urlencode

class ImageOptimizer:
    # 简单的内存缓存
    _url_cache = {}
    _cache_timestamps = {}
    CACHE_TTL = 3600  # 1小时缓存
    # 支持压缩参数的域名配置 - 600x600像素，50%质量
    COMPRESSION_DOMAINS = {
        "img14.360buyimg.com": {
            "quality_param": "q",
            "width_param": "w", 
            "height_param": "h",
            "default_quality": 50,  # 50%质量
            "max_width": 600,       # 最大宽度600px
            "max_height": 600       # 最大高度600px
        },
        "img.alicdn.com": {
            "quality_param": "x-oss-process=image/quality,q_",
            "width_param": "x-oss-process=image/resize,w_",
            "height_param": "h_",
            "default_quality": 50,  # 50%质量
            "max_width": 600,       # 最大宽度600px
            "max_height": 600       # 最大高度600px
        },
        "ae01.alicdn.com": {
            "quality_param": "x-oss-process=image/quality,q_",
            "width_param": "x-oss-process=image/resize,w_",
            "height_param": "h_",
            "default_quality": 50,  # 50%质量
            "max_width": 600,       # 最大宽度600px
            "max_height": 600       # 最大高度600px
        },
        "b2bfiles1.gigab2b.cn": {
            "quality_param": "x-oss-process=image/quality,q_",
            "width_param": "x-oss-process=image/resize,w_",
            "height_param": "h_",
            "default_quality": 50,  # 50%质量
            "max_width": 600,       # 最大宽度600px
            "max_height": 600       # 最大高度600px
        }
    }
    

    
    @staticmethod
    def get_domain_config(image_url):
        """获取域名的压缩配置"""
        for domain, config in ImageOptimizer.COMPRESSION_DOMAINS.items():
            if domain in image_url:
                return config
        return None
    
    @staticmethod
    def compress_image(image_url, quality=None, width=None, height=None):
        """压缩图片，添加压缩参数"""
        if not image_url:
            return image_url
            
        domain_config = ImageOptimizer.get_domain_config(image_url)
        if not domain_config:
            return image_url
            
        parsed = urlparse(image_url)
        query_params = parse_qs(parsed.query)
        
        # 设置压缩参数 - 600x600，50%质量
        if quality is None:
            quality = 50  # 50%质量
        if width is None:
            width = 600   # 最大宽度600px
        if height is None:
            height = 600  # 最大高度600px
            
        # 根据域名类型添加压缩参数
        if "360buyimg.com" in image_url:
            # 京东图片压缩
            if quality:
                query_params[domain_config["quality_param"]] = [str(quality)]
            if width:
                query_params[domain_config["width_param"]] = [str(width)]
            if height:
                query_params[domain_config["height_param"]] = [str(height)]
                
        elif "alicdn.com" in image_url or "gigab2b.cn" in image_url:
            # 阿里云OSS图片处理 (包括 b2bfiles1.gigab2b.cn)
            oss_params = []
            
            # 尺寸压缩 - 限制最大尺寸
            if width and height:
                oss_params.append(f"resize,w_{width},h_{height},m_lfit,limit_1")
            elif width:
                oss_params.append(f"resize,w_{width},m_lfit,limit_1")
            elif height:
                oss_params.append(f"resize,h_{height},m_lfit,limit_1")
                
            # 质量压缩
            if quality:
                oss_params.append(f"quality,q_{quality}")
                
            # 不进行格式转换，保持原格式
                
            if oss_params:
                query_params["x-oss-process"] = [f"image/{'/'.join(oss_params)}"]
        
        # 重建URL
        new_query = urlencode(query_params, doseq=True)
        new_parsed = parsed._replace(query=new_query)
        compressed_url = urlunparse(new_parsed)
        
        return compressed_url
    

    
    @staticmethod
    def optimize_image_url(image_url, user_agent="", width=None, height=None, quality=None):
        """优化图片URL：只进行压缩，不转换格式 - 缓存版本"""
        if not image_url:
            return image_url

        # 构建缓存键
        cache_key = f"img_{hash(image_url)}_{width}_{height}_{quality}"

        # 检查缓存
        if cache_key in ImageOptimizer._url_cache:
            if time.time() - ImageOptimizer._cache_timestamps.get(cache_key, 0) < ImageOptimizer.CACHE_TTL:
                return ImageOptimizer._url_cache[cache_key]

        # 压缩参数 - 600x600，50%质量
        if quality is None:
            quality = 50  # 50%质量
        if width is None:
            width = 600   # 最大宽度600px
        if height is None:
            height = 600  # 最大高度600px

        # 压缩图片
        optimized_url = ImageOptimizer.compress_image(
            image_url,
            quality=quality,
            width=width,
            height=height
        )

        # 缓存结果
        ImageOptimizer._url_cache[cache_key] = optimized_url
        ImageOptimizer._cache_timestamps[cache_key] = time.time()

        return optimized_url
    
    @staticmethod
    def get_optimized_image_data(image_url, user_agent="", **kwargs):
        """获取优化后的图片数据"""
        if not image_url:
            return {
                "src": "https://via.placeholder.com/200x200/f3f4f6/9ca3af?text=No+Image", 
                "optimized": False,
                "compressed": False
            }
        
        domain_config = ImageOptimizer.get_domain_config(image_url)
        
        # 压缩参数 - 600x600，50%质量
        compress_params = {
            "quality": kwargs.get("quality", 50),  # 50%质量
            "width": kwargs.get("width", 600),     # 600px宽度
            "height": kwargs.get("height", 600)    # 600px高度
        }
        
        # 压缩图片
        compressed_url = ImageOptimizer.compress_image(image_url, **compress_params)
        is_compressed = compressed_url != image_url
        
        return {
            "src": compressed_url,
            "optimized": is_compressed,
            "compressed": is_compressed,
            "domain_supported": domain_config is not None
        }

    @staticmethod
    def get_image_stats():
        """获取图片优化统计信息"""
        return {
            "supported_domains": len(ImageOptimizer.COMPRESSION_DOMAINS),
            "compression_enabled": True
        }
