# KKUGUAN 库存管理系统环境变量配置示例
# 复制此文件为 .env 并填入实际值

# ================================
# Flask应用配置
# ================================
FLASK_ENV=production
SECRET_KEY=your-super-secret-key-here-change-this-in-production

# ================================
# 数据库配置
# ================================
# MySQL主数据库配置
DB_HOST_PRIMARY=127.0.0.1
DB_HOST_FALLBACK=your-mysql-host
DB_USER=your-mysql-username
DB_PASSWORD=your-mysql-password
DB_NAME=kkuguan_db
DB_PORT=3306

# ================================
# Redis缓存配置
# ================================
# Redis服务器配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# Redis连接池配置
REDIS_MAX_CONNECTIONS=20
REDIS_SOCKET_TIMEOUT=5
REDIS_SOCKET_CONNECT_TIMEOUT=5

# ================================
# 缓存策略配置
# ================================
# 缓存TTL配置（秒）
CACHE_SKU_DETAIL_TTL=1800        # SKU详情缓存30分钟
CACHE_SALES_STOCK_TTL=1800       # 销量库存缓存30分钟
CACHE_RECOMMENDATIONS_TTL=7200   # 推荐缓存2小时
CACHE_RESTOCK_CALENDAR_TTL=14400 # 补货日历缓存4小时
CACHE_INVENTORY_ALERTS_TTL=1800  # 库存提醒缓存30分钟
CACHE_DASHBOARD_STATS_TTL=1800   # 仪表盘统计缓存30分钟

# ================================
# 飞书API配置
# ================================
FEISHU_APP_ID=your-feishu-app-id
FEISHU_APP_SECRET=your-feishu-app-secret
FEISHU_SPREADSHEET_TOKEN=your-feishu-spreadsheet-token

# ================================
# 应用性能配置
# ================================
# 跳过启动预热（调试用）
SKIP_WARMUP=false

# 快速预热模式（仅预热基础数据）
QUICK_WARMUP=false

# 数据库连接池配置
DB_MAX_CONNECTIONS=10
DB_CONNECTION_TIMEOUT=10

# 应用性能配置
MAX_CONCURRENT_REQUESTS=50
REQUEST_TIMEOUT=30

# 图片优化配置
IMAGE_OPTIMIZATION_ENABLED=true
IMAGE_CACHE_TTL=86400  # 图片缓存24小时

# ================================
# 性能监控配置
# ================================
# 性能监控
PERFORMANCE_MONITORING=true
SLOW_QUERY_THRESHOLD=2.0  # 慢查询阈值（秒）

# 错误报告
ERROR_REPORTING=true
ERROR_EMAIL_ENABLED=false

# ================================
# 部署配置
# ================================
# 应用端口
PORT=5002

# 工作进程数（Docker部署时使用）
WORKERS=4

# 请求超时时间（秒）
TIMEOUT=120
