/*! tailwindcss v3.4.1 | MIT License | https://tailwindcss.com */
@tailwind base;
@tailwind components;
@tailwind utilities;
/* 由于文件体积限制，这里仅保留基础样式，实际项目建议本地编译获得完整功能。 */
html{line-height:1.5;-webkit-text-size-adjust:100%;-moz-tab-size:4;tab-size:4;font-family:ui-sans-serif,system-ui,sans-serif;font-feature-settings:normal;}
body{margin:0;line-height:inherit;}
*,::before,::after{box-sizing:border-box;border-width:0;border-style:solid;border-color:#e5e7eb;}
a{color:inherit;text-decoration:inherit;}
button,input,optgroup,select,textarea{font-family:inherit;font-size:100%;line-height:inherit;margin:0;}
button,select{text-transform:none;}
button,[type=button],[type=reset],[type=submit]{-webkit-appearance:button;background-color:transparent;background-image:none;}
input::placeholder,textarea::placeholder{opacity:1;color:#9ca3af;}
[role=button],button{cursor:pointer;}
:disabled{cursor:default;}
img,svg,video,canvas,audio,iframe,embed,object{display:block;vertical-align:middle;}
img,video{max-width:100%;height:auto;}
.bg-white{background-color:#fff;}
.bg-blue-50{background-color:#eff6ff;}
.bg-blue-100{background-color:#dbeafe;}
.bg-blue-500{background-color:#3b82f6;}
.bg-blue-600{background-color:#2563eb;}
.bg-blue-700{background-color:#1d4ed8;}
.bg-green-100{background-color:#dcfce7;}
.text-blue-500{color:#3b82f6;}
.text-blue-600{color:#2563eb;}
.text-blue-700{color:#1d4ed8;}
.text-green-500{color:#22c55e;}
.text-green-600{color:#16a34a;}
.text-green-700{color:#15803d;}
.text-gray-100{color:#f3f4f6;}
.text-gray-400{color:#9ca3af;}
.text-gray-500{color:#6b7280;}
.text-gray-600{color:#4b5563;}
.text-gray-700{color:#374151;}
.text-gray-800{color:#1f2937;}
.text-xl{font-size:1.25rem;line-height:1.75rem;}
.text-2xl{font-size:1.5rem;line-height:2rem;}
.text-lg{font-size:1.125rem;line-height:1.75rem;}
.text-base{font-size:1rem;line-height:1.5rem;}
.text-sm{font-size:.875rem;line-height:1.25rem;}
.text-xs{font-size:.75rem;line-height:1rem;}
.font-bold{font-weight:700;}
.font-semibold{font-weight:600;}
.font-medium{font-weight:500;}
.rounded{border-radius:.25rem;}
.rounded-xl{border-radius:.75rem;}
.rounded-2xl{border-radius:1rem;}
.rounded-3xl{border-radius:1.5rem;}
.shadow{box-shadow:0 1px 2px 0 rgb(0 0 0 / 0.05);}
.shadow-lg{box-shadow:0 10px 15px -3px rgb(0 0 0 / 0.1),0 4px 6px -4px rgb(0 0 0 / 0.1);}
.shadow-xl{box-shadow:0 20px 25px -5px rgb(0 0 0 / 0.1),0 8px 10px -6px rgb(0 0 0 / 0.1);}
.shadow-2xl{box-shadow:0 25px 50px -12px rgb(0 0 0 / 0.25);}
.p-4{padding:1rem;}
.p-6{padding:1.5rem;}
.p-8{padding:2rem;}
.px-2{padding-left:.5rem;padding-right:.5rem;}
.px-3{padding-left:.75rem;padding-right:.75rem;}
.px-4{padding-left:1rem;padding-right:1rem;}
.px-6{padding-left:1.5rem;padding-right:1.5rem;}
.px-8{padding-left:2rem;padding-right:2rem;}
.py-1{padding-top:.25rem;padding-bottom:.25rem;}
.py-2{padding-top:.5rem;padding-bottom:.5rem;}
.py-3{padding-top:.75rem;padding-bottom:.75rem;}
.py-4{padding-top:1rem;padding-bottom:1rem;}
.py-6{padding-top:1.5rem;padding-bottom:1.5rem;}
.py-8{padding-top:2rem;padding-bottom:2rem;}
.mb-1{margin-bottom:.25rem;}
.mb-2{margin-bottom:.5rem;}
.mb-4{margin-bottom:1rem;}
.mb-6{margin-bottom:1.5rem;}
.mb-8{margin-bottom:2rem;}
.mt-1{margin-top:.25rem;}
.mt-2{margin-top:.5rem;}
.mt-3{margin-top:.75rem;}
.mt-4{margin-top:1rem;}
.mt-6{margin-top:1.5rem;}
.mt-8{margin-top:2rem;}
.flex{display:flex;}
.inline-flex{display:inline-flex;}
.grid{display:grid;}
.gap-1{gap:.25rem;}
.gap-2{gap:.5rem;}
.gap-4{gap:1rem;}
.gap-6{gap:1.5rem;}
.gap-8{gap:2rem;}
.items-center{align-items:center;}
.justify-between{justify-content:space-between;}
.justify-center{justify-content:center;}
.self-end{align-self:flex-end;}
.w-full{width:100%;}
.w-32{width:8rem;}
.w-36{width:9rem;}
.w-96{width:24rem;}
.min-w-full{min-width:100%;}
.max-w-7xl{max-width:80rem;}
.mx-auto{margin-left:auto;margin-right:auto;}
.rounded-l-2xl{border-top-left-radius:1rem;border-bottom-left-radius:1rem;}
.rounded-r-2xl{border-top-right-radius:1rem;border-bottom-right-radius:1rem;}
.border{border-width:1px;}
.border-blue-200{border-color:#bfdbfe;}
.border-gray-200{border-color:#e5e7eb;}
.border-b{border-bottom-width:1px;}
.border-t{border-top-width:1px;}
.border-gray-300{border-color:#d1d5db;}
.border-gray-400{border-color:#9ca3af;}
.border-gray-500{border-color:#6b7280;}
.border-transparent{border-color:transparent;}
.bg-opacity-30{--tw-bg-opacity:0.3;}
.bg-white\/80{background-color:rgba(255,255,255,0.8);}
.bg-gradient-to-br{background-image:linear-gradient(to bottom right,var(--tw-gradient-stops));}
.from-gray-100{--tw-gradient-from:#f3f4f6;--tw-gradient-stops:var(--tw-gradient-from),var(--tw-gradient-to,rgba(243,244,246,0));}
.via-white{--tw-gradient-stops:var(--tw-gradient-from),#fff,var(--tw-gradient-to,rgba(255,255,255,0));}
.to-blue-50{--tw-gradient-to:#eff6ff;}
.sticky{position:sticky;}
.top-0{top:0;}
.z-50{z-index:50;}
.overflow-x-auto{overflow-x:auto;}
.overflow-hidden{overflow:hidden;}
.align-top{vertical-align:top;}
.text-center{text-align:center;}
.text-left{text-align:left;}
.text-right{text-align:right;}
.transition{transition-property:all;transition-duration:150ms;transition-timing-function:cubic-bezier(.4,0,.2,1);}
.hover\:bg-blue-100:hover{background-color:#dbeafe;}
.hover\:bg-blue-600:hover{background-color:#2563eb;}
.hover\:shadow-2xl:hover{box-shadow:0 25px 50px -12px rgb(0 0 0 / 0.25);}
.focus\:ring-2:focus{box-shadow:0 0 0 2px #3b82f6;outline:2px solid transparent;outline-offset:2px;} 
/* 网格布局补充样式 */
/* 网格列数定义 */
.grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
}

.grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
}

.grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
}

.grid-cols-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr));
}

.grid-cols-6 {
    grid-template-columns: repeat(6, minmax(0, 1fr));
}

/* 响应式设计 */
@media (max-width: 768px) {
    .grid-cols-3 {
        grid-template-columns: repeat(2, minmax(0, 1fr));
    }
}

@media (max-width: 480px) {
    .grid-cols-3 {
        grid-template-columns: repeat(1, minmax(0, 1fr));
    }
}