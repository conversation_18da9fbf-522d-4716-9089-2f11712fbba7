/* K<PERSON>UGUAN 统一设计系统 */

:root {
  /* 主色调系统 */
  --primary-50: #eff6ff;
  --primary-100: #dbeafe;
  --primary-200: #bfdbfe;
  --primary-300: #93c5fd;
  --primary-400: #60a5fa;
  --primary-500: #3b82f6;
  --primary-600: #2563eb;
  --primary-700: #1d4ed8;
  --primary-800: #1e40af;
  --primary-900: #1e3a8a;

  /* 中性色系统 */
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;

  /* 状态色系统 */
  --success-50: #f0fdf4;
  --success-500: #22c55e;
  --success-600: #16a34a;
  --warning-50: #fffbeb;
  --warning-500: #f59e0b;
  --warning-600: #d97706;
  --error-50: #fef2f2;
  --error-500: #ef4444;
  --error-600: #dc2626;

  /* 间距系统 */
  --space-1: 0.25rem;   /* 4px */
  --space-2: 0.5rem;    /* 8px */
  --space-3: 0.75rem;   /* 12px */
  --space-4: 1rem;      /* 16px */
  --space-5: 1.25rem;   /* 20px */
  --space-6: 1.5rem;    /* 24px */
  --space-8: 2rem;      /* 32px */
  --space-10: 2.5rem;   /* 40px */
  --space-12: 3rem;     /* 48px */

  /* 圆角系统 */
  --radius-sm: 0.375rem;  /* 6px */
  --radius-md: 0.5rem;    /* 8px */
  --radius-lg: 0.75rem;   /* 12px */
  --radius-xl: 1rem;      /* 16px */
  --radius-2xl: 1.5rem;   /* 24px */

  /* 阴影系统 */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* 字体系统 */
  --font-size-xs: 0.75rem;    /* 12px */
  --font-size-sm: 0.875rem;   /* 14px */
  --font-size-base: 1rem;     /* 16px */
  --font-size-lg: 1.125rem;   /* 18px */
  --font-size-xl: 1.25rem;    /* 20px */
  --font-size-2xl: 1.5rem;    /* 24px */
  --font-size-3xl: 1.875rem;  /* 30px */

  /* 过渡动画 */
  --transition-fast: 0.15s ease-out;
  --transition-base: 0.2s ease-out;
  --transition-slow: 0.3s ease-out;
}

/* 全局重置 */
* {
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  background: var(--gray-50);
  color: var(--gray-900);
  line-height: 1.6;
  margin: 0;
  padding: 0;
}

/* 统一卡片系统 */
.kkuguan-card {
  background: white;
  border-radius: var(--radius-xl);
  border: 1px solid var(--gray-200);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-base);
  overflow: hidden;
}

.kkuguan-card:hover {
  border-color: var(--gray-300);
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.kkuguan-card-header {
  padding: var(--space-6);
  border-bottom: 1px solid var(--gray-200);
  background: var(--gray-50);
}

.kkuguan-card-body {
  padding: var(--space-6);
}

.kkuguan-card-footer {
  padding: var(--space-6);
  border-top: 1px solid var(--gray-200);
  background: var(--gray-50);
}

/* 统一按钮系统 */
.kkuguan-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-4);
  border-radius: var(--radius-lg);
  font-weight: 500;
  font-size: var(--font-size-sm);
  line-height: 1;
  cursor: pointer;
  border: 1px solid transparent;
  transition: all var(--transition-base);
  text-decoration: none;
  white-space: nowrap;
}

.kkuguan-btn-primary {
  background: var(--primary-500);
  color: white;
  border-color: var(--primary-500);
}

.kkuguan-btn-primary:hover {
  background: var(--primary-600);
  border-color: var(--primary-600);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.kkuguan-btn-secondary {
  background: white;
  color: var(--gray-700);
  border-color: var(--gray-300);
}

.kkuguan-btn-secondary:hover {
  background: var(--gray-50);
  border-color: var(--gray-400);
  transform: translateY(-1px);
}

.kkuguan-btn-ghost {
  background: transparent;
  color: var(--gray-600);
}

.kkuguan-btn-ghost:hover {
  background: var(--gray-100);
  color: var(--gray-700);
}

/* 统一输入框系统 */
.kkuguan-input {
  width: 100%;
  padding: var(--space-3) var(--space-4);
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-sm);
  transition: all var(--transition-base);
  background: white;
  color: var(--gray-900);
}

.kkuguan-input:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.kkuguan-input::placeholder {
  color: var(--gray-400);
}

/* 统一选择框系统 */
.kkuguan-select {
  width: 100%;
  padding: var(--space-3) var(--space-4);
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-sm);
  background: white;
  color: var(--gray-900);
  cursor: pointer;
  transition: all var(--transition-base);
}

.kkuguan-select:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 统一导航系统 */
.kkuguan-nav {
  background: white;
  border-bottom: 1px solid var(--gray-200);
  box-shadow: var(--shadow-sm);
}

.kkuguan-nav-link {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-4);
  color: var(--gray-600);
  text-decoration: none;
  border-radius: var(--radius-lg);
  transition: all var(--transition-base);
  font-weight: 500;
}

.kkuguan-nav-link:hover {
  background: var(--gray-100);
  color: var(--gray-900);
}

.kkuguan-nav-link.active {
  background: var(--primary-50);
  color: var(--primary-700);
}

/* 统一表格系统 */
.kkuguan-table {
  width: 100%;
  background: white;
  border-radius: var(--radius-xl);
  border: 1px solid var(--gray-200);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.kkuguan-table th {
  background: var(--gray-50);
  color: var(--gray-700);
  font-weight: 600;
  padding: var(--space-4);
  border-bottom: 1px solid var(--gray-200);
  font-size: var(--font-size-sm);
  text-align: left;
}

.kkuguan-table td {
  padding: var(--space-4);
  border-bottom: 1px solid var(--gray-200);
  color: var(--gray-900);
  font-size: var(--font-size-sm);
}

.kkuguan-table tr:hover {
  background: var(--gray-50);
}

.kkuguan-table tr:last-child td {
  border-bottom: none;
}

/* 统一状态指示器 */
.kkuguan-badge {
  display: inline-flex;
  align-items: center;
  gap: var(--space-1);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-md);
  font-size: var(--font-size-xs);
  font-weight: 500;
}

.kkuguan-badge-success {
  background: var(--success-50);
  color: var(--success-600);
}

.kkuguan-badge-warning {
  background: var(--warning-50);
  color: var(--warning-600);
}

.kkuguan-badge-error {
  background: var(--error-50);
  color: var(--error-600);
}

.kkuguan-badge-info {
  background: var(--primary-50);
  color: var(--primary-600);
}

/* 统一加载动画 */
.kkuguan-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid var(--gray-200);
  border-top-color: var(--primary-500);
  border-radius: 50%;
  animation: kkuguan-spin 1s linear infinite;
}

@keyframes kkuguan-spin {
  to { transform: rotate(360deg); }
}

/* 统一工具类 */
.kkuguan-text-primary { color: var(--gray-900); }
.kkuguan-text-secondary { color: var(--gray-600); }
.kkuguan-text-muted { color: var(--gray-400); }

.kkuguan-bg-primary { background: var(--primary-500); }
.kkuguan-bg-secondary { background: var(--gray-100); }
.kkuguan-bg-success { background: var(--success-500); }
.kkuguan-bg-warning { background: var(--warning-500); }
.kkuguan-bg-error { background: var(--error-500); }

/* 响应式设计 */
@media (max-width: 768px) {
  .kkuguan-card {
    border-radius: var(--radius-lg);
    margin: var(--space-2);
  }
  
  .kkuguan-card-header,
  .kkuguan-card-body,
  .kkuguan-card-footer {
    padding: var(--space-4);
  }
  
  .kkuguan-btn {
    padding: var(--space-3) var(--space-3);
  }
}
