/**
 * KKUGUAN 样式迁移工具
 * 自动将现有的混乱样式统一到新的设计系统
 */

class StyleMigration {
    constructor() {
        this.migrationMap = {
            // 卡片类映射
            'bg-white': 'kkuguan-card',
            'bg-white/80': 'kkuguan-card',
            'bg-white/90': 'kkuguan-card',
            'bg-white/95': 'kkuguan-card',
            'modern-card': 'kkuguan-card',
            'glass-card': 'kkuguan-card',
            'card': 'kkuguan-card',
            
            // 按钮类映射
            'btn': 'kkuguan-btn',
            'btn-primary': 'kkuguan-btn kkuguan-btn-primary',
            'btn-secondary': 'kkuguan-btn kkuguan-btn-secondary',
            'modern-btn': 'kkuguan-btn',
            'modern-btn-primary': 'kkuguan-btn kkuguan-btn-primary',
            'modern-btn-secondary': 'kkuguan-btn kkuguan-btn-secondary',
            
            // 输入框类映射
            'input': 'kkuguan-input',
            'modern-input': 'kkuguan-input',
            'glass-input': 'kkuguan-input',
            
            // 选择框类映射
            'select': 'kkuguan-select',
            'modern-select': 'kkuguan-select',
            'glass-select': 'kkuguan-select',
            
            // 导航类映射
            'nav-link': 'kkuguan-nav-link',
            'modern-nav-link': 'kkuguan-nav-link',
            'glass-nav-link': 'kkuguan-nav-link',
            
            // 表格类映射
            'modern-table': 'kkuguan-table',
            'glass-table': 'kkuguan-table',
            
            // 状态指示器映射
            'badge': 'kkuguan-badge',
            'modern-badge': 'kkuguan-badge',
            'glass-status': 'kkuguan-badge',
            'status-indicator': 'kkuguan-badge',
            
            // 加载动画映射
            'spinner': 'kkuguan-spinner',
            'modern-spinner': 'kkuguan-spinner'
        };
        
        this.init();
    }

    init() {
        console.log('🔄 开始样式迁移...');
        this.migrateClasses();
        this.migrateInlineStyles();
        this.addMissingStructure();
        this.cleanupOldStyles();
        console.log('✅ 样式迁移完成');
    }

    migrateClasses() {
        Object.entries(this.migrationMap).forEach(([oldClass, newClass]) => {
            const elements = document.querySelectorAll(`.${oldClass}`);
            elements.forEach(element => {
                element.classList.remove(oldClass);
                newClass.split(' ').forEach(cls => {
                    if (cls) element.classList.add(cls);
                });
            });
        });
    }

    migrateInlineStyles() {
        // 迁移内联样式到统一类
        const elementsWithInlineStyles = document.querySelectorAll('[style]');
        elementsWithInlineStyles.forEach(element => {
            const style = element.getAttribute('style');
            
            // 检测卡片样式
            if (this.isCardStyle(style)) {
                element.classList.add('kkuguan-card');
                this.cleanInlineStyle(element, ['background', 'border-radius', 'box-shadow', 'border']);
            }
            
            // 检测按钮样式
            if (this.isButtonStyle(style)) {
                element.classList.add('kkuguan-btn');
                if (style.includes('blue') || style.includes('#3b82f6')) {
                    element.classList.add('kkuguan-btn-primary');
                } else {
                    element.classList.add('kkuguan-btn-secondary');
                }
                this.cleanInlineStyle(element, ['background', 'color', 'border', 'padding']);
            }
        });
    }

    isCardStyle(style) {
        return style.includes('background') && 
               (style.includes('white') || style.includes('#fff')) &&
               (style.includes('border-radius') || style.includes('box-shadow'));
    }

    isButtonStyle(style) {
        return style.includes('cursor: pointer') || 
               style.includes('padding') && style.includes('border-radius');
    }

    cleanInlineStyle(element, propertiesToRemove) {
        const style = element.getAttribute('style');
        if (!style) return;
        
        let newStyle = style;
        propertiesToRemove.forEach(prop => {
            const regex = new RegExp(`${prop}[^;]*;?`, 'gi');
            newStyle = newStyle.replace(regex, '');
        });
        
        newStyle = newStyle.trim();
        if (newStyle) {
            element.setAttribute('style', newStyle);
        } else {
            element.removeAttribute('style');
        }
    }

    addMissingStructure() {
        // 为卡片添加缺失的结构
        const cards = document.querySelectorAll('.kkuguan-card');
        cards.forEach(card => {
            // 检查是否需要添加卡片头部
            const firstChild = card.firstElementChild;
            if (firstChild && this.isHeaderContent(firstChild)) {
                if (!firstChild.classList.contains('kkuguan-card-header')) {
                    firstChild.classList.add('kkuguan-card-header');
                }
            }
            
            // 检查是否需要添加卡片主体
            const bodyElements = card.querySelectorAll(':scope > *:not(.kkuguan-card-header):not(.kkuguan-card-footer)');
            if (bodyElements.length > 0) {
                bodyElements.forEach(element => {
                    if (!element.classList.contains('kkuguan-card-body')) {
                        element.classList.add('kkuguan-card-body');
                    }
                });
            }
        });
    }

    isHeaderContent(element) {
        const text = element.textContent.toLowerCase();
        return element.tagName === 'H1' || 
               element.tagName === 'H2' || 
               element.tagName === 'H3' ||
               text.includes('标题') ||
               text.includes('title') ||
               element.querySelector('h1, h2, h3');
    }

    cleanupOldStyles() {
        // 移除旧的样式类
        const oldClasses = [
            'shadow-xl', 'shadow-lg', 'shadow-md',
            'rounded-3xl', 'rounded-2xl', 'rounded-xl',
            'bg-blue-500', 'bg-blue-600', 'text-blue-600', 'text-blue-700',
            'hover:bg-blue-600', 'hover:shadow-2xl',
            'transition-all', 'duration-300'
        ];
        
        oldClasses.forEach(className => {
            const elements = document.querySelectorAll(`.${className}`);
            elements.forEach(element => {
                element.classList.remove(className);
            });
        });
    }

    // 生成迁移报告
    generateMigrationReport() {
        const report = {
            migratedElements: 0,
            remainingIssues: [],
            suggestions: []
        };

        // 检查剩余的不一致样式
        const inconsistentElements = document.querySelectorAll('[class*="bg-"], [class*="text-"], [class*="border-"]');
        inconsistentElements.forEach(element => {
            const classes = Array.from(element.classList);
            const hasOldClasses = classes.some(cls => 
                cls.startsWith('bg-') || 
                cls.startsWith('text-') || 
                cls.startsWith('border-') ||
                cls.startsWith('shadow-') ||
                cls.startsWith('rounded-')
            );
            
            if (hasOldClasses && !classes.some(cls => cls.startsWith('kkuguan-'))) {
                report.remainingIssues.push({
                    element: element.tagName.toLowerCase(),
                    classes: classes.join(' '),
                    suggestion: '建议使用统一的 kkuguan- 类'
                });
            }
        });

        // 检查内联样式
        const inlineStyleElements = document.querySelectorAll('[style]');
        inlineStyleElements.forEach(element => {
            report.remainingIssues.push({
                element: element.tagName.toLowerCase(),
                issue: '仍有内联样式',
                suggestion: '建议迁移到统一的CSS类'
            });
        });

        report.migratedElements = document.querySelectorAll('[class*="kkuguan-"]').length;
        
        return report;
    }

    // 显示迁移报告
    showMigrationReport() {
        const report = this.generateMigrationReport();
        
        console.group('📊 样式迁移报告');
        console.log(`✅ 已迁移元素: ${report.migratedElements} 个`);
        console.log(`⚠️ 剩余问题: ${report.remainingIssues.length} 个`);
        
        if (report.remainingIssues.length > 0) {
            console.group('🔍 需要手动处理的问题:');
            report.remainingIssues.forEach((issue, index) => {
                console.log(`${index + 1}. ${issue.element}: ${issue.issue || issue.classes}`);
                console.log(`   建议: ${issue.suggestion}`);
            });
            console.groupEnd();
        }
        
        console.groupEnd();
        
        // 在页面上显示报告
        this.displayReportOnPage(report);
    }

    displayReportOnPage(report) {
        const reportDiv = document.createElement('div');
        reportDiv.id = 'migration-report';
        reportDiv.style.cssText = `
            position: fixed;
            top: 20px;
            left: 20px;
            background: white;
            border: 1px solid var(--gray-300);
            border-radius: var(--radius-lg);
            padding: var(--space-4);
            box-shadow: var(--shadow-lg);
            z-index: 1000;
            max-width: 400px;
            font-size: 14px;
        `;
        
        reportDiv.innerHTML = `
            <div style="display: flex; justify-content: between; align-items: center; margin-bottom: 12px;">
                <h3 style="margin: 0; color: var(--primary-600);">样式迁移报告</h3>
                <button onclick="this.parentElement.parentElement.remove()" style="background: none; border: none; font-size: 18px; cursor: pointer;">×</button>
            </div>
            <p>✅ 已迁移: <strong>${report.migratedElements}</strong> 个元素</p>
            <p>⚠️ 剩余问题: <strong>${report.remainingIssues.length}</strong> 个</p>
            ${report.remainingIssues.length > 0 ? 
                `<details style="margin-top: 12px;">
                    <summary style="cursor: pointer; color: var(--warning-600);">查看详细问题</summary>
                    <ul style="margin: 8px 0; padding-left: 20px; font-size: 12px;">
                        ${report.remainingIssues.slice(0, 5).map(issue => 
                            `<li>${issue.element}: ${issue.issue || '样式不统一'}</li>`
                        ).join('')}
                        ${report.remainingIssues.length > 5 ? `<li>... 还有 ${report.remainingIssues.length - 5} 个问题</li>` : ''}
                    </ul>
                </details>` : 
                '<p style="color: var(--success-600);">🎉 所有样式已统一!</p>'
            }
        `;
        
        document.body.appendChild(reportDiv);
        
        // 5秒后自动隐藏
        setTimeout(() => {
            if (reportDiv.parentElement) {
                reportDiv.remove();
            }
        }, 10000);
    }
}

// 页面加载完成后自动执行迁移
document.addEventListener('DOMContentLoaded', () => {
    // 延迟执行，确保所有样式都已加载
    setTimeout(() => {
        const migration = new StyleMigration();
        migration.showMigrationReport();
    }, 1000);
});

// 导出供手动调用
window.StyleMigration = StyleMigration;
