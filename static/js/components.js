/**
 * KKUGUAN UI组件库
 * 包含加载动画、Toast提示、骨架屏等组件
 */

class UIComponents {
    constructor() {
        this.toastContainer = this.createToastContainer();
        this.loadingOverlay = null;
    }

    // ==================== Toast 提示组件 ====================
    createToastContainer() {
        const container = document.createElement('div');
        container.id = 'toast-container';
        container.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            pointer-events: none;
        `;
        document.body.appendChild(container);
        return container;
    }

    showToast(message, type = 'success', duration = 3000) {
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.style.cssText = `
            margin-bottom: 10px;
            pointer-events: auto;
            cursor: pointer;
        `;
        
        const icon = this.getToastIcon(type);
        toast.innerHTML = `
            <div style="display: flex; align-items: center; gap: 8px;">
                ${icon}
                <span>${message}</span>
                <button onclick="this.parentElement.parentElement.remove()" 
                        style="background: none; border: none; color: inherit; font-size: 18px; cursor: pointer; margin-left: 8px;">×</button>
            </div>
        `;

        this.toastContainer.appendChild(toast);

        // 自动移除
        setTimeout(() => {
            if (toast.parentNode) {
                toast.style.animation = 'slideOut 0.3s ease forwards';
                setTimeout(() => toast.remove(), 300);
            }
        }, duration);

        return toast;
    }

    getToastIcon(type) {
        const icons = {
            success: '<i class="fas fa-check-circle"></i>',
            warning: '<i class="fas fa-exclamation-triangle"></i>',
            error: '<i class="fas fa-times-circle"></i>',
            info: '<i class="fas fa-info-circle"></i>'
        };
        return icons[type] || icons.info;
    }

    // ==================== 加载动画组件 ====================
    showLoading(message = '加载中...', target = null) {
        const overlay = document.createElement('div');
        overlay.className = 'loading-overlay';
        overlay.innerHTML = `
            <div class="loading-content">
                <div class="loading-spinner"></div>
                <div class="loading-text">${message}</div>
            </div>
        `;

        const styles = `
            .loading-overlay {
                position: ${target ? 'absolute' : 'fixed'};
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(168, 85, 247, 0.1);
                backdrop-filter: blur(5px);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 9999;
                animation: fadeIn 0.3s ease;
            }
            
            .loading-content {
                background: rgba(255, 255, 255, 0.95);
                padding: 2rem;
                border-radius: 1rem;
                box-shadow: 0 10px 25px rgba(168, 85, 247, 0.2);
                text-align: center;
                border: 1px solid rgba(168, 85, 247, 0.2);
            }
            
            .loading-spinner {
                width: 40px;
                height: 40px;
                border: 3px solid rgba(168, 85, 247, 0.2);
                border-top: 3px solid #a855f7;
                border-radius: 50%;
                animation: spin 1s linear infinite;
                margin: 0 auto 1rem;
            }
            
            .loading-text {
                color: #7c3aed;
                font-weight: 500;
                font-size: 0.875rem;
            }
            
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
            
            @keyframes fadeIn {
                from { opacity: 0; }
                to { opacity: 1; }
            }
        `;

        // 添加样式
        if (!document.getElementById('loading-styles')) {
            const styleSheet = document.createElement('style');
            styleSheet.id = 'loading-styles';
            styleSheet.textContent = styles;
            document.head.appendChild(styleSheet);
        }

        if (target) {
            target.style.position = 'relative';
            target.appendChild(overlay);
        } else {
            document.body.appendChild(overlay);
            this.loadingOverlay = overlay;
        }

        return overlay;
    }

    hideLoading(overlay = null) {
        const targetOverlay = overlay || this.loadingOverlay;
        if (targetOverlay && targetOverlay.parentNode) {
            targetOverlay.style.animation = 'fadeOut 0.3s ease forwards';
            setTimeout(() => {
                targetOverlay.remove();
                if (targetOverlay === this.loadingOverlay) {
                    this.loadingOverlay = null;
                }
            }, 300);
        }
    }

    // ==================== 骨架屏组件 ====================
    createSkeleton(type = 'default', count = 1) {
        const skeletons = {
            'sku-item': () => `
                <div class="loading-skeleton" style="width: 120px; height: 32px; margin: 4px;"></div>
            `,
            'card': () => `
                <div class="card">
                    <div class="card-header">
                        <div class="loading-skeleton" style="width: 150px; height: 20px;"></div>
                    </div>
                    <div class="card-body">
                        <div class="loading-skeleton" style="width: 100%; height: 16px; margin-bottom: 8px;"></div>
                        <div class="loading-skeleton" style="width: 80%; height: 16px; margin-bottom: 8px;"></div>
                        <div class="loading-skeleton" style="width: 60%; height: 16px;"></div>
                    </div>
                </div>
            `,
            'chart': () => `
                <div class="chart-container">
                    <div class="loading-skeleton" style="width: 200px; height: 24px; margin-bottom: 16px;"></div>
                    <div class="loading-skeleton" style="width: 100%; height: 300px;"></div>
                </div>
            `,
            'table-row': () => `
                <div style="display: flex; gap: 16px; padding: 12px; border-bottom: 1px solid #e5e7eb;">
                    <div class="loading-skeleton" style="width: 60px; height: 16px;"></div>
                    <div class="loading-skeleton" style="width: 120px; height: 16px;"></div>
                    <div class="loading-skeleton" style="width: 80px; height: 16px;"></div>
                    <div class="loading-skeleton" style="width: 100px; height: 16px;"></div>
                </div>
            `,
            'default': () => `
                <div class="loading-skeleton" style="width: 100%; height: 20px; margin-bottom: 8px;"></div>
            `
        };

        const skeletonHtml = skeletons[type] || skeletons['default'];
        return Array(count).fill(0).map(() => skeletonHtml()).join('');
    }

    showSkeleton(target, type = 'default', count = 1) {
        if (!target) return;
        
        const skeletonContainer = document.createElement('div');
        skeletonContainer.className = 'skeleton-container';
        skeletonContainer.innerHTML = this.createSkeleton(type, count);
        
        target.innerHTML = '';
        target.appendChild(skeletonContainer);
        
        return skeletonContainer;
    }

    hideSkeleton(target) {
        if (!target) return;
        const skeleton = target.querySelector('.skeleton-container');
        if (skeleton) {
            skeleton.remove();
        }
    }

    // ==================== 进度条组件 ====================
    createProgressBar(progress = 0, message = '') {
        const progressBar = document.createElement('div');
        progressBar.className = 'progress-bar-container';
        progressBar.innerHTML = `
            <div class="progress-bar-wrapper">
                <div class="progress-bar-track">
                    <div class="progress-bar-fill" style="width: ${progress}%"></div>
                </div>
                <div class="progress-bar-text">${message} ${progress}%</div>
            </div>
        `;

        const styles = `
            .progress-bar-container {
                padding: 1rem;
                background: rgba(255, 255, 255, 0.95);
                border-radius: 0.5rem;
                border: 1px solid rgba(168, 85, 247, 0.2);
            }
            
            .progress-bar-track {
                width: 100%;
                height: 8px;
                background: rgba(168, 85, 247, 0.1);
                border-radius: 4px;
                overflow: hidden;
                margin-bottom: 0.5rem;
            }
            
            .progress-bar-fill {
                height: 100%;
                background: linear-gradient(90deg, #a855f7, #9333ea);
                border-radius: 4px;
                transition: width 0.3s ease;
            }
            
            .progress-bar-text {
                font-size: 0.875rem;
                color: #7c3aed;
                text-align: center;
                font-weight: 500;
            }
        `;

        if (!document.getElementById('progress-styles')) {
            const styleSheet = document.createElement('style');
            styleSheet.id = 'progress-styles';
            styleSheet.textContent = styles;
            document.head.appendChild(styleSheet);
        }

        return progressBar;
    }

    updateProgress(progressBar, progress, message = '') {
        const fill = progressBar.querySelector('.progress-bar-fill');
        const text = progressBar.querySelector('.progress-bar-text');
        
        if (fill) fill.style.width = `${progress}%`;
        if (text) text.textContent = `${message} ${progress}%`;
    }

    // ==================== 确认对话框 ====================
    showConfirm(message, title = '确认操作') {
        return new Promise((resolve) => {
            const modal = document.createElement('div');
            modal.className = 'modal-overlay';
            modal.innerHTML = `
                <div class="modal-content">
                    <div class="modal-header">
                        <h3>${title}</h3>
                    </div>
                    <div class="modal-body">
                        <p>${message}</p>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" onclick="closeModal(false)">取消</button>
                        <button class="btn btn-primary" onclick="closeModal(true)">确认</button>
                    </div>
                </div>
            `;

            const styles = `
                .modal-overlay {
                    position: fixed;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: rgba(0, 0, 0, 0.5);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    z-index: 10000;
                    animation: fadeIn 0.3s ease;
                }
                
                .modal-content {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 20px 25px rgba(0, 0, 0, 0.2);
                    max-width: 400px;
                    width: 90%;
                    border: 1px solid rgba(168, 85, 247, 0.2);
                }
                
                .modal-header {
                    padding: 1.5rem 1.5rem 0;
                }
                
                .modal-header h3 {
                    margin: 0;
                    color: #7c3aed;
                    font-size: 1.125rem;
                    font-weight: 600;
                }
                
                .modal-body {
                    padding: 1rem 1.5rem;
                }
                
                .modal-body p {
                    margin: 0;
                    color: #374151;
                    line-height: 1.6;
                }
                
                .modal-footer {
                    padding: 0 1.5rem 1.5rem;
                    display: flex;
                    gap: 0.75rem;
                    justify-content: flex-end;
                }
            `;

            if (!document.getElementById('modal-styles')) {
                const styleSheet = document.createElement('style');
                styleSheet.id = 'modal-styles';
                styleSheet.textContent = styles;
                document.head.appendChild(styleSheet);
            }

            window.closeModal = (result) => {
                modal.remove();
                delete window.closeModal;
                resolve(result);
            };

            document.body.appendChild(modal);
        });
    }
}

// 全局实例
window.UI = new UIComponents();

// 添加CSS动画
const additionalStyles = `
    @keyframes slideOut {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
    
    @keyframes fadeOut {
        from { opacity: 1; }
        to { opacity: 0; }
    }
`;

const styleSheet = document.createElement('style');
styleSheet.textContent = additionalStyles;
document.head.appendChild(styleSheet);
