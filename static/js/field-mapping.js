/**
 * 前端字段映射工具
 * 用于处理API返回的英文字段名，映射到前端显示
 */

// 字段映射配置
const FIELD_MAPPING = {
    // 基本信息字段
    'sku': 'SKU',
    'supplier_code': '供应商代码',
    'product_name': '产品名称',
    'product_image': '产品主图',

    // 库存相关字段
    'quantity': '数量',
    'account_stock': '账号内库存',
    'account_inventory': '账号内库存', // 兼容旧字段名
    'available_stock': '可售库存',
    'estimated_restock_date': '预计补货时间',
    'estimated_restock_qty': '预计补货数量',
    'estimated_restock_quantity': '预计补货数量', // 兼容旧字段名
    'first_stock_date': '首次到库时间',

    // 订单相关字段
    'order_date': '下单时间',
    'order_amount': '订单金额',
    'product_quantity': '产品数量',
    'platform_channel': '平台渠道',
    'shop_account': '店铺账号',

    // 产品详情字段
    'product_group': '产品分组',
    'unit_price_usd': '单价_USD',
    'total_price_usd': '总价_USD',
    'shipping_fee_usd': '单件物流费_USD',
    'discount_rate': '折扣率',
    'giga_index': 'GIGA_Index',
    'package_count': '包裹数',
    'length_inch': '产品长度_英寸',
    'width_inch': '产品宽度_英寸',
    'height_inch': '产品高度_英寸',
    'weight_pound': '实际重量_磅'
};

// 反向映射（中文到英文）
const REVERSE_FIELD_MAPPING = {};
Object.keys(FIELD_MAPPING).forEach(key => {
    REVERSE_FIELD_MAPPING[FIELD_MAPPING[key]] = key;
});

/**
 * 将API返回的英文字段数据转换为中文字段
 * @param {Object} data - API返回的数据对象
 * @returns {Object} 转换后的数据对象
 */
function mapFieldsToChinese(data) {
    if (!data || typeof data !== 'object') {
        return data;
    }
    
    const mappedData = {};
    
    // 处理数组
    if (Array.isArray(data)) {
        return data.map(item => mapFieldsToChinese(item));
    }
    
    // 处理对象
    Object.keys(data).forEach(key => {
        const chineseKey = FIELD_MAPPING[key] || key;
        mappedData[chineseKey] = data[key];
    });
    
    return mappedData;
}

/**
 * 将中文字段转换为英文字段（用于发送API请求）
 * @param {Object} data - 包含中文字段的数据对象
 * @returns {Object} 转换后的数据对象
 */
function mapFieldsToEnglish(data) {
    if (!data || typeof data !== 'object') {
        return data;
    }
    
    const mappedData = {};
    
    // 处理数组
    if (Array.isArray(data)) {
        return data.map(item => mapFieldsToEnglish(item));
    }
    
    // 处理对象
    Object.keys(data).forEach(key => {
        const englishKey = REVERSE_FIELD_MAPPING[key] || key;
        mappedData[englishKey] = data[key];
    });
    
    return mappedData;
}

/**
 * 安全获取对象属性值，支持中英文字段名
 * @param {Object} obj - 数据对象
 * @param {string} field - 字段名（中文或英文）
 * @param {*} defaultValue - 默认值
 * @returns {*} 字段值
 */
function safeGet(obj, field, defaultValue = '-') {
    if (!obj || typeof obj !== 'object') {
        return defaultValue;
    }
    
    // 直接获取
    if (obj.hasOwnProperty(field)) {
        return obj[field] ?? defaultValue;
    }
    
    // 尝试英文字段名
    const englishField = REVERSE_FIELD_MAPPING[field];
    if (englishField && obj.hasOwnProperty(englishField)) {
        return obj[englishField] ?? defaultValue;
    }
    
    // 尝试中文字段名
    const chineseField = FIELD_MAPPING[field];
    if (chineseField && obj.hasOwnProperty(chineseField)) {
        return obj[chineseField] ?? defaultValue;
    }
    
    return defaultValue;
}

/**
 * 增强的fetch函数，自动处理字段映射
 * @param {string} url - API URL
 * @param {Object} options - fetch选项
 * @returns {Promise} Promise对象
 */
function fetchWithMapping(url, options = {}) {
    return fetch(url, options)
        .then(response => {
            if (!response.ok) {
                if (response.status === 302 || response.status === 401) {
                    throw new Error('需要登录才能访问，请刷新页面重新登录');
                }
                throw new Error('API请求失败: ' + response.status);
            }
            return response.json();
        })
        .then(data => {
            // 自动转换字段名为中文
            return mapFieldsToChinese(data);
        });
}

/**
 * 格式化日期显示
 * @param {string} dateStr - 日期字符串
 * @returns {string} 格式化后的日期
 */
function formatDate(dateStr) {
    if (!dateStr || dateStr === '-' || dateStr === 'null' || dateStr === 'undefined') {
        return '-';
    }
    
    try {
        const date = new Date(dateStr);
        if (isNaN(date.getTime())) {
            return dateStr; // 如果不是有效日期，返回原字符串
        }
        return date.toLocaleDateString('zh-CN');
    } catch (e) {
        return dateStr;
    }
}

/**
 * 格式化数字显示
 * @param {number|string} num - 数字
 * @param {number} decimals - 小数位数
 * @returns {string} 格式化后的数字
 */
function formatNumber(num, decimals = 0) {
    if (num === null || num === undefined || num === '' || num === '-') {
        return '-';
    }
    
    const number = parseFloat(num);
    if (isNaN(number)) {
        return '-';
    }
    
    return number.toLocaleString('zh-CN', {
        minimumFractionDigits: decimals,
        maximumFractionDigits: decimals
    });
}

/**
 * 获取SKU字段值（兼容中英文）
 */
function getSKU(item) {
    return safeGet(item, 'sku', safeGet(item, 'SKU', ''));
}

/**
 * 获取供应商代码字段值（兼容中英文）
 */
function getSupplierCode(item) {
    return safeGet(item, 'supplier_code', safeGet(item, '供应商代码', ''));
}

/**
 * 获取产品图片字段值（兼容中英文）
 */
function getProductImage(item) {
    return safeGet(item, 'product_image', safeGet(item, '产品主图', ''));
}

// 导出到全局作用域
window.mapFieldsToChinese = mapFieldsToChinese;
window.mapFieldsToEnglish = mapFieldsToEnglish;
window.safeGet = safeGet;
window.fetchWithMapping = fetchWithMapping;
window.formatDate = formatDate;
window.formatNumber = formatNumber;
window.getSKU = getSKU;
window.getSupplierCode = getSupplierCode;
window.getProductImage = getProductImage;
window.FIELD_MAPPING = FIELD_MAPPING;
window.REVERSE_FIELD_MAPPING = REVERSE_FIELD_MAPPING;
