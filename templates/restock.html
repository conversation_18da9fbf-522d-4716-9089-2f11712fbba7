{% extends "base.html" %}
{% block head %}
{% endblock %}

{% block content %}
<div class="bg-white/80 shadow-xl rounded-3xl p-8 mb-8">
  <div class="flex justify-between items-center mb-6">
    <div class="flex items-center gap-4">
      <h2 class="text-2xl font-bold text-blue-600">智能补货周期表</h2>
      <div class="flex items-center gap-2">
        <span id="cache-status" class="text-xs px-2 py-1 rounded-full bg-gray-100 text-gray-600">
          <i class="fas fa-circle text-gray-400"></i> 加载中
        </span>
        <button id="refresh-data" class="text-xs px-3 py-1 bg-blue-100 text-blue-600 rounded-full hover:bg-blue-200 transition-colors" title="刷新数据">
          <i class="fas fa-refresh"></i> 刷新
        </button>
      </div>
    </div>
    <div class="flex items-center gap-6">
      <div class="text-sm text-gray-500" id="date-range-info"></div>
      <div class="flex items-center gap-4 text-xs">
        <div class="flex items-center gap-1">
          <div class="w-3 h-3 bg-blue-500 rounded"></div>
          <span class="text-gray-600">预计到货</span>
        </div>
        <div class="flex items-center gap-1">
          <div class="w-3 h-3 bg-gray-400 rounded"></div>
          <span class="text-gray-600">实际已到货</span>
        </div>
      </div>
    </div>
  </div>

  <!-- 筛选面板 -->
  <div class="card mb-6">
    <div class="card-header">
      <h3 class="text-lg font-semibold text-blue-700">筛选条件</h3>
    </div>
    <div class="card-body">
      <div class="flex flex-wrap gap-4 items-end">
        <div class="flex-1 min-w-40">
          <label class="block text-sm font-medium text-gray-700 mb-2">平台筛选</label>
          <select id="platform-filter" class="input">
            <option value="">全部平台</option>
          </select>
        </div>
        <div class="flex-1 min-w-40">
          <label class="block text-sm font-medium text-gray-700 mb-2">店铺筛选</label>
          <select id="store-filter" class="input">
            <option value="">全部店铺</option>
          </select>
        </div>
        <div class="flex-1 min-w-40">
          <label class="block text-sm font-medium text-gray-700 mb-2">上架状态</label>
          <select id="listing-status-filter" class="input">
            <option value="">全部商品</option>
            <option value="listed">已上架</option>
            <option value="unlisted">未上架</option>
          </select>
        </div>
        <button id="reset-filters" class="btn btn-secondary">
          <i class="fas fa-refresh"></i> 重置筛选
        </button>
      </div>
      <div class="mt-4 flex items-center gap-4 text-sm text-gray-600">
        <div class="flex items-center gap-2">
          <div class="w-4 h-4 bg-blue-500 rounded"></div>
          <span>已上架商品</span>
        </div>
        <div class="flex items-center gap-2">
          <span id="total-listed-count" class="font-semibold">0</span>
          <span>个已上架SKU</span>
        </div>
      </div>
    </div>
  </div>
  
  <!-- 月份导航 -->
  <div class="flex justify-center items-center gap-4 mb-6">
    <button id="prev-month" class="px-4 py-2 bg-blue-100 text-blue-600 rounded-lg hover:bg-blue-200 transition-colors">
      ← 上个月
    </button>
    <span id="current-month-display" class="text-lg font-semibold text-gray-700 min-w-32 text-center"></span>
    <button id="next-month" class="px-4 py-2 bg-blue-100 text-blue-600 rounded-lg hover:bg-blue-200 transition-colors">
      下个月 →
    </button>
  </div>
  
  <div class="overflow-x-auto">
    <table class="min-w-full text-center border-separate border-spacing-0">
      <thead>
        <tr>
          <th class="px-4 py-3 bg-blue-50 text-blue-500 text-lg font-semibold rounded-t-2xl">周一</th>
          <th class="px-4 py-3 bg-blue-50 text-blue-500 text-lg font-semibold rounded-t-2xl">周二</th>
          <th class="px-4 py-3 bg-blue-50 text-blue-500 text-lg font-semibold rounded-t-2xl">周三</th>
          <th class="px-4 py-3 bg-blue-50 text-blue-500 text-lg font-semibold rounded-t-2xl">周四</th>
          <th class="px-4 py-3 bg-blue-50 text-blue-500 text-lg font-semibold rounded-t-2xl">周五</th>
          <th class="px-4 py-3 bg-blue-50 text-blue-500 text-lg font-semibold rounded-t-2xl">周六</th>
          <th class="px-4 py-3 bg-blue-50 text-blue-500 text-lg font-semibold rounded-t-2xl">周日</th>
        </tr>
      </thead>
      <tbody id="calendar-body">
        <!-- 数据将通过JavaScript动态加载 -->
      </tbody>
    </table>
  </div>
  <!-- 动态浮层展示所有SKU -->
  <div id="sku-float-list" style="display:none;position:fixed;z-index:9999;min-width:120px;max-width:220px;background:#fff;border-radius:16px;box-shadow:0 4px 24px rgba(0,0,0,0.12);padding:16px 12px;">
    <div id="sku-float-list-content"></div>
  </div>
</div>

<style>
.sku-block {
  min-width: 120px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #3b82f6;  /* 蓝色：预计到货 */
  color: white;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  margin-bottom: 4px;
  margin-right: 4px;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
  padding: 0 8px;
  text-align: center;
  border: 2px solid #1d4ed8;
}

.sku-block:hover {
  background: #2563eb;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
}

.sku-block.unlisted {
  background: #e5e7eb;  /* 灰色：未上架 */
  color: #6b7280;
  border-color: #d1d5db;
}

.sku-block.unlisted:hover {
  background: #d1d5db;
}

.sku-block.actually-arrived {
  background: #9ca3af;  /* 灰色：实际已到货 */
  color: white;
  border-color: #6b7280;
}

.sku-block.actually-arrived:hover {
  background: #6b7280;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(156, 163, 175, 0.3);
}

/* 悬浮卡片样式 */
.sku-tooltip {
  position: fixed;
  z-index: 10000;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  padding: 16px;
  min-width: 420px;
  max-width: 520px;
  pointer-events: none;
  opacity: 0;
  transform: translateY(10px);
  transition: all 0.2s ease;
}

.sku-tooltip.show {
  opacity: 1;
  transform: translateY(0);
}

.sku-tooltip-content {
  display: flex;
  gap: 12px;
}

.sku-tooltip-image {
  flex-shrink: 0;
  width: 80px;
  text-align: center;
}

.sku-tooltip-image img {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.sku-tooltip-info {
  flex: 1;
  font-size: 12px;
}

.sku-tooltip-shops {
  flex-shrink: 0;
  width: 140px;
  font-size: 11px;
}

.shop-item {
  background: #f3f4f6;
  padding: 4px 8px;
  border-radius: 6px;
  margin-bottom: 4px;
  border-left: 3px solid #3b82f6;
}

.platform-amazon { border-left-color: #ff9900; }
.platform-ebay { border-left-color: #0064d2; }
.platform-walmart { border-left-color: #004c91; }

/* 隐藏原来的更多按钮相关样式 */
.sku-more-btn {
  display: none;
}

.sku-float-list-item {
  display: none;
}

/* 加载更多按钮样式 */
.load-more-btn {
  background: #e0e7ff;
  color: #3b82f6;
  border: 1px solid #c7d2fe;
  border-radius: 8px;
  padding: 4px 12px;
  font-size: 11px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.load-more-btn:hover {
  background: #c7d2fe;
  transform: translateY(-1px);
}
</style>

<script>
// 全局变量
let restockCalendarData = {};
let enhancedRestockData = {};
let currentViewYear, currentViewMonth;
let availableMonths = [];
let allPlatforms = [];
let allStores = [];
let platformStoreMapping = {}; // 平台店铺映射
let currentFilters = {
  platform: '',
  store: '',
  listingStatus: ''
};

// SKU悬浮卡片
let skuTooltip = null;

// 前端缓存配置
const RESTOCK_CACHE_CONFIG = {
  key: 'restock_calendar_cache',
  ttl: 30 * 60 * 1000, // 30分钟缓存
  version: '1.0'
};

// 缓存管理器
const RestockCacheManager = {
  // 保存数据到本地缓存
  saveToCache: function(data) {
    try {
      const cacheData = {
        data: data,
        timestamp: Date.now(),
        version: RESTOCK_CACHE_CONFIG.version
      };
      localStorage.setItem(RESTOCK_CACHE_CONFIG.key, JSON.stringify(cacheData));
      console.log('补货数据已缓存到本地');
    } catch (e) {
      console.warn('保存缓存失败:', e);
    }
  },

  // 从本地缓存获取数据
  getFromCache: function() {
    try {
      const cached = localStorage.getItem(RESTOCK_CACHE_CONFIG.key);
      if (!cached) return null;

      const cacheData = JSON.parse(cached);

      // 检查版本
      if (cacheData.version !== RESTOCK_CACHE_CONFIG.version) {
        this.clearCache();
        return null;
      }

      // 检查是否过期
      const age = Date.now() - cacheData.timestamp;
      if (age > RESTOCK_CACHE_CONFIG.ttl) {
        this.clearCache();
        return null;
      }

      console.log('从本地缓存加载补货数据');
      return cacheData.data;
    } catch (e) {
      console.warn('读取缓存失败:', e);
      this.clearCache();
      return null;
    }
  },

  // 清除缓存
  clearCache: function() {
    try {
      localStorage.removeItem(RESTOCK_CACHE_CONFIG.key);
    } catch (e) {
      console.warn('清除缓存失败:', e);
    }
  }
};

// 加载增强的补货周期数据（带缓存优化）
function loadRestockCalendar(forceRefresh = false) {
  // 如果不是强制刷新，先尝试从缓存加载
  if (!forceRefresh) {
    const cachedData = RestockCacheManager.getFromCache();
    if (cachedData) {
      processRestockData(cachedData);

      // 更新缓存状态
      updateCacheStatus('cache', cachedData.last_updated);

      // 后台异步更新缓存
      setTimeout(() => {
        loadRestockCalendar(true);
      }, 1000);

      return;
    }
  }

  // 显示加载状态
  if (window.UI) {
    UI.showLoading('正在加载补货数据...', document.getElementById('calendar-body'));
  }

  // 使用合并后的API，enhanced=true 获取增强数据
  fetch('/api/restock_calendar?enhanced=true')
    .then(response => {
      if (!response.ok) {
        console.warn('增强模式失败，回退到基础模式:', response.status);
        // 回退到基础模式
        return fetch('/api/restock_calendar').then(fallbackResponse => {
          if (!fallbackResponse.ok) {
            throw new Error(`基础API也失败: HTTP ${fallbackResponse.status}: ${fallbackResponse.statusText}`);
          }
          return fallbackResponse.json().then(data => {
            // 转换基础API数据格式为增强格式
            return {
              calendar_data: data,
              platforms: ['Amazon'],
              stores: ['默认店铺'],
              platform_store_mapping: {},
              total_listed_skus: 0,
              last_updated: new Date().toISOString(),
              fallback_mode: true
            };
          });
        });
      }
      if (response.redirected) {
        throw new Error('需要重新登录');
      }
      return response.json();
    })
    .then(data => {
      // 保存到缓存
      RestockCacheManager.saveToCache(data);

      // 处理数据
      processRestockData(data);

      // 更新缓存状态
      updateCacheStatus('server', data.last_updated);
    })
    .catch(error => {
      console.error('加载补货数据失败:', error);
      document.getElementById('calendar-body').innerHTML =
        '<tr><td colspan="7" class="py-12 text-center text-red-500">数据加载失败</td></tr>';

      // 更新缓存状态
      updateCacheStatus('error');

      if (window.UI) {
        UI.hideLoading();
        UI.showToast('数据加载失败', 'error', 3000);
      }
    });
}

// 处理补货数据的通用函数
function processRestockData(data) {
  enhancedRestockData = data.calendar_data;
  allPlatforms = data.platforms || [];
  allStores = data.stores || [];
  platformStoreMapping = data.platform_store_mapping || {};

  console.log('增强补货数据处理完成:', Object.keys(enhancedRestockData).length + '天');
  console.log('平台列表:', allPlatforms, '类型:', typeof allPlatforms, '长度:', allPlatforms.length);
  console.log('店铺列表:', allStores, '类型:', typeof allStores, '长度:', allStores.length);
  console.log('平台店铺映射:', platformStoreMapping, '类型:', typeof platformStoreMapping);

  // 检查数据完整性
  if (!Array.isArray(allPlatforms)) {
    console.error('平台列表不是数组:', allPlatforms);
    allPlatforms = [];
  }
  if (!Array.isArray(allStores)) {
    console.error('店铺列表不是数组:', allStores);
    allStores = [];
  }
  if (typeof platformStoreMapping !== 'object' || platformStoreMapping === null) {
    console.error('平台店铺映射不是对象:', platformStoreMapping);
    platformStoreMapping = {};
  }

  // 更新筛选器选项
  updateFilterOptions();

  // 更新统计信息
  document.getElementById('total-listed-count').textContent = data.total_listed_skus || 0;

  // 分析数据中的所有月份
  analyzeAvailableMonths(enhancedRestockData);

  // 设置初始显示月份
  initializeCurrentMonth();

  // 渲染当前月份
  renderCurrentMonth();

  // 更新信息显示
  updateDateRangeInfo();

  // 隐藏加载状态
  if (window.UI) {
    UI.hideLoading();
    UI.showToast('补货数据加载完成', 'success', 2000);
  }
}

// 更新缓存状态显示
function updateCacheStatus(source, lastUpdated = null) {
  const statusElement = document.getElementById('cache-status');
  if (!statusElement) return;

  let statusText, statusClass;

  switch (source) {
    case 'cache':
      statusText = '<i class="fas fa-circle text-green-400"></i> 缓存';
      statusClass = 'bg-green-100 text-green-600';
      break;
    case 'server':
      statusText = '<i class="fas fa-circle text-blue-400"></i> 服务器';
      statusClass = 'bg-blue-100 text-blue-600';
      break;
    case 'error':
      statusText = '<i class="fas fa-circle text-red-400"></i> 错误';
      statusClass = 'bg-red-100 text-red-600';
      break;
    default:
      statusText = '<i class="fas fa-circle text-gray-400"></i> 加载中';
      statusClass = 'bg-gray-100 text-gray-600';
  }

  statusElement.innerHTML = statusText;
  statusElement.className = `text-xs px-2 py-1 rounded-full ${statusClass}`;

  if (lastUpdated) {
    statusElement.title = `最后更新: ${lastUpdated}`;
  }
}

// 更新筛选器选项
function updateFilterOptions() {
  const platformFilter = document.getElementById('platform-filter');
  const storeFilter = document.getElementById('store-filter');

  // 清空现有选项
  platformFilter.innerHTML = '<option value="">全部平台</option>';
  storeFilter.innerHTML = '<option value="">全部店铺</option>';

  // 添加平台选项
  if (Array.isArray(allPlatforms) && allPlatforms.length > 0) {
    allPlatforms.forEach(platform => {
      const option = document.createElement('option');
      option.value = platform;
      option.textContent = platform;
      platformFilter.appendChild(option);
    });
  } else {
    console.warn('平台列表为空或无效:', allPlatforms);
  }

  // 添加店铺选项
  if (Array.isArray(allStores) && allStores.length > 0) {
    allStores.forEach(store => {
      const option = document.createElement('option');
      option.value = store;
      option.textContent = store;
      storeFilter.appendChild(option);
    });
  } else {
    console.warn('店铺列表为空或无效:', allStores);
    // 如果没有店铺数据，添加一个提示选项
    const noStoreOption = document.createElement('option');
    noStoreOption.value = '';
    noStoreOption.textContent = '暂无店铺数据';
    noStoreOption.disabled = true;
    storeFilter.appendChild(noStoreOption);
  }
}

// 更新店铺筛选器选项（根据选中的平台）
function updateStoreFilterOptions(selectedPlatform) {
  const storeFilter = document.getElementById('store-filter');

  // 清空现有选项
  storeFilter.innerHTML = '<option value="">全部店铺</option>';

  if (selectedPlatform && platformStoreMapping && platformStoreMapping[selectedPlatform]) {
    // 只显示选中平台的店铺
    const platformStores = platformStoreMapping[selectedPlatform];
    if (Array.isArray(platformStores) && platformStores.length > 0) {
      platformStores.forEach(store => {
        const option = document.createElement('option');
        option.value = store;
        option.textContent = store;
        storeFilter.appendChild(option);
      });
    } else {
      // 该平台没有店铺
      const noStoreOption = document.createElement('option');
      noStoreOption.value = '';
      noStoreOption.textContent = '该平台暂无店铺';
      noStoreOption.disabled = true;
      storeFilter.appendChild(noStoreOption);
    }
  } else {
    // 显示所有店铺
    if (Array.isArray(allStores) && allStores.length > 0) {
      allStores.forEach(store => {
        const option = document.createElement('option');
        option.value = store;
        option.textContent = store;
        storeFilter.appendChild(option);
      });
    } else {
      // 没有任何店铺数据
      const noStoreOption = document.createElement('option');
      noStoreOption.value = '';
      noStoreOption.textContent = '暂无店铺数据';
      noStoreOption.disabled = true;
      storeFilter.appendChild(noStoreOption);
    }
  }
}

// 根据店铺获取对应的平台
function getPlatformByStore(storeName) {
  for (const [platform, stores] of Object.entries(platformStoreMapping)) {
    if (stores.includes(storeName)) {
      return platform;
    }
  }
  return null;
}

// 筛选SKU数据
function filterSkuData(skus) {
  if (!currentFilters.platform && !currentFilters.store && !currentFilters.listingStatus) {
    return skus;
  }

  return skus.filter(skuData => {
    // 兼容不同的数据格式 - 支持 shop_info 和 shop_skus 字段
    const shopInfo = skuData.shop_info || skuData.shop_skus || [];
    const isListed = skuData.is_listed || skuData.has_shop_sku || false;

    // 平台筛选
    if (currentFilters.platform) {
      const hasMatchingPlatform = shopInfo.some(info => info.platform === currentFilters.platform);
      if (!hasMatchingPlatform) return false;
    }

    // 店铺筛选
    if (currentFilters.store) {
      const hasMatchingStore = shopInfo.some(info => info.store === currentFilters.store);
      if (!hasMatchingStore) return false;
    }

    // 上架状态筛选
    if (currentFilters.listingStatus) {
      if (currentFilters.listingStatus === 'listed' && !isListed) return false;
      if (currentFilters.listingStatus === 'unlisted' && isListed) return false;
    }

    return true;
  });
}

// 分析数据中包含的所有月份
function analyzeAvailableMonths(data) {
  const monthSet = new Set();
  
  Object.keys(data).forEach(dateStr => {
    const date = new Date(dateStr);
    if (!isNaN(date.getTime())) {
      const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
      monthSet.add(monthKey);
    }
  });
  
  availableMonths = Array.from(monthSet).sort();
  console.log('可用月份:', availableMonths);
}

// 初始化当前显示月份
function initializeCurrentMonth() {
  const today = new Date();
  const currentMonthKey = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}`;
  
  if (availableMonths.includes(currentMonthKey)) {
    // 如果当前月有数据，显示当前月
    currentViewYear = today.getFullYear();
    currentViewMonth = today.getMonth();
  } else if (availableMonths.length > 0) {
    // 否则显示第一个有数据的月份
    const firstMonth = availableMonths[0];
    const [year, month] = firstMonth.split('-');
    currentViewYear = parseInt(year);
    currentViewMonth = parseInt(month) - 1; // JavaScript月份从0开始
  } else {
    // 没有数据，显示当前月
    currentViewYear = today.getFullYear();
    currentViewMonth = today.getMonth();
  }
}

// 渲染当前月份的日历
function renderCurrentMonth() {
  const tbody = document.getElementById('calendar-body');
  tbody.innerHTML = '';
  
  // 获取当前显示月份的第一天和最后一天
  const firstDay = new Date(currentViewYear, currentViewMonth, 1);
  const lastDay = new Date(currentViewYear, currentViewMonth + 1, 0);
  
  // 计算第一天是星期几（0=周日，1=周一...6=周六，转换为1=周一...7=周日）
  let startDay = firstDay.getDay();
  if (startDay === 0) startDay = 7; // 将周日从0调整为7
  
  // 创建日历网格
  let currentDate = new Date(firstDay);
  let currentWeek = [];
  let allWeeks = [];
  
  // 如果月份不是从周一开始，填充前面的空白
  for (let i = 1; i < startDay; i++) {
    currentWeek.push(null);
  }
  
  // 填充本月的所有日期
  while (currentDate <= lastDay) {
    if (currentWeek.length === 7) {
      allWeeks.push([...currentWeek]);
      currentWeek = [];
    }
    
    currentWeek.push(new Date(currentDate));
    currentDate.setDate(currentDate.getDate() + 1);
  }
  
  // 填充最后一周的剩余空白
  while (currentWeek.length < 7) {
    currentWeek.push(null);
  }
  if (currentWeek.length > 0) {
    allWeeks.push(currentWeek);
  }
  
  // 渲染日历
  allWeeks.forEach(week => {
    const tr = document.createElement('tr');
    
    week.forEach(date => {
      const td = document.createElement('td');
      
      if (date) {
        // 使用本地时区的日期字符串，避免UTC时区偏移问题
        const dateStr = date.getFullYear() + '-' +
                       String(date.getMonth() + 1).padStart(2, '0') + '-' +
                       String(date.getDate()).padStart(2, '0');
        const allDaySkus = enhancedRestockData[dateStr] || [];
        const filteredSkus = filterSkuData(allDaySkus);

        // 获取今天的本地日期字符串
        const today = new Date();
        const todayStr = today.getFullYear() + '-' +
                        String(today.getMonth() + 1).padStart(2, '0') + '-' +
                        String(today.getDate()).padStart(2, '0');
        const isToday = dateStr === todayStr;
        const hasData = filteredSkus.length > 0;

        // 根据是否有数据和是否是今天设置样式
        let cellClass = 'px-4 py-6 align-top rounded-2xl m-2 min-h-24';
        if (hasData) {
          cellClass += ' bg-blue-50 shadow-md border border-blue-200';
        } else {
          cellClass += ' bg-white/80 shadow';
        }
        if (isToday) {
          cellClass += ' ring-2 ring-blue-400';
        }

        td.className = cellClass;

        let dayDisplay = `<div class="font-bold text-lg mb-3 ${hasData ? 'text-blue-600' : 'text-gray-600'}">${date.getDate()}日</div>`;

        if (hasData) {
          // 去重处理
          const uniqueSkus = [];
          const seenSkus = new Set();
          for (const skuData of filteredSkus) {
            // 兼容不同的数据格式 - 支持 sku 和 SKU 字段
            const skuValue = skuData.sku || skuData.SKU;
            if (skuValue && !seenSkus.has(skuValue)) {
              seenSkus.add(skuValue);
              // 标准化数据格式
              const normalizedSkuData = {
                ...skuData,
                sku: skuValue,
                SKU: skuValue
              };
              uniqueSkus.push(normalizedSkuData);
            }
          }

          const displaySkus = uniqueSkus.slice(0, 10); // 最多显示10个
          const hasMore = uniqueSkus.length > 10;

          dayDisplay += `
            <div class="flex flex-col gap-2">
              <div class="text-xs text-blue-600 font-medium mb-1">${uniqueSkus.length}个已上架SKU</div>
              <div class="flex flex-wrap gap-1" id="sku-container-${dateStr}">
                ${displaySkus.map(skuData => {
                  const isActuallyArrived = skuData.is_actually_arrived || false;
                  const skuClass = isActuallyArrived ? 'sku-block actually-arrived' : 'sku-block';
                  return `
                    <div class="${skuClass}"
                         data-sku="${skuData.sku || skuData.SKU}"
                         data-sku-info='${JSON.stringify(skuData)}'
                         onmouseenter="showSkuTooltip(event, this)"
                         onmouseleave="hideSkuTooltip()"
                         title="点击查看详情">
                      ${skuData.sku || skuData.SKU}
                    </div>
                  `;
                }).join('')}
              </div>
              ${hasMore ? `
                <div class="text-center mt-2">
                  <button class="load-more-btn" onclick="loadMoreSkus('${dateStr}', ${JSON.stringify(uniqueSkus).replace(/"/g, '&quot;')})">
                    加载更多 (还有${uniqueSkus.length - 10}个)
                  </button>
                </div>
              ` : ''}
            </div>
          `;
        }
        
        td.innerHTML = dayDisplay;
      } else {
        // 空白单元格
        td.className = 'px-4 py-6 align-top bg-gray-50/50 rounded-2xl m-2 min-h-24';
        td.innerHTML = '';
      }
      
      tr.appendChild(td);
    });
    
    tbody.appendChild(tr);
  });
  
  // 更新月份显示
  updateCurrentMonthDisplay();
}

// 更新当前月份显示
function updateCurrentMonthDisplay() {
  const monthNames = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'];
  const displayText = `${currentViewYear}年${monthNames[currentViewMonth]}`;
  document.getElementById('current-month-display').textContent = displayText;
  
  // 检查是否可以切换到上/下个月
  const currentMonthKey = `${currentViewYear}-${String(currentViewMonth + 1).padStart(2, '0')}`;
  const currentIndex = availableMonths.indexOf(currentMonthKey);
  
  document.getElementById('prev-month').disabled = currentIndex <= 0;
  document.getElementById('next-month').disabled = currentIndex >= availableMonths.length - 1 || currentIndex === -1;
}

// 更新日期范围信息
function updateDateRangeInfo() {
  if (availableMonths.length > 0) {
    const firstMonth = availableMonths[0];
    const lastMonth = availableMonths[availableMonths.length - 1];
    document.getElementById('date-range-info').textContent = 
      `数据范围: ${firstMonth} 至 ${lastMonth} (共${availableMonths.length}个月)`;
  } else {
    document.getElementById('date-range-info').textContent = '暂无补货数据';
  }
}

// 切换到上个月
function goToPrevMonth() {
  const currentMonthKey = `${currentViewYear}-${String(currentViewMonth + 1).padStart(2, '0')}`;
  const currentIndex = availableMonths.indexOf(currentMonthKey);
  
  if (currentIndex > 0) {
    const prevMonth = availableMonths[currentIndex - 1];
    const [year, month] = prevMonth.split('-');
    currentViewYear = parseInt(year);
    currentViewMonth = parseInt(month) - 1;
    renderCurrentMonth();
  }
}

// 切换到下个月
function goToNextMonth() {
  const currentMonthKey = `${currentViewYear}-${String(currentViewMonth + 1).padStart(2, '0')}`;
  const currentIndex = availableMonths.indexOf(currentMonthKey);
  
  if (currentIndex >= 0 && currentIndex < availableMonths.length - 1) {
    const nextMonth = availableMonths[currentIndex + 1];
    const [year, month] = nextMonth.split('-');
    currentViewYear = parseInt(year);
    currentViewMonth = parseInt(month) - 1;
    renderCurrentMonth();
  }
}

// 在鼠标位置展示所有SKU浮层
function showSkuFloatList(e, dateStr) {
  e.stopPropagation();
  const skus = enhancedRestockData[dateStr] || [];
  const floatDiv = document.getElementById('sku-float-list');
  const contentDiv = document.getElementById('sku-float-list-content');

  // 增强浮层内容，显示更多信息
  contentDiv.innerHTML = skus.map(sku => `
    <div class='sku-float-list-item'>
      <div class="font-bold">${sku.sku || sku.SKU}</div>
      <div class="text-xs mt-1">补货数量: ${sku.预计补货数量 || sku.estimated_restock_qty || 0}</div>
    </div>
  `).join('');
  
  floatDiv.style.display = 'block';
  
  // 计算浮层位置，防止溢出
  let x = e.clientX;
  let y = e.clientY;
  const winW = window.innerWidth;
  const winH = window.innerHeight;
  const floatW = 220;
  const floatH = Math.min(60 * skus.length + 32, 400);
  
  if (x + floatW > winW) x = winW - floatW - 20;
  if (y + floatH > winH) y = winH - floatH - 20;
  
  floatDiv.style.left = x + 'px';
  floatDiv.style.top = y + 'px';
}

// 点击空白关闭浮层
window.addEventListener('click', function(e) {
  document.getElementById('sku-float-list').style.display = 'none';
});

// SKU悬浮卡片功能
function createSkuTooltip() {
  if (skuTooltip) return;

  skuTooltip = document.createElement('div');
  skuTooltip.className = 'sku-tooltip';
  skuTooltip.innerHTML = `
    <div class="sku-tooltip-content">
      <div class="sku-tooltip-image">
        <img id="tooltip-image" src="" alt="SKU图片" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjYwIiBoZWlnaHQ9IjYwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0yMCAyMEg0MFY0MEgyMFYyMFoiIGZpbGw9IiM5Q0EzQUYiLz4KPC9zdmc+'" />
        <div id="tooltip-sku-code" class="text-xs font-bold mt-1"></div>
      </div>
      <div class="sku-tooltip-info">
        <div class="font-semibold text-gray-800 mb-2">库存信息</div>
        <div class="space-y-1">
          <div class="flex justify-between">
            <span class="text-gray-600">当前库存:</span>
            <span id="tooltip-stock" class="font-medium">-</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-600">预计到货时间:</span>
            <span id="tooltip-arrival-date" class="font-medium text-blue-600">-</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-600">预计到货数量:</span>
            <span id="tooltip-arrival" class="font-medium">-</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-600">实际到货时间:</span>
            <span id="tooltip-actual-arrival" class="font-medium text-green-600">-</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-600">上次售罄:</span>
            <span id="tooltip-last-sale" class="font-medium">-</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-600">售罄前30天销量:</span>
            <span id="tooltip-sales-30d" class="font-medium">-</span>
          </div>
        </div>
      </div>
      <div class="sku-tooltip-shops">
        <div class="font-semibold text-gray-800 mb-2">上架店铺</div>
        <div id="tooltip-shops" class="space-y-1">
          <!-- 动态加载店铺信息 -->
        </div>
      </div>
    </div>
  `;

  document.body.appendChild(skuTooltip);
}

function showSkuTooltip(event, element) {
  createSkuTooltip();

  const skuData = JSON.parse(element.getAttribute('data-sku-info'));
  const sku = skuData.sku || skuData.SKU;

  // 更新卡片内容
  document.getElementById('tooltip-sku-code').textContent = sku;
  document.getElementById('tooltip-stock').textContent = skuData.current_stock || 0;
  document.getElementById('tooltip-arrival-date').textContent = skuData.arrival_date || '-';
  document.getElementById('tooltip-arrival').textContent = skuData.arrival_quantity || skuData.预计补货数量 || 0;
  document.getElementById('tooltip-actual-arrival').textContent = skuData.actual_arrival_date || (skuData.is_actually_arrived ? '已到货' : '-');
  document.getElementById('tooltip-last-sale').textContent = skuData.last_sale_date || '无记录';
  document.getElementById('tooltip-sales-30d').textContent = skuData.sales_before_stockout || 0;

  // 设置SKU图片 - 复用SKU详情页面的图片加载逻辑
  const imgElement = document.getElementById('tooltip-image');

  // 默认占位图片（与SKU详情页面保持一致）
  const DEFAULT_IMG = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjYwIiBoZWlnaHQ9IjYwIiBmaWxsPSIjRjNGNEY2IiBzdHJva2U9IiNEMUQ1REIiIHN0cm9rZS13aWR0aD0iMSIvPgo8Y2lyY2xlIGN4PSIzMCIgY3k9IjI1IiByPSI4IiBmaWxsPSIjOUNBM0FGIi8+CjxwYXRoIGQ9Ik0xNSA0NUwyNSAzNUwzNSAzOUw0NSA0NVoiIGZpbGw9IiM5Q0EzQUYiLz4KPC9zdmc+';

  // 简单的图片加载逻辑（与SKU详情页面保持一致）
  const productImage = skuData.product_image;
  console.log('调试图片数据:', sku, {
    product_image: productImage,
    type: typeof productImage,
    skuData: skuData
  });

  if (productImage && typeof productImage === 'string' && productImage.startsWith('http')) {
    console.log('加载图片:', sku, productImage);

    // 简单的错误处理
    imgElement.onerror = function() {
      console.warn('图片加载失败:', sku, productImage);
      this.src = DEFAULT_IMG;
      this.onerror = null; // 移除错误处理，避免无限循环
    };

    // 成功加载回调
    imgElement.onload = function() {
      console.log('图片加载成功:', sku);
    };

    // 直接使用原始图片URL
    imgElement.src = productImage;
  } else {
    console.log('使用默认图片:', sku, '原因: 无有效图片URL', productImage);
    imgElement.src = DEFAULT_IMG;
  }

  // 更新店铺信息
  const shopsContainer = document.getElementById('tooltip-shops');
  if (skuData.shop_info && skuData.shop_info.length > 0) {
    shopsContainer.innerHTML = skuData.shop_info.map(shop => `
      <div class="shop-item platform-${shop.platform.toLowerCase()}">
        <div class="font-medium text-xs">${shop.platform}</div>
        <div class="text-xs text-gray-600">${shop.store}</div>
      </div>
    `).join('');
  } else {
    shopsContainer.innerHTML = '<div class="text-xs text-gray-500">未上架</div>';
  }

  // 设置位置
  const rect = element.getBoundingClientRect();
  skuTooltip.style.left = (rect.right + 10) + 'px';
  skuTooltip.style.top = rect.top + 'px';

  // 确保不超出屏幕
  const tooltipRect = skuTooltip.getBoundingClientRect();
  if (tooltipRect.right > window.innerWidth) {
    skuTooltip.style.left = (rect.left - tooltipRect.width - 10) + 'px';
  }
  if (tooltipRect.bottom > window.innerHeight) {
    skuTooltip.style.top = (window.innerHeight - tooltipRect.height - 10) + 'px';
  }

  // 显示卡片
  skuTooltip.classList.add('show');
}

function hideSkuTooltip() {
  if (skuTooltip) {
    skuTooltip.classList.remove('show');
  }
}

// 加载更多SKU功能
function loadMoreSkus(dateStr, allSkus) {
  const container = document.getElementById(`sku-container-${dateStr}`);
  const currentSkus = container.querySelectorAll('.sku-block').length;
  const nextBatch = allSkus.slice(currentSkus, currentSkus + 10);

  // 添加新的SKU块
  nextBatch.forEach(skuData => {
    const skuBlock = document.createElement('div');
    const isActuallyArrived = skuData.is_actually_arrived || false;
    skuBlock.className = isActuallyArrived ? 'sku-block actually-arrived' : 'sku-block';
    const skuValue = skuData.sku || skuData.SKU;
    skuBlock.setAttribute('data-sku', skuValue);
    skuBlock.setAttribute('data-sku-info', JSON.stringify(skuData));
    skuBlock.onmouseenter = function(event) { showSkuTooltip(event, this); };
    skuBlock.onmouseleave = hideSkuTooltip;
    skuBlock.title = '点击查看详情';
    skuBlock.textContent = skuValue;

    container.appendChild(skuBlock);
  });

  // 更新或移除"加载更多"按钮
  const loadMoreBtn = container.parentElement.querySelector('.load-more-btn');
  const newCurrentCount = currentSkus + nextBatch.length;

  if (newCurrentCount >= allSkus.length) {
    // 已加载完所有SKU，移除按钮
    loadMoreBtn.parentElement.remove();
  } else {
    // 更新按钮文字
    const remaining = allSkus.length - newCurrentCount;
    loadMoreBtn.textContent = `加载更多 (还有${remaining}个)`;
    loadMoreBtn.onclick = function() { loadMoreSkus(dateStr, allSkus); };
  }
}

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
  // 绑定按钮事件
  document.getElementById('prev-month').addEventListener('click', goToPrevMonth);
  document.getElementById('next-month').addEventListener('click', goToNextMonth);

  // 绑定刷新按钮事件
  document.getElementById('refresh-data').addEventListener('click', function() {
    // 清除缓存并强制刷新
    RestockCacheManager.clearCache();
    updateCacheStatus('loading');
    loadRestockCalendar(true);
  });

  // 绑定筛选器事件
  document.getElementById('platform-filter').addEventListener('change', function() {
    const selectedPlatform = this.value;
    currentFilters.platform = selectedPlatform;

    // 更新店铺筛选器选项
    updateStoreFilterOptions(selectedPlatform);

    // 如果选择了平台，清空店铺筛选
    if (selectedPlatform) {
      currentFilters.store = '';
      document.getElementById('store-filter').value = '';
    }

    renderCurrentMonth();
  });

  document.getElementById('store-filter').addEventListener('change', function() {
    const selectedStore = this.value;
    currentFilters.store = selectedStore;

    // 如果选择了店铺，自动选择对应的平台
    if (selectedStore) {
      const correspondingPlatform = getPlatformByStore(selectedStore);
      if (correspondingPlatform && correspondingPlatform !== currentFilters.platform) {
        currentFilters.platform = correspondingPlatform;
        document.getElementById('platform-filter').value = correspondingPlatform;
        // 更新店铺筛选器选项以匹配新选择的平台
        updateStoreFilterOptions(correspondingPlatform);
        // 重新设置店铺选择
        document.getElementById('store-filter').value = selectedStore;
      }
    }

    renderCurrentMonth();
  });

  document.getElementById('listing-status-filter').addEventListener('change', function() {
    currentFilters.listingStatus = this.value;
    renderCurrentMonth();
  });

  document.getElementById('reset-filters').addEventListener('click', function() {
    currentFilters.platform = '';
    currentFilters.store = '';
    currentFilters.listingStatus = '';
    document.getElementById('platform-filter').value = '';
    document.getElementById('store-filter').value = '';
    document.getElementById('listing-status-filter').value = '';

    // 重置店铺筛选器选项为显示所有店铺
    updateStoreFilterOptions('');

    renderCurrentMonth();
  });

  // 加载数据
  loadRestockCalendar();
});
</script>
{% endblock %} 