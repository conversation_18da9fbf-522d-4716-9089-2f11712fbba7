<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KKUGUAN 登录</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            position: relative;
            overflow: hidden;
        }
        
        /* 背景装饰 */
        body::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 1px, transparent 1px);
            background-size: 50px 50px;
            animation: float 20s infinite linear;
        }
        
        @keyframes float {
            0% { transform: translate(0, 0) rotate(0deg); }
            100% { transform: translate(-50px, -50px) rotate(360deg); }
        }
        
        .login-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            box-shadow: 
                0 8px 32px rgba(0, 0, 0, 0.1),
                0 0 0 1px rgba(255, 255, 255, 0.2);
            padding: 48px 40px;
            width: 100%;
            max-width: 420px;
            text-align: center;
            position: relative;
            z-index: 1;
            transition: all 0.3s ease;
        }
        
        .login-container:hover {
            transform: translateY(-5px);
            box-shadow: 
                0 20px 40px rgba(0, 0, 0, 0.15),
                0 0 0 1px rgba(255, 255, 255, 0.3);
        }
        
        .logo-container {
            margin-bottom: 32px;
            position: relative;
        }
        
        .logo {
            width: 100px;
            height: 100px;
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            color: white;
            font-size: 48px;
            font-weight: 700;
            box-shadow: 
                0 10px 30px rgba(79, 70, 229, 0.3),
                0 0 0 4px rgba(255, 255, 255, 0.1);
            position: relative;
            overflow: hidden;
        }
        
        .logo::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.2), transparent);
            transform: rotate(45deg);
            animation: shine 3s infinite;
        }
        
        @keyframes shine {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            50% { transform: translateX(100%) translateY(100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }
        
        h1 {
            color: #1f2937;
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 8px;
            letter-spacing: -0.5px;
        }
        
        .subtitle {
            color: #6b7280;
            margin-bottom: 40px;
            font-size: 16px;
            font-weight: 500;
        }
        
        .form-group {
            margin-bottom: 24px;
            text-align: left;
            position: relative;
        }
        
        label {
            display: block;
            color: #374151;
            margin-bottom: 8px;
            font-weight: 600;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .input-container {
            position: relative;
        }
        
        input[type="text"], input[type="password"] {
            width: 100%;
            padding: 16px 20px;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: #fff;
            color: #1f2937;
            font-weight: 500;
        }
        
        input[type="text"]:focus, input[type="password"]:focus {
            outline: none;
            border-color: #4f46e5;
            box-shadow: 0 0 0 4px rgba(79, 70, 229, 0.1);
            transform: translateY(-2px);
        }
        
        input[type="text"]::placeholder, input[type="password"]::placeholder {
            color: #9ca3af;
            font-weight: 400;
        }
        
        .login-btn {
            width: 100%;
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
            border: none;
            padding: 18px;
            font-size: 16px;
            font-weight: 600;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 8px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            position: relative;
            overflow: hidden;
        }
        
        .login-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }
        
        .login-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(79, 70, 229, 0.4);
        }
        
        .login-btn:hover::before {
            left: 100%;
        }
        
        .login-btn:active {
            transform: translateY(-1px);
        }
        
        .login-btn:disabled {
            opacity: 0.7;
            cursor: not-allowed;
            transform: none;
        }
        
        .divider {
            margin: 32px 0;
            position: relative;
            text-align: center;
        }
        
        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, #e5e7eb, transparent);
        }
        
        .divider span {
            background: rgba(255, 255, 255, 0.95);
            padding: 0 20px;
            color: #6b7280;
            font-size: 14px;
            font-weight: 500;
        }
        
        .info-card {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border: 1px solid #e2e8f0;
            border-radius: 16px;
            padding: 24px;
            margin-top: 24px;
            position: relative;
            overflow: hidden;
        }
        
        .info-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: linear-gradient(135deg, #4f46e5, #7c3aed);
        }
        
        .info-card h3 {
            color: #1f2937;
            font-size: 16px;
            font-weight: 700;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .info-card h3::before {
            content: '👤';
            font-size: 18px;
        }
        
        .account-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 16px;
            background: white;
            border-radius: 8px;
            margin-bottom: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            transition: all 0.2s ease;
        }
        
        .account-item:hover {
            transform: translateX(4px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        
        .account-item:last-child {
            margin-bottom: 0;
        }
        
        .username {
            color: #4f46e5;
            font-weight: 600;
            font-size: 15px;
        }
        
        .password {
            color: #6b7280;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            background: #f3f4f6;
            padding: 4px 8px;
            border-radius: 4px;
        }
        
        .footer {
            margin-top: 32px;
            color: #9ca3af;
            font-size: 13px;
            font-weight: 500;
        }
        
        .alert {
            padding: 16px 20px;
            border-radius: 12px;
            margin-bottom: 24px;
            font-weight: 500;
            position: relative;
            animation: slideIn 0.3s ease;
        }
        
        @keyframes slideIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .alert-error {
            background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
            border: 1px solid #fca5a5;
            color: #dc2626;
        }
        
        .alert-success {
            background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
            border: 1px solid #86efac;
            color: #16a34a;
        }
        
        .alert::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: currentColor;
            border-radius: 12px 0 0 12px;
        }
        
        /* 响应式设计 */
        @media (max-width: 480px) {
            .login-container {
                padding: 32px 24px;
                margin: 16px;
            }
            
            .logo {
                width: 80px;
                height: 80px;
                font-size: 36px;
            }
            
            h1 {
                font-size: 28px;
            }
        }
        
        /* 深色模式支持 */
        @media (prefers-color-scheme: dark) {
            .login-container {
                background: rgba(31, 41, 55, 0.95);
                color: #f9fafb;
            }
            
            h1 {
                color: #f9fafb;
            }
            
            label {
                color: #d1d5db;
            }
            
            input[type="text"], input[type="password"] {
                background: #374151;
                border-color: #4b5563;
                color: #f9fafb;
            }
            
            .info-card {
                background: linear-gradient(135deg, #374151 0%, #4b5563 100%);
                border-color: #6b7280;
            }
            
            .account-item {
                background: #4b5563;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <!-- Logo和标题 -->
        <div class="logo-container">
            <div class="logo">K</div>
            <h1>KKUGUAN</h1>
            <p class="subtitle">智能库存管理系统</p>
        </div>

        <!-- 消息提示 -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert {% if category == 'error' %}alert-error{% else %}alert-success{% endif %}">
                        {{ message }}
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <!-- 登录表单 -->
        <form method="POST" id="loginForm">
            <div class="form-group">
                <label for="username">用户名</label>
                <div class="input-container">
                    <input 
                        type="text" 
                        id="username" 
                        name="username" 
                        placeholder="请输入您的用户名" 
                        required
                        value="{{ request.form.username or '' }}"
                        autocomplete="username"
                    >
                </div>
            </div>
            
            <div class="form-group">
                <label for="password">密码</label>
                <div class="input-container">
                    <input 
                        type="password" 
                        id="password" 
                        name="password" 
                        placeholder="请输入您的密码" 
                        required
                        autocomplete="current-password"
                    >
                </div>
            </div>
            
            <button type="submit" class="login-btn" id="loginBtn">
                登录系统
            </button>
        </form>

        <!-- 分割线 -->
        <div class="divider">
            <span>演示账号</span>
        </div>

        <!-- 账号信息卡片 -->
        <div class="info-card">
            <h3>可用账号</h3>
            <div class="account-item">
                <span class="username">admin</span>
                <span class="password">admin123</span>
            </div>
            <div class="account-item">
                <span class="username">kkuguan</span>
                <span class="password">123456</span>
            </div>
        </div>

        <!-- 底部版权 -->
        <div class="footer">
            © 2024 KKUGUAN 库存管理系统 • 安全可靠
        </div>
    </div>

    <script>
        // 页面加载完成后自动聚焦
        window.addEventListener('load', function() {
            const usernameInput = document.getElementById('username');
            if (usernameInput && !usernameInput.value.trim()) {
                setTimeout(() => usernameInput.focus(), 100);
            } else {
                const passwordInput = document.getElementById('password');
                if (passwordInput) {
                    setTimeout(() => passwordInput.focus(), 100);
                }
            }
        });

        // 表单提交处理
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            const btn = document.getElementById('loginBtn');
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value.trim();

            if (!username || !password) {
                e.preventDefault();
                return;
            }

            // 更新按钮状态
            btn.innerHTML = '登录中...';
            btn.disabled = true;

            // 添加加载动画
            setTimeout(() => {
                if (btn.disabled) {
                    btn.innerHTML = '正在验证...';
                }
            }, 1000);
        });

        // 快速填充账号功能
        document.querySelectorAll('.account-item').forEach(item => {
            item.addEventListener('click', function() {
                const username = this.querySelector('.username').textContent;
                const password = this.querySelector('.password').textContent;
                
                document.getElementById('username').value = username;
                document.getElementById('password').value = password;
                
                // 添加视觉反馈
                this.style.background = '#e0e7ff';
                setTimeout(() => {
                    this.style.background = 'white';
                }, 200);
                
                // 聚焦到登录按钮
                document.getElementById('loginBtn').focus();
            });
        });

        // 回车键快速提交
        document.addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                const form = document.getElementById('loginForm');
                const username = document.getElementById('username').value.trim();
                const password = document.getElementById('password').value.trim();
                
                if (username && password) {
                    form.submit();
                }
            }
        });

        // 输入框动画效果
        document.querySelectorAll('input').forEach(input => {
            input.addEventListener('focus', function() {
                this.parentElement.style.transform = 'scale(1.02)';
            });
            
            input.addEventListener('blur', function() {
                this.parentElement.style.transform = 'scale(1)';
            });
        });
    </script>
</body>
</html>
