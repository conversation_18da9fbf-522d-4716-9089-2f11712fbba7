{% extends "base.html" %}

{% block head %}
<meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
<meta http-equiv="Pragma" content="no-cache">
<meta http-equiv="Expires" content="0">
<meta name="version" content="v12.0-optimized-{{ range(1000, 9999) | random }}">

<style>
.compact-mode .sku-item {
    min-width: 90px !important;
    font-size: 0.65rem !important;
    padding: 0.2rem !important;
}
</style>

<script>
// 强制清除缓存并显示版本信息
console.log('🔄 SKU详情页面 v12.0 - 优化版已加载');
console.log('🎨 新功能: 淡紫色主题 + 虚拟滚动');
console.log('📊 高密度显示: 更多SKU，更快加载');
</script>
{% endblock %}

{% block content %}
<div class="content-wrapper">
    <!-- 上下两排，每排两个板块 -->
    <div class="flex flex-row gap-8 mb-8" style="height:400px;">
        <!-- 第一板块：供应商代码 25% -->
        <div id="supplier-info" class="bg-white rounded-2xl shadow-xl p-4 flex flex-col justify-between" style="width:25%; min-width:280px; height:100%; border-radius: 1rem !important; overflow-y: auto;">
            <div>
                <div class="flex flex-row gap-2 px-3">
                    <input id="supplier-input" list="suppliers"
                        class="border border-blue-200 rounded-xl px-2 py-1.5 text-sm flex-1"
                        style="min-width: 60px; font-size: 0.875rem; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;"
                        placeholder="供应商代码">
                    <datalist id="suppliers"></datalist>
                    <select id="chart-range"
                        class="border border-blue-200 rounded-xl px-2 py-1.5 text-sm flex-1"
                        style="min-width: 60px; font-size: 0.875rem;">
                        <option value="30">近30天</option>
                        <option value="30-60">30～60天</option>
                        <option value="60-90">60～90天</option>
                        <option value="60">近60天</option>
                        <option value="90">近90天</option>
                        <option value="all">全部时间</option>
                    </select>
                </div>
                <ul class="divide-y divide-gray-200 text-sm mt-4 px-3">
                    <li class="py-1 flex justify-between" style="font-size: 0.875rem;">
                        <span class="text-gray-500" style="white-space: nowrap; max-width: 50%;">可用总库存：</span>
                        <span id="total-available-stock" class="font-bold text-green-600" style="overflow: hidden; text-overflow: ellipsis; max-width: 50%;">-</span>
                    </li>
                    <li class="py-1 flex justify-between" style="font-size: 0.875rem;">
                        <span class="text-gray-500" style="white-space: nowrap; max-width: 50%;">公共库存：</span>
                        <span id="public-stock" class="font-bold text-blue-600" style="overflow: hidden; text-overflow: ellipsis; max-width: 50%;">-</span>
                    </li>
                    <li class="py-1 flex justify-between" style="font-size: 0.875rem;">
                        <span class="text-gray-500" style="white-space: nowrap; max-width: 50%;">SL库存：</span>
                        <span id="sl-stock" class="font-bold text-purple-600" style="overflow: hidden; text-overflow: ellipsis; max-width: 50%;">-</span>
                    </li>
                    <li class="py-1 flex justify-between" style="font-size: 0.875rem;">
                        <span class="text-gray-500" style="white-space: nowrap; max-width: 50%;">RW库存：</span>
                        <span id="rw-stock" class="font-bold text-purple-600" style="overflow: hidden; text-overflow: ellipsis; max-width: 50%;">-</span>
                    </li>
                    <li class="py-1 flex justify-between" style="font-size: 0.875rem;">
                        <span class="text-gray-500" style="white-space: nowrap; max-width: 50%;">GW库存：</span>
                        <span id="gw-stock" class="font-bold text-purple-600" style="overflow: hidden; text-overflow: ellipsis; max-width: 50%;">-</span>
                    </li>
                    <li class="py-1 flex justify-between items-start" style="font-size: 0.875rem;">
                        <span class="text-gray-500" style="white-space: nowrap; max-width: 50%;">预计到货时间：</span>
                        <span id="arrival-date" class="font-bold text-right" style="max-width: 50%; word-wrap: break-word; white-space: normal; overflow: visible; text-overflow: unset;">-</span>
                    </li>
                    <li class="py-1 flex justify-between" style="font-size: 0.875rem;">
                        <span class="text-gray-500" style="white-space: nowrap; max-width: 50%;">预计到货数量：</span>
                        <span id="arrival-qty" class="font-bold" style="overflow: hidden; text-overflow: ellipsis; max-width: 50%;">-</span>
                    </li>
                    <li class="py-1 flex justify-between" style="font-size: 0.875rem;">
                        <span class="text-gray-500" style="white-space: nowrap; max-width: 50%;">首次可用时间：</span>
                        <span id="first-available" class="font-bold" style="overflow: hidden; text-overflow: ellipsis; max-width: 50%;">-</span>
                    </li>
                    <li class="py-1 flex justify-between" style="font-size: 0.875rem;">
                        <span class="text-gray-500" style="white-space: nowrap; max-width: 50%;">预计可售天数：</span>
                        <span id="est-days" class="font-bold" style="overflow: hidden; text-overflow: ellipsis; max-width: 50%;">-</span>
                    </li>
                    <li class="py-1 flex justify-between" style="font-size: 0.875rem;">
                        <span class="text-gray-500" style="white-space: nowrap; max-width: 50%;">价格：</span>
                        <span id="price" class="font-bold text-blue-600" style="overflow: hidden; text-overflow: ellipsis; max-width: 50%;">-</span>
                    </li>

                </ul>
            </div>
        </div>
        <!-- 第二板块：SKU选择 75% -->
        <div class="bg-white rounded-2xl shadow-xl p-4 flex flex-col" style="width:75%; min-width:400px; height:100%; border-radius: 1rem !important;">
            <div class="flex-1 overflow-hidden">
                <div class="border border-gray-200 rounded-xl p-4 sku-scroll" style="height: 100%; width: 100%; overflow-y: auto; overflow-x: hidden; border-radius: 0.75rem !important;">
                    <div id="sku-list" style="display: flex; flex-wrap: wrap; gap: 8px; width: 100%; justify-content: flex-start;"></div>
                    <div id="load-more-container" class="hidden w-full text-center mt-4 pt-4 border-t border-gray-200">
                        <button id="load-more-btn" class="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors duration-200 font-medium">
                            加载更多 (<span id="remaining-count">0</span> 个剩余)
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 销量&库存变化 独占一行 -->
    <div class="chart-box p-4 mb-8" style="margin-top: 2rem; border-radius: 1rem !important;">
        <div id="sales_stock_chart" style="width:100%;height:320px;"></div>
    </div>

    <!-- 下方两块并排 -->
    <div class="flex gap-8 mb-8" style="margin-top: 2rem;">
        <!-- 销量排名图表 -->
        <div class="chart-box p-4" style="width:70%; border-radius: 1rem !important;">
            <div id="sales_rank_chart" style="width:100%;height:360px;"></div>
        </div>

        <!-- 平台销量数据 -->
        <div class="chart-box p-8" style="width:30%; border-radius: 1rem !important;">
            <div class="flex gap-4 h-full">
                <div id="amazon-sales" class="flex-1 overflow-y-auto sku-scroll"></div>
                <div id="ebay-sales" class="flex-1 overflow-y-auto sku-scroll"></div>
                <div id="walmart-sales" class="flex-1 overflow-y-auto sku-scroll"></div>
            </div>
        </div>
    </div>
</div>

<!-- 自定义滚动条样式 -->
<style>
/* 强制确保圆角样式生效 */
.bg-white.rounded-2xl, .chart-box {
    border-radius: 1rem !important;
}

.rounded-xl {
    border-radius: 0.75rem !important;
}

/* 确保间距正常 */
.gap-8 {
    gap: 2rem !important;
}

.mb-8 {
    margin-bottom: 2rem !important;
}

/* 防止文字溢出，确保左上角板块文字不超出容器 */
#supplier-info ul li {
    overflow: hidden;
    font-size: 0.875rem !important;
}

#supplier-info ul li span {
    max-width: 50%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: inline-block;
}

/* 预计到货时间特殊样式 - 最多显示两行 */
#arrival-date {
    max-width: 50% !important;
    word-wrap: break-word !important;
    white-space: normal !important;
    overflow: hidden !important;
    display: -webkit-box !important;
    -webkit-line-clamp: 2 !important;
    -webkit-box-orient: vertical !important;
    line-height: 1.2 !important;
    max-height: 2.4em !important;
}

#supplier-input, #chart-range {
    font-size: 0.875rem !important;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* 只禁用滚动条宽度调整，保留滑动功能 */
#sales_rank_chart .echarts-data-zoom-handle {
    display: none !important;  /* 隐藏调整宽度的手柄 */
}

#sales_rank_chart .echarts-data-zoom-move-handle {
    display: block !important;  /* 显示移动手柄，允许滑动 */
    cursor: grab !important;
}

#sales_rank_chart .echarts-data-zoom-move-handle:active {
    cursor: grabbing !important;
}

.sku-scroll {
    scrollbar-width: thin;
    scrollbar-color: #cbd5e1 #f1f5f9;
}
.sku-scroll::-webkit-scrollbar {
    width: 6px;
}
.sku-scroll::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
}
.sku-scroll::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
}
.sku-scroll::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}
.sku-selected {
  border: 2px solid #22c55e !important;
  background: #dcfce7 !important;
  box-shadow: 0 0 0 2px rgba(34, 197, 94, 0.2) !important;
}

/* 图片状态样式 */
.error-placeholder {
  opacity: 0.6;
  filter: grayscale(100%);
}

.loaded {
  opacity: 1;
  filter: none;
  transition: opacity 0.3s ease;
}

/* 图片加载动画 */
img[data-optimize="true"]:not(.loaded):not(.error-placeholder) {
  opacity: 0.7;
  animation: loading 1.5s ease-in-out infinite;
}

@keyframes loading {
  0%, 100% { opacity: 0.7; }
  50% { opacity: 1; }
}
</style>

<!-- ECharts CDN -->
<script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
<script>
console.log('=== JavaScript开始执行 ===', new Date().toISOString());

// 版本：v11.4 - 云服务器优化版 + 重试机制
// 新增功能：API重试机制、更好的错误处理、缓存优化

// API重试工具函数
async function fetchWithRetry(url, options = {}, maxRetries = 3, delay = 1000) {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
            console.log(`🔄 API请求尝试 ${attempt}/${maxRetries}: ${url}`);
            const response = await fetch(url, options);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();
            console.log(`✅ API请求成功 (尝试 ${attempt}): ${url}`);
            return data;

        } catch (error) {
            console.warn(`⚠️ API请求失败 (尝试 ${attempt}/${maxRetries}): ${error.message}`);

            if (attempt === maxRetries) {
                console.error(`❌ API请求最终失败: ${url}`, error);
                throw error;
            }

            // 指数退避延迟
            const waitTime = delay * Math.pow(2, attempt - 1);
            console.log(`⏳ ${waitTime}ms后重试...`);
            await new Promise(resolve => setTimeout(resolve, waitTime));
        }
    }
}

// 版本：v11.3 - 原始图片加载 + SKU分页显示 (20排显示版)
        // SKU详情页面脚本加载 v11.3 - 20排显示版本
        // 特性：默认显示20排SKU(120个)，支持"加载更多"功能，修复搜索特定SKU时的选中问题
let skuList = [];
let orderedSuppliers = [];
let currentSKU = '';
let currentSupplier = '';

// 分页相关变量
let currentSupplierSKUs = [];
let currentDisplayedCount = 0;
const INITIAL_DISPLAY_COUNT = 120; // 默认显示 20 排 × 6 个 (更明显的分页效果)
const LOAD_MORE_COUNT = 120; // 每次点击"加载更多"增加的数量

// 简化的图片统计
let imageStats = { total: 0, success: 0, failed: 0 };

// 更好的默认占位符图片
const DEFAULT_IMG = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjYwIiBoZWlnaHQ9IjYwIiBmaWxsPSIjRjNGNEY2IiBzdHJva2U9IiNEMUQ1REIiIHN0cm9rZS13aWR0aD0iMSIvPgo8Y2lyY2xlIGN4PSIzMCIgY3k9IjI1IiByPSI4IiBmaWxsPSIjOUNBM0FGIi8+CjxwYXRoIGQ9Ik0xNSA0NUwyNSAzNUwzNSAzOUw0NSA0NVoiIGZpbGw9IiM5Q0EzQUYiLz4KPC9zdmc+';

// 辅助函数：获取SKU
function getSKU(item) {
    return item.sku || item.SKU || '';
}

// 辅助函数：获取供应商代码
function getSupplierCode(item) {
    return item.supplier_code || item.供应商代码 || '';
}

// 辅助函数：获取产品图片
function getProductImage(item) {
    return item.product_image || item.产品图片 || '';
}

// 辅助函数：安全获取数据
function safeGet(obj, key, defaultValue = '') {
    return obj && obj[key] !== undefined && obj[key] !== null ? obj[key] : defaultValue;
}

// 辅助函数：格式化日期
function formatDate(dateStr) {
    if (!dateStr || dateStr === '-' || dateStr === 'null' || dateStr === null) {
        return '-';
    }
    try {
        const date = new Date(dateStr);
        if (isNaN(date.getTime())) {
            return dateStr; // 如果不是有效日期，返回原字符串
        }
        return date.toLocaleDateString('zh-CN');
    } catch (e) {
        return dateStr;
    }
}





 

// 初始化SKU列表和图片
async function loadSKUList() {
  console.log('开始加载SKU列表...');
  try {
    // 使用重试机制请求SKU列表API
    const data = await fetchWithRetry('/api/sku_list', {}, 3, 1000);
      console.log('SKU列表API响应数据:', data);
      // 支持新的英文字段名和旧的中文字段名
      const skuListData = data.sku_list || data.SKU列表;
      console.log('提取的SKU列表数据:', skuListData);
      if (!skuListData || !Array.isArray(skuListData)) {
        console.error('SKU数据格式错误:', data);
        return;
      }
      skuList = skuListData.filter(x=>(x.sku || x.SKU) && (x.sku || x.SKU)!=='' && (x.sku || x.SKU)!=='NaN' && (x.sku || x.SKU)!=='nan' && (x.sku || x.SKU)!==null && (x.sku || x.SKU)!==undefined);
      console.log('过滤后的SKU列表长度:', skuList.length);
      orderedSuppliers = data.ordered_suppliers || data.已下单供应商 || [];
      
                      // 加载了有效SKU数据
      
      // 检查SKU列表
      if (skuList.length === 0) {
        console.error('SKU列表为空！');
        return;
      }
      
      // 初始化供应商下拉列表（只显示供应商代码）
      const datalist = document.getElementById('suppliers');
      datalist.innerHTML = '';
      let supplierSet = new Set();
      skuList.forEach(item => {
        const supplierCode = getSupplierCode(item);
        if (supplierCode && !supplierSet.has(supplierCode)) {
          const opt = document.createElement('option');
          opt.value = supplierCode;
          // 出过单的供应商用绿色字体
          if (orderedSuppliers.includes(supplierCode)) {
            opt.style.color = '#22c55e';
            opt.style.fontWeight = 'bold';
          }
          datalist.appendChild(opt);
          supplierSet.add(supplierCode);
        }
      });
    

    


    // 恢复之前选择的供应商状态，或选择第一个有数据的供应商
    console.log('可用供应商列表:', Array.from(supplierSet));
    const savedSupplier = sessionStorage.getItem('selectedSupplier');
    console.log('保存的供应商:', savedSupplier);

    // 检查URL参数，如果有SKU参数，优先使用
    const urlSKU = getURLParameter('sku');
    if (urlSKU) {
      console.log('处理URL中的SKU: ' + urlSKU);
      const skuInfo = skuList.find(item => getSKU(item) === urlSKU);
      if (skuInfo) {
        const supplierCode = getSupplierCode(skuInfo);
        console.log('找到SKU对应的供应商: ' + supplierCode);
        // 延迟执行，确保所有函数都已定义
        setTimeout(() => {
          selectSupplier(supplierCode);
          // 再次延迟选择SKU，确保供应商选择完成
          setTimeout(() => selectSKU(urlSKU), 500);
        }, 100);
        return;
      } else {
        console.log('URL中的SKU不存在: ' + urlSKU);
      }
    }

    if (savedSupplier && supplierSet.has(savedSupplier)) {
      console.log('选择保存的供应商:', savedSupplier);
      selectSupplier(savedSupplier);
    } else if (supplierSet.size > 0) {
      // 如果没有保存的供应商，选择第一个供应商
      const firstSupplier = Array.from(supplierSet)[0];
      console.log('选择第一个供应商:', firstSupplier);
      selectSupplier(firstSupplier);
    } else {
      console.log('没有可用的供应商');
    }
  } catch (err) {
    console.error('SKU数据加载失败:', err);
    // 显示用户友好的错误信息
    alert('加载SKU数据失败，请刷新页面重试。如果问题持续存在，请联系管理员。');
  }
}

// 选择供应商，显示该供应商的所有SKU
function selectSupplier(supplierCode) {
              // 选择供应商: ${supplierCode}
            
            // 切换供应商
  
  // 重置图片统计
  imageStats = { total: 0, success: 0, failed: 0, retried: 0, compressed: 0 };
  
  currentSupplier = supplierCode;
  const supplierInput = document.getElementById('supplier-input');
  supplierInput.value = supplierCode;
  
  // 保存到sessionStorage，防止页面刷新时丢失
  sessionStorage.setItem('selectedSupplier', supplierCode);
  
  // 出过单的供应商用绿色字体
  if (orderedSuppliers.includes(supplierCode)) {
    supplierInput.style.color = '#22c55e';
    supplierInput.style.fontWeight = 'bold';
  } else {
    supplierInput.style.color = '#000';
    supplierInput.style.fontWeight = 'normal';
  }
  
  // 过滤该供应商的SKU
  const supplierSKUs = skuList.filter(item => getSupplierCode(item) === supplierCode);
  
  // 按全部历史销量排序：销量越高排名越靠前
  supplierSKUs.sort((a, b) => {
    const aTotalSales = a.total_historical_sales || 0;
    const bTotalSales = b.total_historical_sales || 0;
    
    // 按全部历史销量降序排列
    if (bTotalSales !== aTotalSales) {
      return bTotalSales - aTotalSales;
    }
    
    // 如果销量相同，按SKU字母顺序排列
    const skuA = getSKU(a) || '';
    const skuB = getSKU(b) || '';
    return skuA.localeCompare(skuB);
  });

  // 保存当前供应商的 SKU 列表
  currentSupplierSKUs = supplierSKUs;
  currentDisplayedCount = 0;
  
  // 清空 SKU 列表
  const skuListDiv = document.getElementById('sku-list');
  if (!skuListDiv) {
    console.error('找不到sku-list元素!');
    return;
  }
  skuListDiv.innerHTML = '';
  
  console.log(`🖼️ 开始渲染${supplierSKUs.length}个SKU图片（分页版本）`);
  console.log(`📊 初始显示数量: ${INITIAL_DISPLAY_COUNT}, 总SKU数: ${supplierSKUs.length}`);
  
              // 首次加载，显示前120个SKU
  loadMoreSKUs(INITIAL_DISPLAY_COUNT);
  
  // 默认选择第一个SKU
  if (supplierSKUs.length > 0) {
    const firstSKU = getSKU(supplierSKUs[0]);
    console.log('第一个SKU对象:', supplierSKUs[0]);
    console.log('getSKU返回值:', firstSKU);
    selectSKU(firstSKU);
  } else {
    console.log('没有找到供应商的SKU');
  }
}

// 加载更多SKU
function loadMoreSKUs(count = LOAD_MORE_COUNT) {
  console.log(`🔄 loadMoreSKUs 调用 - count: ${count}, 当前已显示: ${currentDisplayedCount}, 总数: ${currentSupplierSKUs.length}`);
  
  const skuListDiv = document.getElementById('sku-list');
  if (!skuListDiv) {
    console.error('找不到sku-list元素!');
    return;
  }
  
  const loadMoreContainer = document.getElementById('load-more-container');
  const remainingCountSpan = document.getElementById('remaining-count');
  
  // 计算要显示的SKU范围
  const startIndex = currentDisplayedCount;
  const endIndex = Math.min(currentDisplayedCount + count, currentSupplierSKUs.length);
  
  console.log(`📍 渲染范围: ${startIndex} -> ${endIndex}`);
  
  // 渲染新的SKU项目
  for (let i = startIndex; i < endIndex; i++) {
    const item = currentSupplierSKUs[i];
    
    const div = document.createElement('div');
    div.className = 'cursor-pointer border border-gray-200 rounded p-1 hover:border-blue-300 hover:bg-blue-50 transition-all duration-200';
    div.style.cssText = 'display: flex; flex-direction: column; align-items: center; justify-content: center; height: 100px; width: 120px; flex-shrink: 0;';
    const itemSKU = getSKU(item);
    div.onclick = () => selectSKU(itemSKU);
    div.setAttribute('data-sku', itemSKU);
    
    // 创建图片元素
    const img = document.createElement('img');
    img.className = 'rounded object-cover';
    img.style.cssText = 'width: 60px; height: 60px; margin-bottom: 6px;';
    
    // 简单的图片加载逻辑
    const productImage = getProductImage(item);
    if (productImage && typeof productImage === 'string' && productImage.startsWith('http')) {
      imageStats.total++;

      // 简单的错误处理
      img.onerror = function() {
        console.warn('图片加载失败:', itemSKU);
        imageStats.failed++;
        this.src = DEFAULT_IMG;
      };

      // 成功加载回调
      img.onload = function() {
        imageStats.success++;
      };

      // 直接使用原始图片URL
      img.src = productImage;
    } else {
      img.src = DEFAULT_IMG;
    }
    
    // 创建文字元素
    const text = document.createElement('div');
    text.textContent = itemSKU;
    text.className = 'text-center truncate';
    // 有历史销量的SKU用绿色字体
    const hasHistoricalSales = (item.total_historical_sales || 0) > 0;
    const textColor = hasHistoricalSales ? '#22c55e' : '#666';
    const fontWeight = hasHistoricalSales ? 'bold' : 'normal';
    text.style.cssText = `font-size: 12px; color: ${textColor}; font-weight: ${fontWeight}; max-width: 110px; line-height: 1.2; text-align: center;`;
    
    div.appendChild(img);
    div.appendChild(text);
    skuListDiv.appendChild(div);
  }
  
  // 更新显示计数
  currentDisplayedCount = endIndex;
  
  // 更新"加载更多"按钮的状态
  const remainingCount = currentSupplierSKUs.length - currentDisplayedCount;
  
  if (remainingCount > 0) {
    loadMoreContainer.classList.remove('hidden');
    remainingCountSpan.textContent = remainingCount;
  } else {
    loadMoreContainer.classList.add('hidden');
  }
  
  console.log(`✅ 已渲染 ${currentDisplayedCount}/${currentSupplierSKUs.length} 个SKU，剩余 ${remainingCount} 个`);
}

// 选择SKU，刷新所有数据
function selectSKU(sku) {
  console.log(`selectSKU被调用，参数: ${sku}`);

  if (!sku || sku === 'undefined' || sku === undefined) {
    console.log('selectSKU: SKU为空或undefined，跳过');
    return;
  }

  console.log(`选择SKU: ${sku}`);
  currentSKU = sku;
  
  // 检查SKU是否已经渲染
  let selectedDiv = document.querySelector(`#sku-list > div[data-sku="${sku}"]`);
  
  // 如果SKU还没有渲染，需要加载更多直到找到该SKU
  if (!selectedDiv && currentSupplierSKUs.length > 0) {
    const skuIndex = currentSupplierSKUs.findIndex(item => getSKU(item) === sku);
    if (skuIndex !== -1 && skuIndex >= currentDisplayedCount) {
      console.log(`🎯 SKU ${sku} 在索引 ${skuIndex}，需要加载更多 SKU`);
      // 计算需要加载多少个SKU才能显示目标SKU
      const needToLoad = skuIndex - currentDisplayedCount + 1;
      loadMoreSKUs(needToLoad);
      selectedDiv = document.querySelector(`#sku-list > div[data-sku="${sku}"]`);
    }
  }
  
  // 更新选中状态的视觉反馈
  document.querySelectorAll('#sku-list > div').forEach(div => {
    div.classList.remove('sku-selected');
  });
  
  // 高亮当前选中的SKU
  if (selectedDiv) {
    selectedDiv.classList.add('sku-selected');
    
    // 确保选中的SKU在视口内
    selectedDiv.scrollIntoView({
      behavior: 'smooth',
      block: 'nearest',
      inline: 'nearest'
    });
    
    // 加载SKU详情
    loadSKUDetail(sku);
    // 加载销量和库存数据
    loadSalesStock(sku);
    // 加载销量排名
    loadSalesRank();
    // 加载平台销量数据
    loadPlatformSales(sku);
  } else {
    console.warn(`⚠️ 找不到SKU: ${sku}`);
    // 即使找不到SKU元素，也要加载详情数据
    loadSKUDetail(sku);
    loadSalesStock(sku);
    loadSalesRank();
    loadPlatformSales(sku);
  }
}



// 获取URL参数
function getURLParameter(name) {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get(name);
}



// ==================== SKU虚拟滚动优化 ====================
class SKUVirtualScroll {
    constructor() {
        this.container = document.getElementById('sku-list');
        this.searchInput = document.getElementById('sku-search');
        this.compactToggle = document.getElementById('compact-toggle');
        this.skuCount = document.getElementById('sku-count');

        this.allSkus = [];
        this.filteredSkus = [];
        this.visibleSkus = [];
        this.itemsPerPage = 150; // 增加显示数量
        this.currentPage = 0;
        this.isCompact = false;

        this.initEventListeners();
    }

    initEventListeners() {
        // 搜索功能
        this.searchInput.addEventListener('input', (e) => {
            this.filterSkus(e.target.value);
        });

        // 紧凑模式切换
        this.compactToggle.addEventListener('click', () => {
            this.toggleCompactMode();
        });
    }

    setSkus(skus) {
        this.allSkus = skus;
        this.filteredSkus = [...skus];
        this.updateSkuCount();
        this.renderSkus();
    }

    filterSkus(searchTerm) {
        if (!searchTerm.trim()) {
            this.filteredSkus = [...this.allSkus];
        } else {
            this.filteredSkus = this.allSkus.filter(sku =>
                sku.toLowerCase().includes(searchTerm.toLowerCase())
            );
        }
        this.currentPage = 0;
        this.updateSkuCount();
        this.renderSkus();
    }

    toggleCompactMode() {
        this.isCompact = !this.isCompact;
        const wrapper = document.querySelector('.content-wrapper');

        if (this.isCompact) {
            wrapper.classList.add('compact-mode');
            this.compactToggle.innerHTML = '<i class="fas fa-expand-alt"></i> 标准模式';
            this.itemsPerPage = 200; // 紧凑模式显示更多
        } else {
            wrapper.classList.remove('compact-mode');
            this.compactToggle.innerHTML = '<i class="fas fa-compress-alt"></i> 紧凑模式';
            this.itemsPerPage = 150;
        }

        this.renderSkus();
        UI.showToast(`已切换到${this.isCompact ? '紧凑' : '标准'}模式`, 'success', 2000);
    }

    updateSkuCount() {
        this.skuCount.textContent = `${this.filteredSkus.length} 个SKU`;
    }

    renderSkus() {
        const startIndex = 0;
        const endIndex = Math.min(this.itemsPerPage * (this.currentPage + 1), this.filteredSkus.length);
        this.visibleSkus = this.filteredSkus.slice(startIndex, endIndex);

        // 使用骨架屏
        UI.showSkeleton(this.container, 'sku-item', 20);

        // 模拟异步渲染
        setTimeout(() => {
            this.container.innerHTML = this.visibleSkus.map(sku => this.createSkuElement(sku)).join('');
            this.updateLoadMoreButton();
        }, 100);
    }

    createSkuElement(sku) {
        // 这里需要根据实际的SKU数据结构调整
        const hasOrders = Math.random() > 0.7; // 模拟有订单数据
        const hasShopSku = Math.random() > 0.8; // 模拟有店铺SKU

        let className = 'sku-item';
        if (hasOrders && hasShopSku) {
            className += ' has-orders';
        } else if (hasShopSku) {
            className += ' has-shop-sku';
        } else {
            className += ' no-data';
        }

        return `
            <div class="${className}" onclick="selectSKU('${sku}')" title="${sku}">
                ${sku}
                ${hasOrders && Math.random() > 0.9 ? '<div class="alert-badge">!</div>' : ''}
            </div>
        `;
    }

    updateLoadMoreButton() {
        const loadMoreContainer = document.getElementById('load-more-container');
        const loadMoreBtn = document.getElementById('load-more-btn');
        const remainingCount = document.getElementById('remaining-count');

        const remaining = this.filteredSkus.length - this.visibleSkus.length;

        if (remaining > 0) {
            loadMoreContainer.classList.remove('hidden');
            remainingCount.textContent = remaining;

            loadMoreBtn.onclick = () => {
                this.currentPage++;
                this.renderSkus();
            };
        } else {
            loadMoreContainer.classList.add('hidden');
        }
    }
}

// 全局实例
let skuVirtualScroll;

// 输入框处理逻辑
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM加载完成，开始初始化');
    console.log('当前页面URL: ' + window.location.href);

    // 检查URL参数，如果有SKU参数，预设供应商代码
    const urlSKU = getURLParameter('sku');
    if (urlSKU) {
        console.log('检测到URL中的SKU: ' + urlSKU);
        // 根据SKU推断供应商代码（简单规则：取SKU的前几位字符）
        let supplierCode = '';
        if (urlSKU.startsWith('W1422')) {
            supplierCode = 'W1422';
        } else if (urlSKU.startsWith('W1715')) {
            supplierCode = 'W1715';
        } else if (urlSKU.startsWith('W1552')) {
            supplierCode = 'W1552';
        } else if (urlSKU.startsWith('S028')) {
            supplierCode = 'S028';
        } else if (urlSKU.startsWith('W637')) {
            supplierCode = 'W637';
        } else {
            // 通用规则：取前4位字符
            supplierCode = urlSKU.substring(0, 4);
        }

        if (supplierCode) {
            console.log('推断的供应商代码: ' + supplierCode);
            // 设置供应商输入框的值
            const supplierInput = document.getElementById('supplier-input');
            if (supplierInput) {
                supplierInput.value = supplierCode;
                console.log('已设置供应商输入框: ' + supplierCode);
            }
        }
    }

    console.log('开始加载SKU列表');
    loadSKUList();
    
    // 监听供应商输入框变化
    const supplierInput = document.getElementById('supplier-input');
    supplierInput.addEventListener('input', function() {
        const value = this.value.trim().toUpperCase();
        const datalist = document.getElementById('suppliers');
        datalist.innerHTML = '';
        let matchedSuppliers = [];
        if (!value) {
            matchedSuppliers = skuList.map(item => getSupplierCode(item));
        } else {
            matchedSuppliers = skuList
                .filter(item => {
                    const supplierCode = getSupplierCode(item);
                    const sku = getSKU(item);
                    return (supplierCode && supplierCode.toUpperCase().includes(value)) ||
                           (sku && sku.toUpperCase().includes(value));
                })
                .map(item => getSupplierCode(item));
        }
        // 去重并渲染
        const uniqueSuppliers = Array.from(new Set(matchedSuppliers));
        uniqueSuppliers.forEach(code => {
            const opt = document.createElement('option');
            opt.value = code;
            datalist.appendChild(opt);
        });
        // 1. 输入内容完全等于某个SKU时，自动选中该SKU对应的供应商并联动SKU详情
        const matchedSKU = skuList.find(item => getSKU(item).toUpperCase() === value);
        if (matchedSKU) {
            selectSupplier(getSupplierCode(matchedSKU));
            setTimeout(() => selectSKU(getSKU(matchedSKU)), 100);
            return;
        }
        // 2. 输入内容完全等于某个供应商代码时，自动selectSupplier
        const matchedSupplier = uniqueSuppliers.find(code => code.toUpperCase() === value);
        if (matchedSupplier) {
            selectSupplier(matchedSupplier);
        }
    });
    
    // 新增：监听回车实现SKU模糊搜索（优先供应商代码）
    supplierInput.addEventListener('keydown', function(e) {
        if (e.key === 'Enter') {
            const value = this.value.trim().toUpperCase();
            // 1. 优先模糊匹配供应商代码
            let matchedSupplier = skuList.find(item => {
                const supplierCode = getSupplierCode(item);
                return supplierCode && supplierCode.toUpperCase().includes(value);
            });
            if (matchedSupplier) {
                selectSupplier(getSupplierCode(matchedSupplier));
                return;
            }
            // 2. 再精确匹配SKU
            let matchedSKU = skuList.find(item => getSKU(item).toUpperCase() === value);
            if (!matchedSKU) {
                // 3. 最后模糊匹配SKU
                matchedSKU = skuList.find(item => {
                    const sku = getSKU(item);
                    return sku && sku.toUpperCase().includes(value);
                });
            }
            if (matchedSKU) {
                selectSupplier(getSupplierCode(matchedSKU));
                setTimeout(() => selectSKU(getSKU(matchedSKU)), 100);
            } else {
                // 没有匹配到，记录日志
                console.log('未找到包含该内容的供应商或SKU');
            }
        }
    });
    
    // 监听时间区间变化
    document.getElementById('chart-range').addEventListener('change', function() {
        const timeRange = this.value;
        if (currentSKU) {
            const dateRange = getDateRange();
            // 重新加载所有数据
            loadSKUDetail(currentSKU);
            loadSalesStock(currentSKU);
            loadSalesRank();
            loadPlatformSales(currentSKU);
        }
    });
    
    // 监听"加载更多"按钮点击
    document.getElementById('load-more-btn').addEventListener('click', function() {
        loadMoreSKUs();
    });

});

// 加载平台销量数据
function loadPlatformSales(sku) {
    const dateRange = getDateRange();
    
    const url = new URL('/api/platform_sales', window.location.origin);
    if (sku) url.searchParams.append('sku', encodeURIComponent(sku));
    url.searchParams.append('start_date', dateRange.start);
    url.searchParams.append('end_date', dateRange.end);
    
    fetch(url).then(r=>r.json()).then(data => {
        // 处理Amazon数据
        const amazonDiv = document.getElementById('amazon-sales');
        renderPlatformData(amazonDiv, 'Amazon', data.Amazon);

        // 处理eBay数据
        const ebayDiv = document.getElementById('ebay-sales');
        renderPlatformData(ebayDiv, 'eBay', data.eBay);

        // 处理Walmart数据
        const walmartDiv = document.getElementById('walmart-sales');
        renderPlatformData(walmartDiv, 'Walmart', data.Walmart);
    });
}

// 渲染单个平台的销量数据
function renderPlatformData(container, platform, data) {
    if (!data) {
        container.innerHTML = `
            <div class="text-base font-bold mb-2">${platform} (0)</div>
            <div class="text-gray-500 text-sm">暂无数据</div>
        `;
        return;
    }

    let html = `
        <div class="text-base font-bold mb-2">${platform} (${data.total_sales || 0})</div>
    `;

    if (data.shops && data.shops.length > 0) {
        // 按销量降序排序
        const sortedShops = data.shops.slice().sort((a, b) => b.sales - a.sales);
        html += sortedShops.map(shop => `
            <div class="flex justify-between items-center py-1 border-b border-gray-100">
                <span class="text-sm text-gray-600 truncate" style="max-width: 70%;">${shop.name}</span>
                <span class="text-sm font-semibold text-blue-600">${shop.sales}</span>
            </div>
        `).join('');
    } else {
        html += '<div class="text-gray-500 text-sm">暂无店铺数据</div>';
    }

    container.innerHTML = html;
}

// 加载SKU详情
async function loadSKUDetail(sku) {
    console.log(`加载SKU详情: ${sku}`);
    const timeRange = document.getElementById('chart-range').value;
    let rangeText;

    // 将select的值转换为后端需要的格式
    switch(timeRange) {
        case '30':
            rangeText = '近30天';
            break;
        case '30-60':
            rangeText = '30~60天';
            break;
        case '60-90':
            rangeText = '60~90天';
            break;
        case '60':
            rangeText = '近60天';
            break;
        case '90':
            rangeText = '近90天';
            break;
        case 'all':
            rangeText = '全部时间';
            break;
        default:
            rangeText = '近30天';
    }

    const dateRange = getDateRange();

    try {
        const data = await fetchWithRetry(
            `/api/sku_detail?sku=${encodeURIComponent(sku)}&time_range=${encodeURIComponent(rangeText)}&start_date=${dateRange.start}&end_date=${dateRange.end}`,
            {}, 3, 1000
        );
        if (data.error) {
            throw new Error(data.error);
        }
        // 使用safeGet函数支持中英文字段名，更新各种库存显示
        document.getElementById('total-available-stock').innerText = safeGet(data, 'total_available_stock', safeGet(data, '可用总库存', '-'));
        document.getElementById('public-stock').innerText = safeGet(data, 'public_stock', safeGet(data, '公共库存', '-'));
        document.getElementById('sl-stock').innerText = safeGet(data, 'sl_stock', safeGet(data, 'SL库存', '-'));
        document.getElementById('rw-stock').innerText = safeGet(data, 'rw_stock', safeGet(data, 'RW库存', '-'));
        document.getElementById('gw-stock').innerText = safeGet(data, 'gw_stock', safeGet(data, 'GW库存', '-'));
        document.getElementById('arrival-date').innerText = formatDate(safeGet(data, 'estimated_restock_date', safeGet(data, '预计补货时间', '-')));
        document.getElementById('arrival-qty').innerText = safeGet(data, 'estimated_restock_qty', safeGet(data, 'estimated_restock_quantity', safeGet(data, '预计补货数量', '-')));
        document.getElementById('first-available').innerText = formatDate(safeGet(data, 'first_stock_date', safeGet(data, '首次到库时间', '-')));
        document.getElementById('est-days').innerText = safeGet(data, 'estimated_days', safeGet(data, '预计可售天数', '-'));

        // 显示价格
        const price = safeGet(data, 'price', '-');
        document.getElementById('price').innerText = price !== '-' && price !== null && price !== '' ? `$${price}` : '-';
    } catch (err) {
        console.error('加载SKU详情失败:', err);
        // 显示默认值，避免页面显示空白
        document.getElementById('total-available-stock').innerText = '-';
        document.getElementById('public-stock').innerText = '-';
        document.getElementById('sl-stock').innerText = '-';
        document.getElementById('rw-stock').innerText = '-';
        document.getElementById('gw-stock').innerText = '-';
        document.getElementById('arrival-date').innerText = '-';
        document.getElementById('arrival-qty').innerText = '-';
        document.getElementById('first-available').innerText = '-';
        document.getElementById('est-days').innerText = '-';
        document.getElementById('price').innerText = '-';
    }
}

// 获取日期范围
function getDateRange() {
    const today = new Date();
    const timeRange = document.getElementById('chart-range').value;
    let startDate, endDate;

    // 保证 endDate 是今天
    endDate = new Date(today);

    switch(timeRange) {
        case '30':
            startDate = new Date(today);
            startDate.setDate(today.getDate() - 30);
            break;
        case '30-60':
            endDate.setDate(today.getDate() - 30);
            startDate = new Date(today);
            startDate.setDate(today.getDate() - 60);
            break;
        case '60-90':
            endDate.setDate(today.getDate() - 60);
            startDate = new Date(today);
            startDate.setDate(today.getDate() - 90);
            break;
        case '60':
            startDate = new Date(today);
            startDate.setDate(today.getDate() - 60);
            break;
        case '90':
            startDate = new Date(today);
            startDate.setDate(today.getDate() - 90);
            break;
        case 'all':
            // 全部时间：从很早的时间开始
            startDate = new Date('2020-01-01');
            break;
        default:
            startDate = new Date(today);
            startDate.setDate(today.getDate() - 30);
    }

    return {
        start: startDate.toISOString().split('T')[0],
        end: endDate.toISOString().split('T')[0]
    };
}

// 加载销量和库存数据
let salesStockChart = null;
let salesRankChart = null;

// 获取未来30天的日期数组
function getNext30Days() {
    const dates = [];
    const today = new Date();
    for (let i = 0; i < 30; i++) {
        const date = new Date(today);
        date.setDate(today.getDate() + i);
        dates.push(date.toISOString().split('T')[0]);
    }
    return dates;
}

function loadSalesStock(sku) {
    const dateRange = getDateRange();
    
    // 确保图表容器存在
    const chartContainer = document.getElementById('sales_stock_chart');
    if (!chartContainer) {
        console.error('找不到图表容器');
        return;
    }
    
    const url = new URL('/api/sales_stock', window.location.origin);
    url.searchParams.append('sku', sku);
    url.searchParams.append('start_date', dateRange.start);
    url.searchParams.append('end_date', dateRange.end);
    
    // 初始化或重置图表
    if (!salesStockChart) {
        salesStockChart = echarts.init(document.getElementById('sales_stock_chart'));
    } else {
        salesStockChart.clear();  // 清除之前的图表数据
    }
    
    // 显示加载状态
    salesStockChart.showLoading();

        // 基础配置
    const baseOption = {
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            }
        },
        grid: {
            top: '15%',
            bottom: '10%',
            left: '3%',
            right: '3%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            data: getNext30Days(),  // 默认显示未来30天
            axisLabel: {
                interval: 'auto',
                rotate: 45
            }
        },
        yAxis: [
            {
                type: 'value',
                name: '销量',
                position: 'left',
                alignTicks: true,
                min: 0,
                max: function(value) {
                    return value.max > 6 ? value.max : 6;
                },
                interval: 1,
                axisLine: {
                    show: true,
                    lineStyle: {
                        color: '#2563eb'
                    }
                },
                axisLabel: {
                    formatter: function(value) {
                        return Math.floor(value);
                    }
                }
            },
            {
                type: 'value',
                name: '库存',
                position: 'right',
                alignTicks: true,
                min: 0,
                interval: function(value) {
                    return Math.ceil(value.max / 6);
                },
                splitNumber: 6,
                axisLine: {
                    show: true,
                    lineStyle: {
                        color: '#22c55e'
                    }
                },
                axisLabel: {
                    formatter: function(value) {
                        return Math.floor(value);
                    }
                }
            }
        ]
    };

    // 先设置空的图表框架
    salesStockChart.setOption({
        ...baseOption,
        series: []
    });

    // 获取数据并更新图表 - 使用重试机制
    fetchWithRetry(url.toString(), {}, 3, 1000)
        .then(data => {
            console.log('销量库存数据:', data);
            console.log('公共库存数据:', data.public_stock);
            console.log('自己库存数据:', data.own_stock);
            console.log('可用总库存数据:', data.total_stock);
            console.log('实际到货时间:', data.actual_arrival_date);
            console.log('所有实际到货时间:', data.actual_arrival_dates);

            // 确保所有库存数据都存在，如果不存在则用空数组填充
            if (!data.public_stock) data.public_stock = data.stock || [];
            if (!data.own_stock) data.own_stock = [];
            if (!data.total_stock) data.total_stock = [];

            // 检查是否有数据结构（即使数据都是0也要显示图表）
            const hasDataStructure = data &&
                                    data.sales &&
                                    data.stock &&
                                    Array.isArray(data.sales) &&
                                    Array.isArray(data.stock) &&
                                    data.sales.length > 0;

            console.log('是否有数据结构:', hasDataStructure);

            if (!hasDataStructure) {
                console.log('没有数据结构，显示空图表');
                // 显示空图表
                salesStockChart.setOption({
                    ...baseOption,
                    series: []
                });
                return;
            }

            // 有数据时更新图表
            const totalSales = data.sales.reduce((sum, val) => sum + (val || 0), 0);

            // 构建图表配置
            const chartOption = {
                ...baseOption,
                xAxis: {
                    ...baseOption.xAxis,
                    data: data.dates || getNext30Days()
                },
                legend: {
                    data: ['销量', '公共库存', '自己库存', '可用总库存'],
                    top: 10,
                    textStyle: {
                        fontSize: 12,
                        color: '#666'
                    },
                    selected: {
                        '销量': true,           // 销量默认显示
                        '公共库存': false,      // 公共库存默认隐藏
                        '自己库存': false,      // 自己库存默认隐藏
                        '可用总库存': true      // 可用总库存默认显示
                    },
                    formatter: function(name) {
                        if (name === '销量') {
                            return `销量(${totalSales})`;
                        }
                        return name;
                    }
                },
                series: [{
                    name: '销量',
                    type: 'bar',
                    data: data.sales.map(val => val || 0),
                    itemStyle: {
                        color: '#2563eb',
                        borderRadius: [4, 4, 0, 0]
                    },
                    label: {
                        show: true,
                        position: 'top',
                        fontSize: 10,
                        color: '#2563eb',  // 与柱状图颜色保持一致
                        formatter: function(params) {
                            return params.value > 0 ? params.value : '';
                        }
                    }
                }, {
                    name: '公共库存',
                    type: 'line',
                    yAxisIndex: 1,
                    data: data.public_stock || data.stock || [],  // 公共库存
                    itemStyle: {
                        color: '#22c55e'
                    },
                    symbol: 'circle',
                    symbolSize: 6,
                    showSymbol: true,
                    lineStyle: {
                        width: 2
                    },
                    connectNulls: true,
                    triggerLineEvent: true,
                    label: {
                        show: true,
                        position: 'top',
                        fontSize: 10,
                        color: '#22c55e',
                        formatter: function(params) {
                            return params.value != null ? params.value : '';
                        }
                    }
                }, {
                    name: '自己库存',
                    type: 'line',
                    yAxisIndex: 1,
                    data: data.own_stock || [],  // 自己库存 = SL + RW + GW
                    itemStyle: {
                        color: '#f59e0b'  // 橙色
                    },
                    symbol: 'diamond',
                    symbolSize: 6,
                    showSymbol: true,
                    lineStyle: {
                        width: 2,
                        type: 'dashed'  // 虚线
                    },
                    connectNulls: true,
                    triggerLineEvent: true,
                    label: {
                        show: true,
                        position: 'top',
                        fontSize: 10,
                        color: '#f59e0b',
                        formatter: function(params) {
                            return params.value != null ? params.value : '';
                        }
                    }
                }, {
                    name: '可用总库存',
                    type: 'line',
                    yAxisIndex: 1,
                    data: data.total_stock || [],  // 可用总库存
                    itemStyle: {
                        color: '#8b5cf6'  // 紫色
                    },
                    symbol: 'rect',
                    symbolSize: 6,
                    showSymbol: true,
                    lineStyle: {
                        width: 3  // 更粗的线条
                    },
                    connectNulls: true,
                    triggerLineEvent: true,
                    label: {
                        show: true,
                        position: 'top',
                        fontSize: 10,
                        color: '#8b5cf6',
                        formatter: function(params) {
                            return params.value != null ? params.value : '';
                        }
                    }
                }]
            };

            // 如果有实际到货时间，添加垂直标记线（支持多个到货时间）
            const arrivalDates = data.actual_arrival_dates || (data.actual_arrival_date ? [data.actual_arrival_date] : []);
            if (arrivalDates.length > 0) {
                // 过滤出在当前日期范围内的到货时间
                const validArrivalDates = arrivalDates.filter(date => data.dates.includes(date));

                if (validArrivalDates.length > 0) {
                    chartOption.series.push({
                        name: '实际到货时间',
                        type: 'line',
                        markLine: {
                            silent: true,
                            symbol: 'none',
                            lineStyle: {
                                type: 'dashed',
                                color: '#ff6b6b',
                                width: 2
                            },
                            label: {
                                show: true,
                                position: 'end',
                                formatter: function(params) {
                                    // 如果有多个到货时间，显示序号
                                    if (validArrivalDates.length > 1) {
                                        const index = validArrivalDates.indexOf(params.value) + 1;
                                        return `实际到货${index}`;
                                    }
                                    return '实际到货';
                                },
                                color: '#ff6b6b',
                                fontSize: 12,
                                fontWeight: 'bold'
                            },
                            data: validArrivalDates.map(date => ({
                                xAxis: date
                            }))
                        },
                        data: []  // 空数据，只显示标记线
                    });
                }
            }

            salesStockChart.setOption(chartOption);
        })
        .catch(error => {
            console.error('加载数据失败:', error);
            salesStockChart.setOption({
                title: {
                    text: '加载失败',
                    left: 'center',
                    top: 'center'
                }
            });
        })
        .finally(() => {
            salesStockChart.hideLoading();
        });
}

// 加载销量排名数据
function loadSalesRank() {
    const dateRange = getDateRange();
    
    const url = new URL('/api/sales_rank', window.location.origin);
    url.searchParams.append('start_date', dateRange.start);
    url.searchParams.append('end_date', dateRange.end);
    
    fetch(url)
        .then(r => r.json())
        .then(data => {
            console.log('销量排名数据:', data);

            // 检查数据是否有效
            if (!data || data.error) {
                console.error('销量排名API错误:', data?.error || '未知错误');
                return;
            }

            // 确保data是数组
            if (!Array.isArray(data)) {
                console.error('销量排名数据格式错误:', data);
                return;
            }

            if (!salesRankChart) {
                salesRankChart = echarts.init(document.getElementById('sales_rank_chart'));
            }

            // 如果没有数据，显示空状态
            if (data.length === 0) {
                salesRankChart.setOption({
                    title: {
                        text: '暂无销量数据',
                        left: 'center',
                        top: 'middle',
                        textStyle: {
                            color: '#999',
                            fontSize: 16
                        }
                    }
                });
                return;
            }

            // 计算是否需要滚动条（当SKU数量超过20时）
            const showDataZoom = data.length > 20;
            const endPercent = showDataZoom ? Math.min(100, (20 / data.length) * 100) : 100;
            
            salesRankChart.setOption({
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                grid: {
                    top: '8%',
                    bottom: showDataZoom ? '20%' : '12%',
                    left: '3%',
                    right: '3%',
                    containLabel: true
                },
                dataZoom: showDataZoom ? [{
                    type: 'slider',
                    start: 0,
                    end: endPercent,
                    bottom: '5%',
                    height: 20,
                    borderColor: '#ddd',
                    textStyle: {
                        color: '#666',
                        fontSize: 12
                    },
                    // 允许滑动但禁止调整宽度
                    zoomLock: true,           // 锁定缩放范围大小，但允许移动
                    disabled: false,          // 启用组件
                    handleSize: 0,            // 隐藏调整宽度的手柄
                    moveHandleSize: 10,       // 显示移动手柄，允许滑动
                    showDetail: true,         // 显示详情
                    showDataShadow: true,     // 显示数据阴影
                    realtime: true,           // 启用实时更新
                    filterMode: 'filter',     // 启用过滤模式
                    // 手柄样式
                    handleStyle: {
                        color: 'transparent',     // 隐藏调整大小的手柄
                        borderColor: 'transparent'
                    },
                    moveHandleStyle: {
                        color: '#2563eb',         // 显示移动手柄
                        borderColor: '#1d4ed8'
                    },
                    // 数据背景样式
                    dataBackground: {
                        areaStyle: {
                            color: '#f5f5f5'
                        },
                        lineStyle: {
                            color: '#ddd'
                        }
                    },
                    selectedDataBackground: {
                        areaStyle: {
                            color: '#e6f2ff'
                        },
                        lineStyle: {
                            color: '#2563eb'
                        }
                    }
                }] : [],
                xAxis: {
                    type: 'category',
                    data: data.map(item => getSKU(item)),
                    axisLabel: {
                        interval: 0,
                        rotate: 30,
                        fontSize: 11,
                        width: 100,
                        overflow: 'truncate'
                    }
                },
                yAxis: {
                    type: 'value',
                    name: '销量'
                },
                series: [{
                    data: data.map(item => item.product_quantity),
                    type: 'bar',
                    barWidth: '60%',
                    itemStyle: {
                        color: '#2563eb',
                        borderRadius: [4, 4, 0, 0]
                    },
                    label: {
                        show: true,
                        position: 'top',
                        fontSize: 12
                    }
                }]
            });
            
            // 添加点击事件
            salesRankChart.off('click');
            salesRankChart.on('click', function(params) {
                if (params.componentType === 'series') {
                    const sku = data[params.dataIndex] ? getSKU(data[params.dataIndex]) : '';
                    const skuInfo = skuList.find(item => getSKU(item) === sku);
                    if (skuInfo) {
                        selectSupplier(getSupplierCode(skuInfo));
                        setTimeout(() => selectSKU(sku), 100);
                    }
                }
            });
        })
        .catch(error => {
            console.error('加载销量排名数据失败:', error);
            if (salesRankChart) {
                salesRankChart.setOption({
                    title: {
                        text: '加载失败',
                        left: 'center',
                        top: 'middle',
                        textStyle: {
                            color: '#f56565',
                            fontSize: 16
                        }
                    }
                });
            }
        });
}

// 页面加载完成后初始化
window.addEventListener('load', function() {
    console.log('页面加载完成，开始初始化');
    
    // 初始化图表
    salesStockChart = echarts.init(document.getElementById('sales_stock_chart'));
    salesRankChart = echarts.init(document.getElementById('sales_rank_chart'));
    
    // 监听窗口大小变化，重绘图表
    window.addEventListener('resize', function() {
        if (salesStockChart) salesStockChart.resize();
        if (salesRankChart) salesRankChart.resize();
    });
    
    console.log('图表初始化完成，等待SKU选择后加载数据');
});
</script>
{% endblock %}