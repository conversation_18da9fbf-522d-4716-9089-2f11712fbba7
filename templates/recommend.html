{% extends "base.html" %}
{% block content %}
<!-- 性能监控面板 -->
<div id="performance-panel" class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4 text-sm">
  <div class="flex items-center justify-between">
    <div class="flex items-center gap-4">
      <span class="text-blue-700 font-medium">性能监控</span>
      <span id="load-time" class="text-gray-600">加载时间: --</span>
      <span id="cache-status" class="text-gray-600">缓存状态: 检查中...</span>
    </div>
    <button onclick="refreshCache()" class="px-3 py-1 bg-blue-500 text-white rounded text-xs hover:bg-blue-600">
      刷新缓存
    </button>
  </div>
</div>

<!-- 按需加载提示 -->
<div id="first-load-tip" class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4 hidden">
  <div class="flex items-center gap-2 text-blue-800">
    <div class="animate-pulse w-2 h-2 bg-blue-500 rounded-full"></div>
    <span>⚡ 按需计算模式 - 根据您的筛选条件智能计算，首次查询需要几秒钟...</span>
  </div>
</div>

<div class="bg-white/80 shadow-xl rounded-3xl p-8 mb-8">
  <form class="flex flex-wrap gap-6 items-center" onsubmit="return false;">
    <div>
      <label class="block text-gray-700 text-lg font-semibold mb-2">产品类型</label>
      <select id="product-type" class="border border-blue-200 rounded-2xl px-4 py-3 w-36 text-lg">
        <option value="all">全部</option>
        <option value="new">新品</option>
        <option value="old">老品</option>
      </select>
    </div>
    <div>
      <label class="block text-gray-700 text-lg font-semibold mb-2">供应商</label>
      <input id="supplier-input" list="suppliers"
        class="border border-blue-200 rounded-2xl px-4 py-3 w-36 text-lg"
        placeholder="输入/选择供应商">
      <datalist id="suppliers"></datalist>
    </div>
    <div>
      <label class="block text-gray-700 text-lg font-semibold mb-2">排序方式</label>
      <select id="sort-by" class="border border-blue-200 rounded-2xl px-4 py-3 w-36 text-lg">
        <option value="30d">30天</option>
        <option value="7d">7天</option>
      </select>
    </div>
    <div>
      <label class="block text-gray-700 text-lg font-semibold mb-2">价格区间</label>
      <div class="flex items-center gap-2">
        <input type="number" id="min-price" class="border border-blue-200 rounded-2xl px-3 py-2 w-24 text-lg" placeholder="最低价">
        <span class="text-gray-400">-</span>
        <input type="number" id="max-price" class="border border-blue-200 rounded-2xl px-3 py-2 w-24 text-lg" placeholder="最高价">
      </div>
    </div>
    <button onclick="loadRecommendations(1)" class="bg-blue-500 text-white px-8 py-3 rounded-2xl shadow hover:bg-blue-600 text-lg font-semibold transition">筛选</button>
    <button onclick="resetFilters()" class="bg-gray-500 text-white px-8 py-3 rounded-2xl shadow hover:bg-gray-600 text-lg font-semibold transition">重置</button>
  </form>
</div>

<!-- 加载状态 -->
<div id="loading-indicator" class="hidden text-center py-12">
  <div class="inline-flex items-center gap-2 text-blue-500">
    <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
    <span>加载中...</span>
  </div>
</div>

<!-- 结果统计 -->
<div id="results-info" class="hidden mb-4 text-gray-600">
  <span>共找到 <span id="total-count">0</span> 个SKU，当前显示第 <span id="current-range">0-0</span> 个</span>
</div>

<div id="recommendations-container" class="grid grid-cols-4 gap-6 w-full" style="display: grid; grid-template-columns: repeat(4, minmax(0, 1fr));">
  <!-- 数据将通过JavaScript动态加载 -->
</div>

<!-- 分页控件 -->
<div id="pagination-container" class="hidden flex justify-center items-center space-x-4 mt-8">
  <button id="prev-btn" class="px-4 py-2 bg-blue-500 text-white rounded-lg disabled:bg-gray-300 disabled:cursor-not-allowed hover:bg-blue-600 transition-colors">
    上一页
  </button>
  <div id="page-info" class="text-gray-600">
    第 <span id="current-page">1</span> 页 / 共 <span id="total-pages">1</span> 页
  </div>
  <button id="next-btn" class="px-4 py-2 bg-blue-500 text-white rounded-lg disabled:bg-gray-300 disabled:cursor-not-allowed hover:bg-blue-600 transition-colors">
    下一页
  </button>
  <div class="ml-4 flex items-center gap-2">
    <span class="text-gray-600">跳转到第</span>
    <input type="number" id="goto-page" class="border border-gray-300 rounded px-2 py-1 w-16 text-center" min="1">
    <span class="text-gray-600">页</span>
    <button onclick="gotoPage()" class="px-3 py-1 bg-gray-500 text-white rounded hover:bg-gray-600">跳转</button>
  </div>
</div>

<script>
// 全局变量
let skuList = [];
let orderedSuppliers = [];
let currentPage = 1;
let totalPages = 1;
let totalCount = 0;
const itemsPerPage = 24; // 6排 × 4列

// 图片缓存
const imageCache = new Map();

// 批量渲染函数，避免阻塞主线程（优化版）
function renderItemsBatch(container, items, batchSize = 12) {
  let index = 0;
  
  function renderBatch() {
    const fragment = document.createDocumentFragment();
    const endIndex = Math.min(index + batchSize, items.length);
    
    for (let i = index; i < endIndex; i++) {
      const item = items[i];
      const div = createItemElement(item);
      fragment.appendChild(div);
    }
    
    container.appendChild(fragment);
    index = endIndex;
    
    // 显示渲染进度
    const progress = Math.round((index / items.length) * 100);
    if (progress < 100) {
      const indicator = document.getElementById('loading-indicator');
      if (indicator) {
        indicator.textContent = `渲染中... ${progress}%`;
      }
    }
    
    if (index < items.length) {
      requestAnimationFrame(renderBatch);
    } else {
      // 渲染完成
      const indicator = document.getElementById('loading-indicator');
      if (indicator) {
        indicator.textContent = '加载中...';
      }
    }
  }
  
  renderBatch();
}

// 创建单个商品元素（优化版）
function createItemElement(item) {
  const div = document.createElement('div');
  div.className = 'bg-white rounded-2xl shadow hover:shadow-xl transition-transform duration-200 p-4 hover:scale-105';
  
  // 预处理数据
  const imgSrc = item.product_image || 'https://via.placeholder.com/176x176/f3f4f6/9ca3af?text=No+Image';
  const price = typeof item.price === 'number' ? item.price.toFixed(2) : '0.00';

  div.innerHTML = `
    <div class="relative mx-auto mb-2" style="width: 160px; height: 160px;">
      <div class="bg-gray-50 rounded-lg overflow-hidden w-full h-full flex items-center justify-center">
        <img data-src="${imgSrc}"
          class="lazy-img object-contain w-full h-full max-w-[160px] max-h-[160px] opacity-0 transition-opacity duration-300"
          alt="${item.sku}"
          loading="lazy"
          data-optimize="true">
      </div>
    </div>
    <div class="text-sm text-gray-500 truncate mb-1 text-center">SKU: ${item.sku}</div>
    <div class="text-xs text-gray-700 font-medium mb-1 truncate text-center">供应商：${item.supplier_code}</div>
    <div class="text-xs mb-1 text-center">
      <span class="text-gray-700">30天库存变化：</span>
      <span class="${(item.stock_change_30d || 0) > 0 ? 'text-blue-600' : 'text-gray-500'} font-medium">
        ${item.stock_change_30d || 0}
      </span>
    </div>
    <div class="text-xs mb-1 text-center">
      <span class="text-gray-700">7天库存变化：</span>
      <span class="${(item.stock_change_7d || 0) > 0 ? 'text-green-600' : 'text-gray-500'} font-medium">
        ${item.stock_change_7d || 0}
      </span>
    </div>
    <div class="text-xs text-gray-500 mb-1 text-center">库存：${item.available_stock || '-'}</div>
    <div class="text-xs text-gray-500 mb-1 text-center">价格：$${price}</div>
    <div class="text-xs text-gray-500 mb-1 text-center">预计到货时间: ${item.estimated_restock_date || '-'}</div>
    <div class="text-xs text-gray-500 mb-2 truncate text-center">预计到货数量: ${item.estimated_restock_qty || '-'}</div>
  `;
  
  return div;
}

// 懒加载图片
function setupLazyLoading() {
  const imageObserver = new IntersectionObserver((entries, observer) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const img = entry.target;
        const src = img.getAttribute('data-src');
        
        if (src) {
          // 检查缓存
          if (imageCache.has(src)) {
            img.src = src;
            img.classList.remove('opacity-0');
            observer.unobserve(img);
            return;
          }
          // 直接设置图片源
          img.src = src;
          imageCache.set(src, true);
          img.classList.remove('opacity-0');
          observer.unobserve(img);
        }
      }
    });
  }, {
    root: null,
    rootMargin: '50px',
    threshold: 0.1
  });
  
  // 观察所有懒加载图片
  document.querySelectorAll('.lazy-img').forEach(img => {
    imageObserver.observe(img);
  });
}

// 初始化供应商列表
function initSupplierList() {
  return fetch('/api/sku_list')
    .then(response => response.json())
    .then(data => {
      skuList = data.sku_list || [];
      orderedSuppliers = data.ordered_suppliers || [];
      
      // 初始化供应商下拉列表
      const datalist = document.getElementById('suppliers');
      datalist.innerHTML = '';
      let supplierSet = new Set();
      
      skuList.forEach(item => {
        if (item.supplier_code && !supplierSet.has(item.supplier_code)) {
          const opt = document.createElement('option');
          opt.value = item.supplier_code;
          // 出过单的供应商用绿色字体
          if (orderedSuppliers.includes(item.supplier_code)) {
            opt.style.color = '#22c55e';
            opt.style.fontWeight = 'bold';
          }
          datalist.appendChild(opt);
          supplierSet.add(item.supplier_code);
        }
      });
    })
    .catch(err => {
      console.error('供应商数据加载失败:', err);
    });
}

// 获取所有可用的供应商列表
function getAllSuppliers() {
  const supplierSet = new Set();
  skuList.forEach(item => {
    if (item.supplier_code) {
      supplierSet.add(item.supplier_code);
    }
  });
  return Array.from(supplierSet);
}

// 智能匹配供应商
function findMatchingSupplier(input) {
  const allSuppliers = getAllSuppliers();
  const inputLower = input.toLowerCase().trim();

  // 1. 精确匹配
  const exactMatch = allSuppliers.find(supplier => supplier.toLowerCase() === inputLower);
  if (exactMatch) return exactMatch;

  // 2. 供应商代码包含输入内容（如输入"465"匹配"W465"）
  const partialMatch = allSuppliers.find(supplier => supplier.toLowerCase().includes(inputLower));
  if (partialMatch) return partialMatch;

  return null;
}

// 供应商输入框处理
const supplierInput = document.getElementById('supplier-input');

// 输入时实时验证（不自动加载数据，避免频繁请求）
supplierInput.addEventListener('input', function() {
  const val = this.value.trim();
  if (val) {
    const matchedSupplier = findMatchingSupplier(val);
    if (matchedSupplier) {
      // 找到匹配的供应商，显示提示
      this.style.borderColor = '#22c55e';
      this.title = `将匹配: ${matchedSupplier}`;
    } else {
      // 没有找到匹配
      this.style.borderColor = '#ef4444';
      this.title = '未找到匹配的供应商';
    }
  } else {
    this.style.borderColor = '#d1d5db';
    this.title = '';
  }
});

// 回车键处理
supplierInput.addEventListener('keydown', function(e) {
  if (e.key === 'Enter') {
    e.preventDefault();
    const val = this.value.trim();
    if (val) {
      const matchedSupplier = findMatchingSupplier(val);
      if (matchedSupplier) {
        this.value = matchedSupplier;
        // 触发change事件
        this.dispatchEvent(new Event('change'));
        // 自动加载推荐数据
        loadRecommendations(1);
      } else {
        alert(`未找到匹配的供应商: ${val}\n请从下拉列表中选择有效的供应商`);
        this.focus();
      }
    }
  }
});

// 失去焦点时验证
supplierInput.addEventListener('blur', function() {
  const val = this.value.trim();
  if (val) {
    const allSuppliers = getAllSuppliers();
    if (!allSuppliers.includes(val)) {
      const matchedSupplier = findMatchingSupplier(val);
      if (matchedSupplier) {
        this.value = matchedSupplier;
        this.dispatchEvent(new Event('change'));
      } else {
        alert(`供应商 "${val}" 不存在，请选择有效的供应商`);
        this.value = '';
        this.style.borderColor = '#d1d5db';
        this.title = '';
      }
    }
  }
});

// 值改变时的样式处理
supplierInput.addEventListener('change', function() {
  const val = this.value.trim();

  // 检查是否是出过单的供应商
  if (val && orderedSuppliers.includes(val)) {
    this.style.color = '#22c55e';
    this.style.fontWeight = 'bold';
    this.style.borderColor = '#22c55e';
  } else if (val) {
    this.style.color = '#000';
    this.style.fontWeight = 'normal';
    this.style.borderColor = '#d1d5db';
  } else {
    this.style.color = '#000';
    this.style.fontWeight = 'normal';
    this.style.borderColor = '#d1d5db';
  }
  this.title = '';
});

// 显示加载状态（优化版）
function showLoading() {
  const container = document.getElementById('recommendations-container');
  const indicator = document.getElementById('loading-indicator');
  
  if (indicator) {
    indicator.classList.remove('hidden');
  }
  
  // 不要设置透明度，直接显示骨架屏
  // container.classList.add('opacity-50');
  
  // 显示更精美的骨架屏
  container.innerHTML = Array(16).fill(0).map(() => `
    <div class="bg-white rounded-2xl shadow p-4 animate-pulse">
      <div class="bg-gradient-to-r from-gray-200 to-gray-300 rounded-lg h-40 mb-4"></div>
      <div class="space-y-2">
        <div class="bg-gradient-to-r from-gray-200 to-gray-300 rounded h-4 w-3/4 mx-auto"></div>
        <div class="bg-gradient-to-r from-gray-200 to-gray-300 rounded h-3 w-1/2 mx-auto"></div>
        <div class="bg-gradient-to-r from-gray-200 to-gray-300 rounded h-3 w-2/3 mx-auto"></div>
        <div class="bg-gradient-to-r from-gray-200 to-gray-300 rounded h-3 w-1/3 mx-auto"></div>
      </div>
    </div>
  `).join('');
}

// 隐藏加载状态
function hideLoading() {
  const indicator = document.getElementById('loading-indicator');
  if (indicator) {
    indicator.classList.add('hidden');
  }
  document.getElementById('recommendations-container').classList.remove('opacity-50');
}

// 更新分页信息
function updatePaginationInfo() {
  document.getElementById('current-page').textContent = currentPage;
  document.getElementById('total-pages').textContent = totalPages;
  document.getElementById('total-count').textContent = totalCount;
  
  const startIndex = (currentPage - 1) * itemsPerPage + 1;
  const endIndex = Math.min(currentPage * itemsPerPage, totalCount);
  document.getElementById('current-range').textContent = `${startIndex}-${endIndex}`;
  
  // 更新按钮状态
  document.getElementById('prev-btn').disabled = currentPage <= 1;
  document.getElementById('next-btn').disabled = currentPage >= totalPages;
  document.getElementById('goto-page').max = totalPages;
  
  // 显示/隐藏分页控件和结果信息
  const showPagination = totalCount > 0;
  document.getElementById('pagination-container').classList.toggle('hidden', !showPagination);
  document.getElementById('results-info').classList.toggle('hidden', !showPagination);
}

// 加载选品推荐数据
function loadRecommendations(page = 1) {
  currentPage = page;
  saveFilterState(); // 保存当前筛选状态
  showLoading();
  
  const startTime = performance.now(); // 记录开始时间
  
  const productType = document.getElementById('product-type').value;
  const supplier = document.getElementById('supplier-input').value;
  const sortBy = document.getElementById('sort-by').value;
  const minPrice = document.getElementById('min-price').value;
  const maxPrice = document.getElementById('max-price').value;
  
  const url = new URL('/api/recommendations', window.location.origin);
  url.searchParams.append('type', productType);
  url.searchParams.append('supplier', supplier);
  url.searchParams.append('sort_by', sortBy);
  url.searchParams.append('page', page);
  url.searchParams.append('per_page', itemsPerPage);
  if (minPrice) url.searchParams.append('min_price', minPrice);
  if (maxPrice) url.searchParams.append('max_price', maxPrice);
  
                  // 请求URL和筛选参数已准备
  
  fetch(url)
    .then(response => {
                      // API响应状态检查
      return response.json();
    })
    .then(data => {
      console.log('API返回数据:', data);
      hideLoading();
      
      const endTime = performance.now();
      updateLoadTime(endTime - startTime); // 更新加载时间
      
      const container = document.getElementById('recommendations-container');
      container.innerHTML = '';
      
      if (!data || !data.items || data.items.length === 0) {
        console.log('没有数据，显示空结果消息');
        container.innerHTML = '<div class="col-span-4 text-center text-gray-500 py-12">没有找到符合条件的SKU</div>';
        totalCount = 0;
        totalPages = 1;
        updatePaginationInfo();
        return;
      }

      console.log(`成功获取 ${data.items.length} 个SKU数据，总计 ${data.total} 个`);

      // 更新分页信息
      totalCount = data.total || 0;
      totalPages = data.pages || 1;
      currentPage = data.page || 1;
      
      // 使用 requestAnimationFrame 批量渲染，避免阻塞主线程
      renderItemsBatch(container, data.items);
      
      // 渲染完成后设置懒加载
      setTimeout(() => {
        setupLazyLoading();
        updatePaginationInfo();
      }, 100);
    })
    .catch(error => {
      console.error('加载推荐数据失败:', error);
      hideLoading();
      const container = document.getElementById('recommendations-container');
      container.innerHTML = '<div class="col-span-4 text-center text-red-500 py-12">加载数据失败，请稍后重试</div>';
      totalCount = 0;
      totalPages = 1;
      updatePaginationInfo();
    });
}

// 更新加载时间显示
function updateLoadTime(milliseconds) {
  const seconds = (milliseconds / 1000).toFixed(2);
  document.getElementById('load-time').textContent = `加载时间: ${seconds}s`;
}

// 更新缓存状态（优化版）
function updateCacheStatus() {
  fetch('/api/cache_info')
    .then(response => response.json())
    .then(data => {
      const status = data.cache_exists ? 
        `已缓存 ${data.cached_queries}个查询 (${data.cache_size}个SKU, ${data.cache_age_minutes.toFixed(1)}分钟前)` :
        `暂无缓存 - 按需计算`;
      document.getElementById('cache-status').textContent = `缓存状态: ${status}`;
      
      // 缓存状态颜色
      const cacheSpan = document.getElementById('cache-status');
      if (data.cache_exists && data.cache_valid) {
        cacheSpan.className = 'text-green-600';
        // 有缓存，隐藏提示
        const tip = document.getElementById('first-load-tip');
        if (tip) tip.classList.add('hidden');
      } else {
        cacheSpan.className = 'text-blue-600';
        // 无缓存，显示按需计算提示
        const tip = document.getElementById('first-load-tip');
        if (tip) tip.classList.remove('hidden');
      }
    })
    .catch(error => {
      console.error('获取缓存状态失败:', error);
      document.getElementById('cache-status').textContent = '缓存状态: 连接中...';
      // 显示连接提示
      const tip = document.getElementById('first-load-tip');
      if (tip) tip.classList.remove('hidden');
    });
}

// 刷新缓存
function refreshCache() {
  const button = event.target;
  button.disabled = true;
  button.textContent = '刷新中...';
  
  fetch('/api/refresh_cache', { method: 'POST' })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        alert(`缓存刷新成功！\n重建时间: ${data.rebuild_time_seconds}秒\n缓存大小: ${data.cache_size}个SKU`);
        updateCacheStatus();
        // 重新加载当前页面数据
        loadRecommendations(currentPage);
      } else {
        alert('缓存刷新失败');
      }
    })
    .catch(error => {
      console.error('刷新缓存失败:', error);
      alert('刷新缓存失败: ' + error.message);
    })
    .finally(() => {
      button.disabled = false;
      button.textContent = '刷新缓存';
    });
}

// 分页按钮事件
document.getElementById('prev-btn').addEventListener('click', () => {
  if (currentPage > 1) {
    loadRecommendations(currentPage - 1);
  }
});

document.getElementById('next-btn').addEventListener('click', () => {
  if (currentPage < totalPages) {
    loadRecommendations(currentPage + 1);
  }
});

// 跳转到指定页面
function gotoPage() {
  const page = parseInt(document.getElementById('goto-page').value);
  if (page >= 1 && page <= totalPages) {
    loadRecommendations(page);
  } else {
    alert(`请输入1到${totalPages}之间的页码`);
  }
}

// 回车键跳转
document.getElementById('goto-page').addEventListener('keypress', function(e) {
  if (e.key === 'Enter') {
    gotoPage();
  }
});

// 保存筛选状态到localStorage
function saveFilterState() {
  const state = {
    productType: document.getElementById('product-type').value,
    supplier: document.getElementById('supplier-input').value,
    sortBy: document.getElementById('sort-by').value,
    minPrice: document.getElementById('min-price').value,
    maxPrice: document.getElementById('max-price').value,
    currentPage: currentPage
  };
  localStorage.setItem('recommendationFilters', JSON.stringify(state));
}

// 恢复筛选状态
function restoreFilterState() {
  try {
    const saved = localStorage.getItem('recommendationFilters');
    if (saved) {
      const state = JSON.parse(saved);
      document.getElementById('product-type').value = state.productType || 'all';
      document.getElementById('supplier-input').value = state.supplier || '';
      document.getElementById('sort-by').value = state.sortBy || '30d';
      document.getElementById('min-price').value = state.minPrice || '';
      document.getElementById('max-price').value = state.maxPrice || '';
      
      // 如果有供应商选择，设置样式
      const supplierInput = document.getElementById('supplier-input');
      if (state.supplier && orderedSuppliers.includes(state.supplier)) {
        supplierInput.style.color = '#22c55e';
        supplierInput.style.fontWeight = 'bold';
      }
      
      return state.currentPage || 1;
    }
  } catch (e) {
    console.error('恢复筛选状态失败:', e);
  }
  return 1;
}

// 页面加载时初始化（优化版本）
let isInitialized = false;
window.addEventListener('DOMContentLoaded', function() {
  if (isInitialized) return; // 防止重复初始化
  isInitialized = true;
  
  // 并行执行所有初始化任务
  const initTasks = [
    updateCacheStatus(),
    initSupplierList()
  ];
  
  // 立即加载推荐数据，不等待供应商列表
  const savedPage = restoreFilterState();
  loadRecommendations(savedPage);
  
  // 后台完成供应商初始化
  Promise.all(initTasks).then(() => {
    console.log('所有初始化任务完成');
  }).catch(err => {
    console.error('初始化任务失败:', err);
  });
  
  // 每30秒更新一次缓存状态
  setInterval(updateCacheStatus, 30000);
});

// 为所有筛选条件添加change事件监听（重置到第一页）
document.getElementById('product-type').addEventListener('change', () => loadRecommendations(1));
document.getElementById('sort-by').addEventListener('change', () => loadRecommendations(1));
document.getElementById('min-price').addEventListener('input', debounce(() => loadRecommendations(1), 500));
document.getElementById('max-price').addEventListener('input', debounce(() => loadRecommendations(1), 500));

// 重置筛选条件
function resetFilters() {
  document.getElementById('product-type').value = 'all';
  document.getElementById('supplier-input').value = '';
  document.getElementById('sort-by').value = '30d';
  document.getElementById('min-price').value = '';
  document.getElementById('max-price').value = '';
  
  // 重置供应商输入框样式
  const supplierInput = document.getElementById('supplier-input');
  supplierInput.style.color = '#000';
  supplierInput.style.fontWeight = 'normal';
  
  // 清除保存的状态
  localStorage.removeItem('recommendationFilters');
  
  // 重新加载数据
  loadRecommendations(1);
}

// 防抖函数
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}
</script>
{% endblock %} 