{% extends "base.html" %}
{% block head %}


<style>
/* 使用更高的特异性来确保样式生效 */
.alert-page input[type="date"],
.alert-page .date-input {
  padding: 12px 40px 12px 16px !important;
  border: 2px solid #d1d5db !important;
  border-radius: 12px !important;
  font-size: 14px !important;
  min-width: 140px !important;
  color: #374151 !important;
  background-color: #ffffff !important;
  position: relative !important;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, "Helvetica Neue", Arial, sans-serif !important;
  transition: all 0.2s ease !important;
}

.alert-page input[type="date"]:focus,
.alert-page .date-input:focus {
  outline: none !important;
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1) !important;
  transform: translateY(-1px) !important;
}

/* 确保日期输入框在所有浏览器中都能正常显示 */
.alert-page input[type="date"]::-webkit-calendar-picker-indicator {
  display: block !important;
  background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%236b7280'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z'/%3E%3C/svg%3E") !important;
  background-repeat: no-repeat !important;
  background-size: 16px 16px !important;
  width: 20px !important;
  height: 20px !important;
  cursor: pointer !important;
  position: absolute !important;
  right: 12px !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  opacity: 0.6 !important;
}

/* 为 Safari 和其他 WebKit 浏览器优化 */
.alert-page input[type="date"]::-webkit-inner-spin-button,
.alert-page input[type="date"]::-webkit-clear-button {
  display: none !important;
}

.alert-page input[type="date"]::-webkit-datetime-edit {
  padding: 0 !important;
}

.alert-page input[type="date"]::-webkit-datetime-edit-fields-wrapper {
  background: transparent !important;
}

.alert-page input[type="date"]::-webkit-datetime-edit-text {
  color: #374151 !important;
  padding: 0 !important;
}

.alert-page input[type="date"]::-webkit-datetime-edit-month-field,
.alert-page input[type="date"]::-webkit-datetime-edit-day-field,
.alert-page input[type="date"]::-webkit-datetime-edit-year-field {
  color: #374151 !important;
}

.alert-page .time-filter-container {
  background: white !important;
  border-radius: 16px !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
  padding: 24px !important;
  margin-bottom: 24px !important;
  border: 1px solid rgba(229, 231, 235, 0.8) !important;
}

.alert-page .filter-title {
  font-size: 18px !important;
  font-weight: 600 !important;
  color: #1f2937 !important;
  margin-bottom: 20px !important;
}

.alert-page .date-inputs {
  display: flex !important;
  gap: 20px !important;
  align-items: flex-end !important;
  flex-wrap: wrap !important;
}

.alert-page .date-input-group {
  display: flex !important;
  flex-direction: column !important;
  gap: 8px !important;
  position: relative !important;
}

.alert-page .date-label {
  font-size: 14px !important;
  font-weight: 500 !important;
  color: #6b7280 !important;
  margin-bottom: 4px !important;
}

/* 为不支持 type="date" 的浏览器提供后备样式 */
.alert-page .date-input[type="text"] {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%236b7280'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z'/%3E%3C/svg%3E") !important;
  background-repeat: no-repeat !important;
  background-position: right 12px center !important;
  background-size: 16px !important;
  padding-right: 40px !important;
}

.alert-page .search-btn {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%) !important;
  color: white !important;
  border: none !important;
  border-radius: 12px !important;
  padding: 12px 28px !important;
  font-size: 14px !important;
  font-weight: 600 !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
  box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.1) !important;
  text-transform: none !important;
  letter-spacing: 0.025em !important;
}

.alert-page .search-btn:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.15) !important;
}

.alert-page .search-btn:disabled {
  background: #9ca3af !important;
  cursor: not-allowed !important;
  transform: none !important;
}

/* 确保按钮文字始终可见 */
.alert-page #search-btn {
  font-size: 14px !important;
  font-weight: 500 !important;
  color: white !important;
  text-align: center !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 8px !important;
}

.alert-page #search-btn span {
  display: inline !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* 确保在移动设备上也能正常显示 */
@media (max-width: 768px) {
  .alert-page .date-inputs {
    flex-direction: column !important;
    align-items: stretch !important;
  }
  
  .alert-page .date-input {
    min-width: 100% !important;
  }
  
  .alert-page #search-btn {
    align-self: stretch !important;
    margin-top: 16px !important;
  }
}

/* 浏览器特定样式修复 */
/* Chrome, Safari, Edge */
.alert-page input[type="date"]::-webkit-inner-spin-button,
.alert-page input[type="date"]::-webkit-clear-button {
  display: none !important;
}

/* Firefox */
.alert-page input[type="date"] {
  -moz-appearance: textfield !important;
}

/* 确保日历图标可见 */
.alert-page input[type="date"]::-webkit-calendar-picker-indicator:hover {
  opacity: 1 !important;
  background-color: rgba(59, 130, 246, 0.1) !important;
  border-radius: 4px !important;
}

/* 覆盖任何可能的Tailwind重置 */
.alert-page * {
  box-sizing: border-box !important;
}


</style>
{% endblock %}

{% block content %}
<div class="alert-page min-h-screen p-6" style="background: linear-gradient(135deg, var(--primary-50) 0%, var(--secondary-50) 100%);">
  <div class="mb-6">
    <h1 class="text-2xl font-semibold text-blue-700">库存提醒</h1>
    <p class="mt-2 text-sm text-gray-600">库存监控和售罄SKU查询 · 默认显示当天售罄SKU</p>
  </div>



  <!-- 库存提醒 -->
  <div class="card mb-6">
    <div class="card-header">
      <h3 class="text-lg font-semibold text-primary-700">库存提醒</h3>
      <p class="text-sm text-secondary-600 mt-1">基于时间范围的库存变化查询</p>
    </div>
    <div class="card-body">
      <div class="flex flex-wrap gap-4 items-end">
        <div class="flex-1 min-w-40">
          <label class="block text-sm font-medium text-secondary-700 mb-2" for="start-date">开始日期</label>
          <input type="date" id="start-date" class="input" required autocomplete="off">
        </div>
        <div class="flex-1 min-w-40">
          <label class="block text-sm font-medium text-secondary-700 mb-2" for="end-date">结束日期</label>
          <input type="date" id="end-date" class="input" required autocomplete="off">
        </div>
        <button id="search-btn" class="btn btn-primary" type="button">
          <i class="fas fa-search"></i>
          <span>查询</span>
        </button>
      </div>
    </div>
  </div>

  <div id="error-message" class="hidden mb-6">
    <div class="bg-red-50 border-l-4 border-red-500 p-4 rounded-r-xl">
      <p class="text-sm text-red-700" id="error-text"></p>
    </div>
  </div>

  <div>
    <div class="flex items-center justify-between mb-4">
      <div>
        <h2 class="text-lg font-medium text-gray-900">售罄SKU列表</h2>
        <p class="text-sm text-gray-500 mt-1" id="current-date-info">默认显示当天售罄SKU，可通过上方日期筛选查看其他日期</p>
      </div>
      <div class="text-sm text-gray-500" id="sku-count"></div>
    </div>

    <!-- 颜色说明 -->
    <div class="mb-6 p-4 bg-blue-50 rounded-lg">
      <div class="text-sm font-medium text-gray-700 mb-2">颜色标记说明：</div>
      <div class="flex flex-wrap gap-4 text-xs">
        <div class="flex items-center">
          <div class="w-4 h-4 border-2 border-green-300 rounded mr-2"></div>
          <span class="text-gray-600">绿色边框：有订单 + 有店铺SKU</span>
        </div>
        <div class="flex items-center">
          <div class="w-4 h-4 border-2 border-orange-300 rounded mr-2"></div>
          <span class="text-gray-600">橙色边框：仅有店铺SKU</span>
        </div>
        <div class="flex items-center">
          <div class="w-4 h-4 border-2 border-gray-200 rounded mr-2"></div>
          <span class="text-gray-600">灰色边框：无店铺SKU</span>
        </div>
      </div>
    </div>

    <div class="grid gap-6" style="grid-template-columns: repeat(3, 1fr);" id="sku-list"></div>
  </div>
</div>

<script>
function showError(message) {
  const errorDiv = document.getElementById('error-message');
  const errorText = document.getElementById('error-text');
  errorText.textContent = message;
  errorDiv.classList.remove('hidden');
  setTimeout(() => { errorDiv.classList.add('hidden'); }, 5000);
}

function renderSkuCard(item) {
  const hasSales = item.historical_sales > 0 || (item.历史销量 && item.历史销量 > 0);
  const hasShopSku = item.has_shop_sku || (item.shop_skus && item.shop_skus.length > 0);

  // 确定卡片样式
  let cardStyle = '';
  let skuTextColor = '';

  if (hasSales && hasShopSku) {
    // 既有订单又有店铺SKU：绿色
    cardStyle = 'ring-2 ring-green-200 border-green-300';
    skuTextColor = 'text-green-600';
  } else if (hasShopSku) {
    // 只有店铺SKU：橙色
    cardStyle = 'ring-2 ring-orange-200 border-orange-300';
    skuTextColor = 'text-orange-600';
  } else {
    // 默认样式
    cardStyle = hasSales ? 'ring-2 ring-blue-100' : '';
    skuTextColor = hasSales ? 'text-green-600' : 'text-gray-500';
  }

  const salesBadge = hasSales ?
    `<span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">历史销量: ${item.historical_sales || item.历史销量 || 0}</span>` :
    `<span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-600">无销量记录</span>`;

  // 构建店铺SKU显示
  let shopSkuHtml = '';
  if (hasShopSku && item.shop_skus) {
    shopSkuHtml = `
      <div class="mt-2 pt-2 border-t border-gray-100">
        <div class="text-xs text-gray-500 mb-1">店铺SKU：</div>
        <div class="space-y-1">
          ${item.shop_skus.map(shop => `
            <div class="text-xs">
              <span class="font-medium text-blue-600">${shop.shop_name}：</span>
              <span class="text-gray-900">${shop.shop_sku}</span>
            </div>
          `).join('')}
        </div>
      </div>`;
  }

  return `<div class="bg-white rounded-xl shadow-sm hover:shadow-md overflow-hidden ${cardStyle}">
    <div class="flex">
      <div class="w-20 h-20 flex-shrink-0 m-3">
        <img src="${item.产品主图 || item.产品图片 || '/static/images/placeholder.png'}" loading="lazy" alt="${item.SKU}" class="w-full h-full object-cover rounded-lg">
      </div>
      <div class="flex-1 p-3 pl-0">
        <div class="flex items-center justify-between mb-2">
          <div class="font-medium ${skuTextColor} truncate text-sm font-semibold">${item.SKU}</div>
          <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">售罄</span>
        </div>
        <div class="mb-2">
          ${salesBadge}
        </div>
        <div class="grid gap-1">
          <div class="flex items-center text-xs">
            <span class="text-gray-500">售罄日期：</span>
            <span class="text-gray-900 ml-1">${item.售罄日期}</span>
          </div>
          <div class="flex items-center text-xs">
            <span class="text-gray-500">断货前库存：</span>
            <span class="text-gray-900 ml-1">${item.之前库存}</span>
          </div>
          ${(item['预计到货时间'] || item['estimated_restock_date']) && (item['预计到货时间'] !== '' || item['estimated_restock_date'] !== '') ? `
          <div class="flex items-center text-xs">
            <span class="text-gray-500">预计到货时间：</span>
            <span class="text-gray-900 ml-1">${item['预计到货时间'] || item['estimated_restock_date']}</span>
          </div>` : ''}
          ${(item['预计到货数量'] || item['estimated_restock_qty']) && (item['预计到货数量'] !== '' || item['estimated_restock_qty'] !== '') && (item['预计到货数量'] !== null || item['estimated_restock_qty'] !== null) && (item['预计到货数量'] !== 0 || item['estimated_restock_qty'] !== 0) ? `
          <div class="flex items-center text-xs">
            <span class="text-gray-500">预计到货数量：</span>
            <span class="text-gray-900 ml-1">${item['预计到货数量'] || item['estimated_restock_qty']}件</span>
          </div>` : ''}
        </div>
        ${shopSkuHtml}
      </div>
    </div>
  </div>`;
}

async function loadAlertData(startDate, endDate) {
  const container = document.getElementById('sku-list');
  const skuCount = document.getElementById('sku-count');
  const searchBtn = document.getElementById('search-btn');
  
  container.innerHTML = '<div class="col-span-3 py-12 text-center">正在加载数据...</div>';
  searchBtn.disabled = true;
  
  try {
    let url = new URL('/api/inventory_alerts', window.location.origin);
    if (startDate) url.searchParams.append('start_date', startDate);
    if (endDate) url.searchParams.append('end_date', endDate);
    
                    // 请求URL已准备
    
    const response = await fetch(url);
    if (!response.ok) {
      const errorText = await response.text();
      console.error('API错误:', response.status, errorText);
      throw new Error(`服务器错误 ${response.status}: ${errorText}`);
    }
    
    const data = await response.json();
                    // API数据加载完成
    
    if (data.error) {
      throw new Error(data.error);
    }
    
    if (Array.isArray(data)) {
      // 更新日期信息显示
      const dateInfo = document.getElementById('current-date-info');
      if (startDate === endDate) {
        if (startDate === new Date().toISOString().split('T')[0]) {
          dateInfo.textContent = '当天售罄SKU列表';
        } else {
          dateInfo.textContent = `${startDate} 售罄SKU列表`;
        }
      } else {
        dateInfo.textContent = `${startDate} 至 ${endDate} 售罄SKU列表`;
      }

      if (data.length === 0) {
        const dateText = startDate === endDate ?
          (startDate === new Date().toISOString().split('T')[0] ? '今天' : startDate) :
          `${startDate} 至 ${endDate}`;
        container.innerHTML = `<div class="col-span-3 py-12 text-center text-gray-500">
          <div class="mb-2">📋</div>
          <div>${dateText}暂无售罄记录</div>
          <div class="text-xs mt-2 text-gray-400">这是个好消息！说明库存管理良好</div>
        </div>`;
        skuCount.textContent = '共 0 条售罄记录';
      } else {
        skuCount.textContent = `共 ${data.length} 条售罄记录`;
        container.innerHTML = data.map(renderSkuCard).join('');
      }
    } else {
      throw new Error('数据格式错误: 期望数组格式');
    }
  } catch (error) {
    console.error('加载库存提醒数据失败:', error);
    container.innerHTML = '<div class="col-span-3 py-12 text-center text-red-500">加载失败</div>';
    showError(`加载数据失败: ${error.message}`);
  } finally {
    searchBtn.disabled = false;
  }
}

function setDefaultDates() {
  const today = new Date();

  // 默认开始时间和结束时间都是当天
  document.getElementById('start-date').value = today.toISOString().split('T')[0];
  document.getElementById('end-date').value = today.toISOString().split('T')[0];
}

function handleSearch() {
  const startDate = document.getElementById('start-date').value;
  const endDate = document.getElementById('end-date').value;

  if (!startDate || !endDate) {
    showError('请选择完整的日期范围');
    return;
  }

  if (new Date(startDate) > new Date(endDate)) {
    showError('开始日期不能晚于结束日期');
    return;
  }

  loadAlertData(startDate, endDate);
}

function initializePage() {
  const container = document.getElementById('sku-list');
  const skuCount = document.getElementById('sku-count');

  // 显示加载状态
  container.innerHTML = '<div class="col-span-3 py-12 text-center text-gray-500"><div class="mb-2">⏳</div><div>正在加载当天售罄SKU数据...</div></div>';
  skuCount.textContent = '正在加载...';

  // 自动加载当天数据
  const today = new Date().toISOString().split('T')[0];
  loadAlertData(today, today);
}

// 检测浏览器是否支持 type="date"
function checkDateInputSupport() {
  const input = document.createElement('input');
  input.type = 'date';
  input.value = '不是日期';
  return input.type === 'date' && input.value !== '不是日期';
}

// 为不支持的浏览器提供后备方案
function setupDateInputFallback() {
  if (!checkDateInputSupport()) {
    const dateInputs = document.querySelectorAll('input[type="date"]');
    dateInputs.forEach(input => {
      input.type = 'text';
      input.placeholder = 'YYYY-MM-DD';
      input.pattern = '\\d{4}-\\d{2}-\\d{2}';
      
      // 添加输入验证
      input.addEventListener('input', function(e) {
        const value = e.target.value;
        const regex = /^\d{4}-\d{2}-\d{2}$/;
        if (value && !regex.test(value)) {
          e.target.setCustomValidity('请输入正确的日期格式 (YYYY-MM-DD)');
        } else {
          e.target.setCustomValidity('');
        }
      });
    });
    
    console.warn('浏览器不支持 type="date"，已启用文本输入后备方案');
  }
}





document.addEventListener('DOMContentLoaded', function() {
  console.log('🚀 页面加载完成，初始化库存提醒页面...');

  // 设置日期输入框兼容性
  setupDateInputFallback();

  setDefaultDates();
  initializePage();
  document.getElementById('search-btn').addEventListener('click', handleSearch);

  // 显示欢迎提示
  UI.showToast('库存提醒系统已启动', 'success', 3000);

  // 回车键触发查询
  document.getElementById('start-date').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') handleSearch();
  });
  document.getElementById('end-date').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') handleSearch();
  });

  // 添加调试信息
  console.log('日期输入框支持:', checkDateInputSupport() ? '是' : '否');
  console.log('默认日期已设置');
});
</script>
{% endblock %} 