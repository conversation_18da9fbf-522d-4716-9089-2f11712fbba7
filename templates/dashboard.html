{% extends "base.html" %}

{% block title %}KKUGUAN - 仪表板{% endblock %}

{% block content %}
<div class="content-wrapper">
    <!-- 快速统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div class="bg-white/80 shadow-xl rounded-3xl p-6 hover:shadow-2xl transition-all duration-300">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-500 mb-2">总SKU数</p>
                    <p class="text-3xl font-bold text-gray-900" id="total-sku">-</p>
                </div>
                <div class="bg-gradient-to-br from-blue-400 to-blue-600 rounded-2xl p-4">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                    </svg>
                </div>
            </div>
        </div>
        <div class="bg-white/80 shadow-xl rounded-3xl p-6 hover:shadow-2xl transition-all duration-300">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-500 mb-2">待补货SKU</p>
                    <p class="text-3xl font-bold text-gray-900" id="restock-sku">-</p>
                </div>
                <div class="bg-gradient-to-br from-yellow-400 to-orange-500 rounded-2xl p-4">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                    </svg>
                </div>
            </div>
        </div>
        <div class="bg-white/80 shadow-xl rounded-3xl p-6 hover:shadow-2xl transition-all duration-300">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-500 mb-2">圈货提醒</p>
                    <p class="text-3xl font-bold text-gray-900" id="alert-count">-</p>
                </div>
                <div class="bg-gradient-to-br from-red-400 to-red-600 rounded-2xl p-4">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM4 19h6v-2H4v2zM4 15h8v-2H4v2zM4 11h10V9H4v2z"></path>
                    </svg>
                </div>
            </div>
        </div>
    </div>

    <!-- 圈货提醒卡片区域 -->
    <div class="bg-white/80 shadow-xl rounded-3xl p-8">
        <div class="flex items-center justify-between mb-6">
            <div>
                <h2 class="text-2xl font-bold text-gray-900">圈货提醒</h2>
                <p class="text-gray-600 mt-1">按预计可售天数排序，优先显示紧急库存</p>
                <!-- 缓存状态指示器 -->
                <div id="cache-status" class="mt-2 text-xs text-gray-400 flex items-center gap-2">
                    <span id="cache-indicator" class="w-2 h-2 bg-gray-300 rounded-full"></span>
                    <span id="cache-text">检查缓存状态...</span>
                </div>
            </div>
            <div class="flex items-center gap-2 text-sm text-gray-500">
                <div class="w-3 h-3 bg-red-500 rounded-full"></div>
                <span>≤7天</span>
                <div class="w-3 h-3 bg-orange-500 rounded-full ml-3"></div>
                <span>8-15天</span>
                <div class="w-3 h-3 bg-green-500 rounded-full ml-3"></div>
                <span>16-60天</span>
                <!-- 手动刷新按钮 -->
                <button id="refresh-alerts" class="ml-4 px-3 py-1 text-xs bg-blue-500 text-white rounded hover:bg-blue-600 transition">
                    🔄 刷新
                </button>
            </div>
        </div>
        <div id="alerts-container" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <!-- 圈货提醒卡片将在这里动态加载 -->
            <div class="text-center py-12 text-gray-500 col-span-full">
                <div class="animate-spin w-8 h-8 border-4 border-blue-200 border-t-blue-600 rounded-full mx-auto mb-4"></div>
                <p class="text-lg">正在加载圈货提醒...</p>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript代码 -->
<script>
    // 更新当前时间
    function updateCurrentTime() {
        const now = new Date();
        const options = {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: false
        };
        const timeElement = document.getElementById('current-time');
        if (timeElement) {
            timeElement.textContent = now.toLocaleString('zh-CN', options);
        }
    }

    // 每秒更新时间
    setInterval(updateCurrentTime, 1000);
    updateCurrentTime();

    // 更新统计数据
    async function updateStats() {
        try {
            const response = await fetch('/api/stats');
            if (!response.ok) throw new Error('获取统计数据失败');
            const data = await response.json();

            // 添加动画效果
            const updateElement = (id, value) => {
                const element = document.getElementById(id);
                if (element) {
                    element.style.transform = 'scale(1.1)';
                    element.textContent = value || '-';
                    setTimeout(() => {
                        element.style.transform = 'scale(1)';
                    }, 200);
                }
            };

            updateElement('total-sku', data.total_skus);
            updateElement('restock-sku', data.pending_restock);
            updateElement('alert-count', data.alerts_count);

        } catch (error) {
            console.error('更新统计数据失败:', error);
            // 显示错误状态
            ['total-sku', 'restock-sku', 'alert-count'].forEach(id => {
                const element = document.getElementById(id);
                if (element) element.textContent = '错误';
            });
        }
    }

    // 更新缓存状态显示
    async function updateCacheStatus() {
        try {
            const response = await fetch('/api/cache_info');
            if (!response.ok) throw new Error('获取缓存信息失败');
            const cacheInfo = await response.json();

            const indicator = document.getElementById('cache-indicator');
            const text = document.getElementById('cache-text');

            if (cacheInfo.dashboard_alerts_cache) {
                const cache = cacheInfo.dashboard_alerts_cache;
                const mainCache = cache.main_cache;
                const backupCache = cache.backup_cache;

                if (mainCache.exists && !mainCache.expired) {
                    // 主缓存有效
                    indicator.className = 'w-2 h-2 bg-green-500 rounded-full animate-pulse';
                    text.textContent = `缓存有效 (剩余 ${Math.floor(mainCache.remaining_seconds / 60)}分钟)`;
                } else if (backupCache.exists && !backupCache.expired) {
                    // 备用缓存有效
                    indicator.className = 'w-2 h-2 bg-yellow-500 rounded-full animate-pulse';
                    text.textContent = `备用缓存 (剩余 ${Math.floor(backupCache.remaining_seconds / 60)}分钟)`;
                } else {
                    // 无缓存
                    indicator.className = 'w-2 h-2 bg-red-500 rounded-full animate-pulse';
                    text.textContent = '无缓存，实时查询';
                }
            }
        } catch (error) {
            console.error('更新缓存状态失败:', error);
            const indicator = document.getElementById('cache-indicator');
            const text = document.getElementById('cache-text');
            indicator.className = 'w-2 h-2 bg-gray-500 rounded-full';
            text.textContent = '缓存状态未知';
        }
    }

    // 加载圈货提醒卡片
    async function loadAlerts(forceRefresh = false) {
        try {
            // 如果强制刷新，先清除缓存
            if (forceRefresh) {
                await fetch('/api/cache_clear?cache_key=dashboard_alerts');
                console.log('🔄 已清除仪表盘库存提醒缓存');
            }

            const startTime = performance.now();
            const response = await fetch('/api/dashboard_alerts');
            if (!response.ok) throw new Error('获取圈货提醒失败');
            const alerts = await response.json();
            const loadTime = performance.now() - startTime;

            console.log(`📊 库存提醒加载完成，耗时: ${loadTime.toFixed(1)}ms，数据量: ${alerts.length} 条`);

            // 更新缓存状态
            await updateCacheStatus();

            const container = document.getElementById('alerts-container');

            if (alerts.length === 0) {
                container.innerHTML = `
                    <div class="col-span-full text-center py-12">
                        <div class="bg-green-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">暂无圈货提醒</h3>
                        <p class="text-gray-600">所有SKU库存状态良好</p>
                    </div>
                `;
                return;
            }

            container.innerHTML = alerts.map(alert => {
                // 根据预计可售天数确定颜色
                let borderColor, bgColor, urgencyColor;
                if (alert.estimated_days <= 7) {
                    borderColor = 'border-red-200';
                    bgColor = 'from-red-50 to-red-100';
                    urgencyColor = 'text-red-600';
                } else if (alert.estimated_days <= 15) {
                    borderColor = 'border-orange-200';
                    bgColor = 'from-orange-50 to-orange-100';
                    urgencyColor = 'text-orange-600';
                } else {
                    borderColor = 'border-green-200';
                    bgColor = 'from-green-50 to-green-100';
                    urgencyColor = 'text-green-600';
                }

                return `
                    <div class="bg-gradient-to-br ${bgColor} border ${borderColor} rounded-2xl p-6 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                        <!-- SKU和紧急程度 -->
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-xl font-bold text-gray-900">${alert.SKU}</h3>
                            <div class="flex items-center gap-2">
                                <div class="w-3 h-3 ${alert.estimated_days <= 7 ? 'bg-red-500' : alert.estimated_days <= 15 ? 'bg-orange-500' : 'bg-green-500'} rounded-full"></div>
                                <span class="text-xs font-medium ${urgencyColor}">
                                    ${alert.estimated_days <= 7 ? '紧急' : alert.estimated_days <= 15 ? '警告' : '正常'}
                                </span>
                            </div>
                        </div>

                        <!-- 产品图片 -->
                        ${alert.产品主图 ? `
                            <div class="mb-4 flex justify-center">
                                <img src="${alert.产品主图}" alt="${alert.SKU}"
                                     class="w-32 h-32 object-cover rounded-xl shadow-sm"
                                     onerror="this.style.display='none'">
                            </div>
                        ` : ''}

                        <!-- 关键信息网格 -->
                        <div class="grid grid-cols-2 gap-3 mb-4">
                            <div class="bg-white/60 rounded-lg p-3 text-center">
                                <div class="text-xs text-gray-500 mb-1">近30天销量</div>
                                <div class="font-bold text-blue-600">${alert.近30天销量}</div>
                            </div>
                            <div class="bg-white/60 rounded-lg p-3 text-center">
                                <div class="text-xs text-gray-500 mb-1">近60天销量</div>
                                <div class="font-bold text-purple-600">${alert.近60天销量}</div>
                            </div>
                            <div class="bg-white/60 rounded-lg p-3 text-center">
                                <div class="text-xs text-gray-500 mb-1">公共库存</div>
                                <div class="font-bold text-green-600">${alert.公共库存}</div>
                            </div>
                            <div class="bg-white/60 rounded-lg p-3 text-center">
                                <div class="text-xs text-gray-500 mb-1">总库存</div>
                                <div class="font-bold text-gray-900">${alert.总库存}</div>
                            </div>
                        </div>

                        <!-- 详细信息 -->
                        <div class="space-y-3">
                            ${alert.预计到货时间 ? `
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-gray-600">预计到货</span>
                                    <span class="font-medium text-green-600">${alert.预计到货时间}</span>
                                </div>
                            ` : ''}

                            ${alert.预计到货数量 ? `
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-gray-600">到货数量</span>
                                    <span class="font-medium text-green-600">${alert.预计到货数量}</span>
                                </div>
                            ` : ''}
                        </div>

                        <!-- 预计可售天数 - 突出显示 -->
                        <div class="mt-4 pt-4 border-t border-white/50">
                            <div class="bg-white/80 rounded-xl p-3 text-center">
                                <div class="text-xs text-gray-500 mb-1">预计可售天数</div>
                                <div class="text-2xl font-bold ${urgencyColor}">
                                    ${alert.estimated_days === Infinity ? '♾️' : alert.estimated_days + '天'}
                                </div>
                            </div>
                        </div>

                        <!-- 供应商信息 -->
                        ${alert.supplier_code ? `
                            <div class="mt-3 text-center">
                                <span class="inline-block bg-white/60 text-xs text-gray-600 px-2 py-1 rounded-full">
                                    供应商: ${alert.supplier_code}
                                </span>
                            </div>
                        ` : ''}
                    </div>
                `;
            }).join('');

        } catch (error) {
            console.error('加载圈货提醒失败:', error);
            document.getElementById('alerts-container').innerHTML = `
                <div class="col-span-full text-center py-12">
                    <div class="bg-red-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">加载失败</h3>
                    <p class="text-gray-600 mb-4">无法加载圈货提醒数据</p>
                    <button onclick="loadAlerts()" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition">
                        重试
                    </button>
                </div>
            `;
        }
    }

    // 手动刷新按钮事件
    document.getElementById('refresh-alerts').addEventListener('click', async () => {
        const button = document.getElementById('refresh-alerts');
        const originalText = button.textContent;

        try {
            button.textContent = '🔄 刷新中...';
            button.disabled = true;

            await loadAlerts(true); // 强制刷新

            button.textContent = '✅ 已刷新';
            setTimeout(() => {
                button.textContent = originalText;
            }, 2000);
        } catch (error) {
            button.textContent = '❌ 失败';
            setTimeout(() => {
                button.textContent = originalText;
            }, 2000);
        } finally {
            button.disabled = false;
        }
    });

    // 初始加载数据
    updateStats();
    loadAlerts();
    updateCacheStatus();

    // 定期刷新数据（每5分钟）
    setInterval(() => {
        updateStats();
        loadAlerts();
        updateCacheStatus();
    }, 300000);

    // 每30秒更新缓存状态
    setInterval(updateCacheStatus, 30000);
</script>
{% endblock %}