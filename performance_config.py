"""
KKUGUAN 性能优化配置
用于提高应用运行速度和响应性能
"""

import os
from datetime import timedelta

class PerformanceConfig:
    """性能优化配置类"""
    
    # Redis缓存配置
    CACHE_CONFIG = {
        'CACHE_TYPE': 'redis',
        'CACHE_REDIS_HOST': os.getenv('REDIS_HOST', 'localhost'),
        'CACHE_REDIS_PORT': int(os.getenv('REDIS_PORT', 6379)),
        'CACHE_REDIS_DB': int(os.getenv('REDIS_DB', 0)),
        'CACHE_DEFAULT_TIMEOUT': 300,  # 5分钟默认缓存
        'CACHE_KEY_PREFIX': 'kkuguan_',
    }
    
    # 数据库连接池配置
    DB_POOL_CONFIG = {
        'pool_size': 20,           # 连接池大小
        'max_overflow': 30,        # 最大溢出连接
        'pool_timeout': 30,        # 连接超时
        'pool_recycle': 3600,      # 连接回收时间(1小时)
        'pool_pre_ping': True,     # 连接前ping检查
    }
    
    # 缓存策略配置
    CACHE_TIMEOUTS = {
        'dashboard_summary': 300,      # 5分钟
        'sku_list': 600,              # 10分钟
        'restock_calendar': 1800,     # 30分钟
        'tracking_data': 300,         # 5分钟
        'inventory_alerts': 180,      # 3分钟
        'sales_data': 900,            # 15分钟
    }
    
    # 分页配置
    PAGINATION_CONFIG = {
        'default_page_size': 50,
        'max_page_size': 200,
        'sku_detail_page_size': 100,
    }
    
    # 图片优化配置
    IMAGE_CONFIG = {
        'default_width': 200,
        'default_height': 200,
        'default_quality': 60,
        'cache_timeout': 86400,  # 24小时
    }
    
    # API限流配置
    RATE_LIMIT_CONFIG = {
        'default': '100 per minute',
        'api_heavy': '20 per minute',
        'api_light': '200 per minute',
    }
    
    # 压缩配置
    COMPRESSION_CONFIG = {
        'COMPRESS_MIMETYPES': [
            'text/html',
            'text/css',
            'text/xml',
            'application/json',
            'application/javascript',
            'text/javascript',
        ],
        'COMPRESS_LEVEL': 6,
        'COMPRESS_MIN_SIZE': 500,
    }

# 性能监控配置
PERFORMANCE_MONITORING = {
    'slow_query_threshold': 2.0,  # 慢查询阈值(秒)
    'memory_warning_threshold': 80,  # 内存警告阈值(%)
    'cpu_warning_threshold': 80,     # CPU警告阈值(%)
}

# 预热配置
WARMUP_CONFIG = {
    'enabled': True,
    'endpoints': [
        '/api/dashboard_summary',
        '/api/sku_list',
        '/api/restock_calendar',
    ],
    'delay_between_requests': 0.5,  # 请求间隔(秒)
}
