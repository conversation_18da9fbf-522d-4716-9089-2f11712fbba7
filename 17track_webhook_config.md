# 17track Webhook 配置指南

## 🔗 Webhook URL 配置

### 生产环境 URL
```
http://*************:5002/webhook/17track
```

### 测试环境 URL
```
http://*************:5002/webhook/17track/test
```

## 📋 17track 后台配置步骤

### 1. 登录17track开发者后台
- 访问：https://www.17track.net/zh-cn/apikey
- 使用你的17track账号登录

### 2. 配置Webhook
1. 进入 **API管理** → **Webhook设置**
2. 添加新的Webhook URL：`http://*************:5002/webhook/17track`
3. 选择推送事件：
   - ✅ **状态更新** (Status Update)
   - ✅ **新事件** (New Event)
   - ✅ **包裹送达** (Delivered)
4. 设置推送格式：**JSON**
5. 启用Webhook

### 3. Webhook 数据格式
17track会推送以下格式的数据：
```json
{
  "data": [
    {
      "number": "**********",
      "carrier": "ups",
      "track_info": {
        "latest_status": {
          "status": "In Transit"
        },
        "latest_event": {
          "description": "Package is in transit",
          "location": "New York, NY",
          "time_iso": "2024-01-15T10:30:00Z"
        }
      }
    }
  ]
}
```

## 🧪 测试Webhook

### 1. 测试连通性
```bash
curl -X GET http://*************:5002/webhook/17track/test
```

### 2. 测试数据处理
```bash
curl -X POST http://*************:5002/webhook/17track/test
```

### 3. 手动测试Webhook
```bash
curl -X POST http://*************:5002/webhook/17track \
  -H "Content-Type: application/json" \
  -d '{
    "data": [
      {
        "number": "TEST123456789",
        "carrier": "ups",
        "track_info": {
          "latest_status": {
            "status": "Delivered"
          },
          "latest_event": {
            "description": "Package delivered",
            "location": "Customer Address",
            "time_iso": "2024-01-15T15:30:00Z"
          }
        }
      }
    ]
  }'
```

## 🔧 服务器配置要求

### 1. 防火墙设置
确保服务器的5002端口对外开放：
```bash
# 阿里云安全组规则
# 添加入方向规则：
# 协议类型: TCP
# 端口范围: 5002
# 授权对象: 0.0.0.0/0
```

### 2. Nginx配置（可选）
如果使用Nginx反向代理：
```nginx
server {
    listen 80;
    server_name *************;
    
    location /webhook/ {
        proxy_pass http://localhost:5002;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## 📊 监控和日志

### 1. 查看Webhook日志
```bash
# 查看应用日志
tail -f app.log | grep "17track webhook"

# 或者查看实时日志
docker logs -f kkuguan-app | grep "17track webhook"
```

### 2. 常见日志信息
```
✅ 17track webhook: 更新物流信息 ********** -> Delivered
❌ 17track webhook: 处理单个更新失败 **********: error message
🧪 测试17track webhook处理
```

## 🚨 故障排除

### 1. Webhook无法接收数据
- 检查服务器防火墙设置
- 确认应用正在运行在5002端口
- 验证URL是否正确配置

### 2. 数据处理失败
- 查看应用日志获取详细错误信息
- 检查数据库连接是否正常
- 验证17track推送的数据格式

### 3. 测试命令
```bash
# 检查端口是否开放
telnet ************* 5002

# 检查应用状态
curl -X GET http://*************:5002/health

# 测试webhook端点
curl -X GET http://*************:5002/webhook/17track/test
```

## 📈 性能优化

### 1. 批量处理
Webhook端点支持批量处理多个跟踪更新

### 2. 异步处理
对于大量数据，考虑使用异步处理机制

### 3. 缓存策略
更新后自动清理相关缓存，确保数据一致性

## 🔐 安全考虑

### 1. IP白名单（推荐）
在17track后台配置IP白名单，只允许特定IP访问

### 2. 签名验证（高级）
如果17track支持，可以添加签名验证机制

### 3. HTTPS（生产环境推荐）
生产环境建议使用HTTPS：
```
https://*************/webhook/17track
```
