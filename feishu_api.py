"""
飞书数据表API集成模块
用于获取店铺SKU映射信息
"""

import requests
import logging
import time
from typing import Dict, List, Optional

logger = logging.getLogger(__name__)

# 简单的内存缓存
_feishu_cache = {}
_cache_timestamps = {}
CACHE_TTL = 1800  # 30分钟缓存

class FeishuAPI:
    """飞书API客户端"""

    def __init__(self, app_id: str, app_secret: str, spreadsheet_token: str, table_type: str = "sheets"):
        self.app_id = app_id
        self.app_secret = app_secret
        self.spreadsheet_token = spreadsheet_token
        self.table_type = table_type  # "sheets" 或 "bitable"
        self.access_token = None
        self.token_expires_at = 0
        self.base_url = "https://open.feishu.cn/open-apis"
        
    def get_access_token(self) -> str:
        """获取访问令牌"""
        if self.access_token and time.time() < self.token_expires_at:
            return self.access_token
            
        url = f"{self.base_url}/auth/v3/tenant_access_token/internal"
        payload = {
            "app_id": self.app_id,
            "app_secret": self.app_secret
        }
        
        try:
            response = requests.post(url, json=payload)
            response.raise_for_status()
            data = response.json()
            
            if data.get("code") == 0:
                self.access_token = data["tenant_access_token"]
                self.token_expires_at = time.time() + data["expire"] - 300  # 提前5分钟刷新
                logger.info("飞书访问令牌获取成功")
                return self.access_token
            else:
                logger.error(f"获取飞书访问令牌失败: {data}")
                return None
                
        except Exception as e:
            logger.error(f"获取飞书访问令牌异常: {e}")
            return None
    
    def get_table_sheets(self) -> List[Dict]:
        """获取表格的所有工作表 - 缓存版本"""
        cache_key = f"sheets_{self.spreadsheet_token}_{self.table_type}"

        # 检查缓存
        if cache_key in _feishu_cache:
            if time.time() - _cache_timestamps.get(cache_key, 0) < CACHE_TTL:
                logger.info(f"飞书工作表缓存命中: {cache_key}")
                return _feishu_cache[cache_key]

        token = self.get_access_token()
        if not token:
            return []

        if self.table_type == "sheets":
            # 普通表格API
            url = f"{self.base_url}/sheets/v3/spreadsheets/{self.spreadsheet_token}/sheets/query"
        else:
            # 多维表格API
            url = f"{self.base_url}/bitable/v1/apps/{self.spreadsheet_token}/tables"

        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }

        try:
            response = requests.get(url, headers=headers)
            response.raise_for_status()
            data = response.json()

            if data.get("code") == 0:
                if self.table_type == "sheets":
                    sheets = data.get("data", {}).get("sheets", [])
                    # 转换为统一格式
                    formatted_sheets = []
                    for sheet in sheets:
                        formatted_sheets.append({
                            "sheet_id": sheet.get("sheet_id"),
                            "name": sheet.get("title", ""),
                            "index": sheet.get("index", 0)
                        })
                    sheets = formatted_sheets
                else:
                    sheets = data.get("data", {}).get("items", [])

                logger.info(f"获取到 {len(sheets)} 个工作表")

                # 缓存结果
                _feishu_cache[cache_key] = sheets
                _cache_timestamps[cache_key] = time.time()
                logger.info(f"飞书工作表数据已缓存: {cache_key}")

                return sheets
            else:
                logger.error(f"获取工作表列表失败: {data}")
                return []

        except Exception as e:
            logger.error(f"获取工作表列表异常: {e}")
            return []
    
    def get_sheet_records(self, sheet_id: str) -> List[Dict]:
        """获取指定工作表的所有记录 - 缓存版本"""
        cache_key = f"records_{self.spreadsheet_token}_{sheet_id}_{self.table_type}"

        # 检查缓存
        if cache_key in _feishu_cache:
            if time.time() - _cache_timestamps.get(cache_key, 0) < CACHE_TTL:
                logger.info(f"飞书记录缓存命中: {sheet_id}")
                return _feishu_cache[cache_key]

        token = self.get_access_token()
        if not token:
            return []

        if self.table_type == "sheets":
            # 普通表格API - 获取工作表数据，使用更大的范围以支持更多数据
            url = f"{self.base_url}/sheets/v2/spreadsheets/{self.spreadsheet_token}/values/{sheet_id}!A1:Z10000"
        else:
            # 多维表格API
            url = f"{self.base_url}/bitable/v1/apps/{self.spreadsheet_token}/tables/{sheet_id}/records"

        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }

        try:
            if self.table_type == "sheets":
                # 普通表格：获取所有数据
                response = requests.get(url, headers=headers)
                response.raise_for_status()
                data = response.json()

                if data.get("code") == 0:
                    value_range = data.get("data", {}).get("valueRange", {})
                    values = value_range.get("values", [])
                    if not values:
                        return []

                    # 调试信息：记录原始数据量
                    logger.debug(f"工作表 {sheet_id} 原始数据行数: {len(values)} (包括表头)")

                    # 第一行作为表头
                    headers_row = values[0] if values else []
                    records = []

                    # 转换为字典格式
                    for row in values[1:]:  # 跳过表头
                        if not row:  # 跳过空行
                            continue
                        record = {}
                        for i, cell_value in enumerate(row):
                            if i < len(headers_row) and headers_row[i]:
                                header = headers_row[i]
                                # 处理复杂的单元格值（如链接）
                                if isinstance(cell_value, list) and cell_value:
                                    if isinstance(cell_value[0], dict) and 'text' in cell_value[0]:
                                        cell_value = cell_value[0]['text']
                                    else:
                                        cell_value = str(cell_value[0])
                                elif cell_value is None:
                                    cell_value = ""
                                record[header] = cell_value
                        if record:  # 只添加非空记录
                            records.append(record)

                    # 如果记录数恰好是999，可能遇到了API限制
                    if len(records) == 999:
                        logger.warning(f"⚠️ 工作表 {sheet_id} 返回999条记录，可能遇到API限制，原始行数: {len(values)}")

                    logger.info(f"从工作表 {sheet_id} 获取到 {len(records)} 条记录")

                    # 缓存结果
                    _feishu_cache[cache_key] = records
                    _cache_timestamps[cache_key] = time.time()
                    logger.info(f"飞书记录数据已缓存: {sheet_id}")

                    return records
                else:
                    logger.error(f"获取工作表数据失败: {data}")
                    return []
            else:
                # 多维表格：分页获取
                all_records = []
                page_token = None

                while True:
                    params = {"page_size": 500}
                    if page_token:
                        params["page_token"] = page_token

                    response = requests.get(url, headers=headers, params=params)
                    response.raise_for_status()
                    data = response.json()

                    if data.get("code") == 0:
                        records = data.get("data", {}).get("items", [])
                        all_records.extend(records)

                        # 检查是否有下一页
                        page_token = data.get("data", {}).get("page_token")
                        if not page_token:
                            break
                    else:
                        logger.error(f"获取多维表格记录失败: {data}")
                        break

                logger.info(f"从多维表格 {sheet_id} 获取到 {len(all_records)} 条记录")

                # 缓存结果
                _feishu_cache[cache_key] = all_records
                _cache_timestamps[cache_key] = time.time()
                logger.info(f"飞书多维表格数据已缓存: {sheet_id}")

                return all_records

        except Exception as e:
            logger.error(f"获取工作表记录异常: {e}")
            return []
    
    def get_sku_mapping(self) -> Dict[str, List[Dict]]:
        """
        获取SKU映射关系
        返回格式: {云仓编码: [{'shop_name': '店铺名', 'shop_sku': '店铺SKU'}, ...]}
        """
        sku_mapping = {}

        # 获取所有工作表
        sheets = self.get_table_sheets()
        if not sheets:
            logger.warning("未获取到任何工作表")
            return sku_mapping

        for sheet in sheets:
            sheet_name = sheet.get("name", "")
            sheet_id = sheet.get("sheet_id", "") if self.table_type == "sheets" else sheet.get("table_id", "")

            if not sheet_id:
                continue

            logger.info(f"处理工作表: {sheet_name}")

            # 获取工作表中的记录
            records = self.get_sheet_records(sheet_id)

            for record in records:
                if self.table_type == "sheets":
                    # 普通表格：record 直接是字典
                    fields = record
                else:
                    # 多维表格：record 有 fields 字段
                    fields = record.get("fields", {})

                # 提取SKU编码和云仓编码
                shop_sku = None
                warehouse_sku = None

                # 查找SKU编码字段（可能的字段名）
                for field_name, field_value in fields.items():
                    if not field_name or not field_value:
                        continue

                    field_name_lower = str(field_name).lower()

                    if ("sku编码" in field_name_lower or
                        "sku" in field_name_lower and "编码" in field_name_lower or
                        field_name in ["SKU编码", "店铺SKU", "商品SKU"]):

                        if isinstance(field_value, list) and field_value:
                            shop_sku = str(field_value[0].get("text", "")).strip()
                        elif isinstance(field_value, str):
                            shop_sku = str(field_value).strip()

                    elif ("云仓编码" in field_name_lower or
                          "云仓sku" in field_name_lower or
                          "仓库编码" in field_name_lower or
                          field_name in ["云仓编码", "云仓SKU", "仓库SKU"]):

                        if isinstance(field_value, list) and field_value:
                            warehouse_sku = str(field_value[0].get("text", "")).strip()
                        elif isinstance(field_value, str):
                            warehouse_sku = str(field_value).strip()

                # 如果找到了有效的映射关系
                if shop_sku and warehouse_sku and shop_sku != "" and warehouse_sku != "":
                    if warehouse_sku not in sku_mapping:
                        sku_mapping[warehouse_sku] = []

                    # 避免重复添加
                    existing = any(
                        item["shop_name"] == sheet_name and item["shop_sku"] == shop_sku
                        for item in sku_mapping[warehouse_sku]
                    )

                    if not existing:
                        sku_mapping[warehouse_sku].append({
                            "shop_name": sheet_name,
                            "shop_sku": shop_sku
                        })

        logger.info(f"SKU映射构建完成，共 {len(sku_mapping)} 个云仓SKU有店铺映射")
        return sku_mapping


# 全局飞书API实例（需要配置）
feishu_client = None

def init_feishu_client(app_id: str, app_secret: str, spreadsheet_token: str, table_type: str = "sheets"):
    """初始化飞书客户端"""
    global feishu_client
    feishu_client = FeishuAPI(app_id, app_secret, spreadsheet_token, table_type)
    logger.info(f"飞书API客户端已初始化（{table_type}模式）")

def get_sku_shop_mapping() -> Dict[str, List[Dict]]:
    """获取SKU店铺映射关系"""
    if not feishu_client:
        logger.warning("飞书客户端未初始化")
        return {}

    try:
        return feishu_client.get_sku_mapping()
    except Exception as e:
        logger.error(f"获取SKU映射失败: {e}")
        return {}

def get_platform_store_mapping() -> Dict[str, List[str]]:
    """获取平台店铺映射关系"""
    if not feishu_client:
        logger.warning("飞书客户端未初始化")
        return {}

    try:
        # 获取所有工作表
        sheets = feishu_client.get_table_sheets()
        if not sheets:
            logger.warning("未获取到任何工作表")
            return {}

        # 查找平台映射Sheet（查找"平台_店铺对应表"）
        platform_sheet = None
        for sheet in sheets:
            sheet_name = sheet.get("name", "")
            if "平台_店铺对应表" in sheet_name or "平台店铺对应表" in sheet_name:
                platform_sheet = sheet
                break

        # 如果没找到精确名称，再尝试模糊匹配
        if not platform_sheet:
            for sheet in sheets:
                sheet_name = sheet.get("name", "").lower()
                if "平台" in sheet_name and ("店铺" in sheet_name or "对应" in sheet_name):
                    platform_sheet = sheet
                    break

        if not platform_sheet:
            logger.warning("未找到平台映射工作表")
            return {}

        sheet_id = platform_sheet.get("sheet_id", "") if feishu_client.table_type == "sheets" else platform_sheet.get("table_id", "")
        if not sheet_id:
            logger.warning("平台映射工作表ID为空")
            return {}

        # 获取工作表数据
        records = feishu_client.get_sheet_records(sheet_id)
        if not records:
            logger.warning("平台映射工作表无数据")
            return {}

        # 解析平台店铺映射
        platform_mapping = {}

        if records:
            # 初始化平台映射
            platform_mapping = {
                'Amazon': [],
                'eBay': [],
                'Walmart': []
            }

            # 获取字段名列表（这些应该对应表格的列）
            field_names = list(records[0].keys()) if records else []

            # 根据字段名或位置映射到平台
            # 假设前三列分别对应Amazon、eBay、Walmart
            platform_field_mapping = {}

            if len(field_names) >= 3:
                # 尝试根据字段名匹配平台
                for field_name in field_names:
                    field_lower = str(field_name).lower()
                    if 'amazon' in field_lower:
                        platform_field_mapping['Amazon'] = field_name
                    elif 'ebay' in field_lower:
                        platform_field_mapping['eBay'] = field_name
                    elif 'walmart' in field_lower:
                        platform_field_mapping['Walmart'] = field_name

                # 如果没有找到明确的字段名匹配，使用位置映射
                if len(platform_field_mapping) < 3:
                    platform_field_mapping = {
                        'Amazon': field_names[0],
                        'eBay': field_names[1] if len(field_names) > 1 else None,
                        'Walmart': field_names[2] if len(field_names) > 2 else None
                    }

            # 处理所有记录，提取店铺数据
            for record in records:
                for platform, field_name in platform_field_mapping.items():
                    if field_name:
                        store_name = record.get(field_name, "")
                        if store_name and str(store_name).strip():
                            store_value = str(store_name).strip()
                            # 跳过明显的表头数据和无效数据
                            if (store_value.lower() not in ['amazon', 'ebay', 'walmart'] and
                                store_value not in ['1', '2', '3'] and
                                len(store_value) > 1):
                                platform_mapping[platform].append(store_value)

        logger.info(f"平台店铺映射构建完成: {platform_mapping}")
        return platform_mapping

    except Exception as e:
        logger.error(f"获取平台店铺映射失败: {e}")
        return {}
